{"name": "tucsenberg-website", "version": "1.0.0", "description": "<PERSON><PERSON>enberg防洪设备企业展示平台", "private": true, "scripts": {"dev": "next dev", "build": "contentlayer build && next build", "start": "next start", "lint": "next lint", "lint:custom": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix", "type-check": "tsc --noEmit", "contentlayer": "contentlayer build", "clean": "rm -rf .next .contentlayer", "export": "next export"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "clsx": "^2.0.0", "contentlayer": "^0.3.4", "date-fns": "^3.0.6", "gray-matter": "^4.0.3", "i18next": "^23.7.16", "lucide-react": "^0.303.0", "next": "^14.0.4", "next-contentlayer": "^0.3.4", "next-i18next": "^15.2.0", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-i18next": "^13.5.0", "rehype-autolink-headings": "^7.1.0", "rehype-highlight": "^7.0.0", "rehype-slug": "^6.0.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "resend": "^3.2.0", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.4.0", "typescript": "^5.3.3", "zod": "^3.22.4"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "eslint-plugin-react-you-might-not-need-an-effect": "^0.1.5", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9"}, "engines": {"node": ">=20.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.1", "keywords": ["防洪设备", "企业网站", "Next.js", "TypeScript", "多语言", "<PERSON><PERSON><PERSON>"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tucsenberg/website.git"}, "homepage": "https://tucsenberg.com"}