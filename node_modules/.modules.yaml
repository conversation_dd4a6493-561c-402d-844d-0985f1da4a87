hoistPattern:
  - '*'
hoistedDependencies:
  /@alloc/quick-lru/5.2.0:
    '@alloc/quick-lru': private
  /@babel/runtime/7.27.6:
    '@babel/runtime': private
  /@contentlayer/cli/0.3.4(esbuild@0.25.5):
    '@contentlayer/cli': private
  /@contentlayer/client/0.3.4(esbuild@0.25.5):
    '@contentlayer/client': private
  /@contentlayer/core/0.3.4(esbuild@0.25.5):
    '@contentlayer/core': private
  /@contentlayer/source-files/0.3.4(esbuild@0.25.5):
    '@contentlayer/source-files': private
  /@contentlayer/source-remote-files/0.3.4(esbuild@0.25.5):
    '@contentlayer/source-remote-files': private
  /@contentlayer/utils/0.3.4:
    '@contentlayer/utils': private
  /@effect-ts/core/0.60.5:
    '@effect-ts/core': private
  /@effect-ts/otel-exporter-trace-otlp-grpc/0.15.1(@effect-ts/core@0.60.5)(@opentelemetry/api@1.9.0)(@opentelemetry/core@1.30.1)(@opentelemetry/exporter-trace-otlp-grpc@0.39.1)(@opentelemetry/sdk-trace-base@1.30.1):
    '@effect-ts/otel-exporter-trace-otlp-grpc': private
  /@effect-ts/otel-sdk-trace-node/0.15.1(@effect-ts/core@0.60.5)(@opentelemetry/api@1.9.0)(@opentelemetry/core@1.30.1)(@opentelemetry/sdk-trace-base@1.30.1)(@opentelemetry/sdk-trace-node@1.30.1):
    '@effect-ts/otel-sdk-trace-node': private
  /@effect-ts/otel/0.15.1(@effect-ts/core@0.60.5)(@opentelemetry/api@1.9.0)(@opentelemetry/core@1.30.1)(@opentelemetry/sdk-trace-base@1.30.1):
    '@effect-ts/otel': private
  /@effect-ts/system/0.57.5:
    '@effect-ts/system': private
  /@esbuild-plugins/node-resolve/0.1.4(esbuild@0.25.5):
    '@esbuild-plugins/node-resolve': private
  /@esbuild/aix-ppc64/0.25.5:
    '@esbuild/aix-ppc64': private
  /@esbuild/android-arm/0.25.5:
    '@esbuild/android-arm': private
  /@esbuild/android-arm64/0.25.5:
    '@esbuild/android-arm64': private
  /@esbuild/android-x64/0.25.5:
    '@esbuild/android-x64': private
  /@esbuild/darwin-arm64/0.25.5:
    '@esbuild/darwin-arm64': private
  /@esbuild/darwin-x64/0.25.5:
    '@esbuild/darwin-x64': private
  /@esbuild/freebsd-arm64/0.25.5:
    '@esbuild/freebsd-arm64': private
  /@esbuild/freebsd-x64/0.25.5:
    '@esbuild/freebsd-x64': private
  /@esbuild/linux-arm/0.25.5:
    '@esbuild/linux-arm': private
  /@esbuild/linux-arm64/0.25.5:
    '@esbuild/linux-arm64': private
  /@esbuild/linux-ia32/0.25.5:
    '@esbuild/linux-ia32': private
  /@esbuild/linux-loong64/0.25.5:
    '@esbuild/linux-loong64': private
  /@esbuild/linux-mips64el/0.25.5:
    '@esbuild/linux-mips64el': private
  /@esbuild/linux-ppc64/0.25.5:
    '@esbuild/linux-ppc64': private
  /@esbuild/linux-riscv64/0.25.5:
    '@esbuild/linux-riscv64': private
  /@esbuild/linux-s390x/0.25.5:
    '@esbuild/linux-s390x': private
  /@esbuild/linux-x64/0.25.5:
    '@esbuild/linux-x64': private
  /@esbuild/netbsd-arm64/0.25.5:
    '@esbuild/netbsd-arm64': private
  /@esbuild/netbsd-x64/0.25.5:
    '@esbuild/netbsd-x64': private
  /@esbuild/openbsd-arm64/0.25.5:
    '@esbuild/openbsd-arm64': private
  /@esbuild/openbsd-x64/0.25.5:
    '@esbuild/openbsd-x64': private
  /@esbuild/sunos-x64/0.25.5:
    '@esbuild/sunos-x64': private
  /@esbuild/win32-arm64/0.25.5:
    '@esbuild/win32-arm64': private
  /@esbuild/win32-ia32/0.25.5:
    '@esbuild/win32-ia32': private
  /@esbuild/win32-x64/0.25.5:
    '@esbuild/win32-x64': private
  /@eslint-community/eslint-utils/4.7.0(eslint@8.57.1):
    '@eslint-community/eslint-utils': public
  /@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  /@eslint/eslintrc/2.1.4:
    '@eslint/eslintrc': public
  /@eslint/js/8.57.1:
    '@eslint/js': public
  /@fal-works/esbuild-plugin-global-externals/2.1.2:
    '@fal-works/esbuild-plugin-global-externals': private
  /@grpc/grpc-js/1.13.4:
    '@grpc/grpc-js': private
  /@grpc/proto-loader/0.7.15:
    '@grpc/proto-loader': private
  /@humanwhocodes/config-array/0.13.0:
    '@humanwhocodes/config-array': private
  /@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  /@humanwhocodes/object-schema/2.0.3:
    '@humanwhocodes/object-schema': private
  /@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  /@jridgewell/gen-mapping/0.3.8:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/set-array/1.2.1:
    '@jridgewell/set-array': private
  /@jridgewell/sourcemap-codec/1.5.0:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.25:
    '@jridgewell/trace-mapping': private
  /@js-sdsl/ordered-map/4.4.2:
    '@js-sdsl/ordered-map': private
  /@js-temporal/polyfill/0.4.4:
    '@js-temporal/polyfill': private
  /@mdx-js/esbuild/2.3.0(esbuild@0.25.5):
    '@mdx-js/esbuild': private
  /@mdx-js/mdx/2.3.0:
    '@mdx-js/mdx': private
  /@next/env/14.2.30:
    '@next/env': private
  /@next/eslint-plugin-next/14.2.30:
    '@next/eslint-plugin-next': public
  /@next/swc-darwin-arm64/14.2.30:
    '@next/swc-darwin-arm64': private
  /@next/swc-darwin-x64/14.2.30:
    '@next/swc-darwin-x64': private
  /@next/swc-linux-arm64-gnu/14.2.30:
    '@next/swc-linux-arm64-gnu': private
  /@next/swc-linux-arm64-musl/14.2.30:
    '@next/swc-linux-arm64-musl': private
  /@next/swc-linux-x64-gnu/14.2.30:
    '@next/swc-linux-x64-gnu': private
  /@next/swc-linux-x64-musl/14.2.30:
    '@next/swc-linux-x64-musl': private
  /@next/swc-win32-arm64-msvc/14.2.30:
    '@next/swc-win32-arm64-msvc': private
  /@next/swc-win32-ia32-msvc/14.2.30:
    '@next/swc-win32-ia32-msvc': private
  /@next/swc-win32-x64-msvc/14.2.30:
    '@next/swc-win32-x64-msvc': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@nolyfill/is-core-module/1.0.39:
    '@nolyfill/is-core-module': private
  /@one-ini/wasm/0.1.1:
    '@one-ini/wasm': private
  /@opentelemetry/api-logs/0.39.1:
    '@opentelemetry/api-logs': private
  /@opentelemetry/api/1.9.0:
    '@opentelemetry/api': private
  /@opentelemetry/context-async-hooks/1.30.1(@opentelemetry/api@1.9.0):
    '@opentelemetry/context-async-hooks': private
  /@opentelemetry/core/1.30.1(@opentelemetry/api@1.9.0):
    '@opentelemetry/core': private
  /@opentelemetry/exporter-trace-otlp-grpc/0.39.1(@opentelemetry/api@1.9.0):
    '@opentelemetry/exporter-trace-otlp-grpc': private
  /@opentelemetry/otlp-exporter-base/0.39.1(@opentelemetry/api@1.9.0):
    '@opentelemetry/otlp-exporter-base': private
  /@opentelemetry/otlp-grpc-exporter-base/0.39.1(@opentelemetry/api@1.9.0):
    '@opentelemetry/otlp-grpc-exporter-base': private
  /@opentelemetry/otlp-transformer/0.39.1(@opentelemetry/api@1.9.0):
    '@opentelemetry/otlp-transformer': private
  /@opentelemetry/propagator-b3/1.30.1(@opentelemetry/api@1.9.0):
    '@opentelemetry/propagator-b3': private
  /@opentelemetry/propagator-jaeger/1.30.1(@opentelemetry/api@1.9.0):
    '@opentelemetry/propagator-jaeger': private
  /@opentelemetry/resources/1.30.1(@opentelemetry/api@1.9.0):
    '@opentelemetry/resources': private
  /@opentelemetry/sdk-logs/0.39.1(@opentelemetry/api-logs@0.39.1)(@opentelemetry/api@1.9.0):
    '@opentelemetry/sdk-logs': private
  /@opentelemetry/sdk-metrics/1.13.0(@opentelemetry/api@1.9.0):
    '@opentelemetry/sdk-metrics': private
  /@opentelemetry/sdk-trace-base/1.30.1(@opentelemetry/api@1.9.0):
    '@opentelemetry/sdk-trace-base': private
  /@opentelemetry/sdk-trace-node/1.30.1(@opentelemetry/api@1.9.0):
    '@opentelemetry/sdk-trace-node': private
  /@opentelemetry/semantic-conventions/1.34.0:
    '@opentelemetry/semantic-conventions': private
  /@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  /@protobufjs/aspromise/1.1.2:
    '@protobufjs/aspromise': private
  /@protobufjs/base64/1.1.2:
    '@protobufjs/base64': private
  /@protobufjs/codegen/2.0.4:
    '@protobufjs/codegen': private
  /@protobufjs/eventemitter/1.1.0:
    '@protobufjs/eventemitter': private
  /@protobufjs/fetch/1.1.0:
    '@protobufjs/fetch': private
  /@protobufjs/float/1.0.2:
    '@protobufjs/float': private
  /@protobufjs/inquire/1.1.0:
    '@protobufjs/inquire': private
  /@protobufjs/path/1.1.2:
    '@protobufjs/path': private
  /@protobufjs/pool/1.1.0:
    '@protobufjs/pool': private
  /@protobufjs/utf8/1.1.0:
    '@protobufjs/utf8': private
  /@react-email/render/0.0.16(react-dom@18.3.1)(react@18.3.1):
    '@react-email/render': private
  /@rtsao/scc/1.1.0:
    '@rtsao/scc': private
  /@rushstack/eslint-patch/1.11.0:
    '@rushstack/eslint-patch': public
  /@selderee/plugin-htmlparser2/0.11.0:
    '@selderee/plugin-htmlparser2': private
  /@swc/counter/0.1.3:
    '@swc/counter': private
  /@swc/helpers/0.5.5:
    '@swc/helpers': private
  /@types/acorn/4.0.6:
    '@types/acorn': private
  /@types/debug/4.1.12:
    '@types/debug': private
  /@types/estree-jsx/1.0.5:
    '@types/estree-jsx': private
  /@types/estree/1.0.8:
    '@types/estree': private
  /@types/hast/3.0.4:
    '@types/hast': private
  /@types/hoist-non-react-statics/3.3.6:
    '@types/hoist-non-react-statics': private
  /@types/json-schema/7.0.15:
    '@types/json-schema': private
  /@types/json5/0.0.29:
    '@types/json5': private
  /@types/mdast/4.0.4:
    '@types/mdast': private
  /@types/mdx/2.0.13:
    '@types/mdx': private
  /@types/ms/2.1.0:
    '@types/ms': private
  /@types/parse5/6.0.3:
    '@types/parse5': private
  /@types/prop-types/15.7.15:
    '@types/prop-types': private
  /@types/resolve/1.20.6:
    '@types/resolve': private
  /@types/semver/7.7.0:
    '@types/semver': private
  /@types/unist/3.0.3:
    '@types/unist': private
  /@typescript-eslint/scope-manager/6.21.0:
    '@typescript-eslint/scope-manager': public
  /@typescript-eslint/type-utils/6.21.0(eslint@8.57.1)(typescript@5.8.3):
    '@typescript-eslint/type-utils': public
  /@typescript-eslint/types/6.21.0:
    '@typescript-eslint/types': public
  /@typescript-eslint/typescript-estree/6.21.0(typescript@5.8.3):
    '@typescript-eslint/typescript-estree': public
  /@typescript-eslint/utils/6.21.0(eslint@8.57.1)(typescript@5.8.3):
    '@typescript-eslint/utils': public
  /@typescript-eslint/visitor-keys/6.21.0:
    '@typescript-eslint/visitor-keys': public
  /@ungap/structured-clone/1.3.0:
    '@ungap/structured-clone': private
  /@unrs/resolver-binding-android-arm-eabi/1.9.0:
    '@unrs/resolver-binding-android-arm-eabi': private
  /@unrs/resolver-binding-android-arm64/1.9.0:
    '@unrs/resolver-binding-android-arm64': private
  /@unrs/resolver-binding-darwin-arm64/1.9.0:
    '@unrs/resolver-binding-darwin-arm64': private
  /@unrs/resolver-binding-darwin-x64/1.9.0:
    '@unrs/resolver-binding-darwin-x64': private
  /@unrs/resolver-binding-freebsd-x64/1.9.0:
    '@unrs/resolver-binding-freebsd-x64': private
  /@unrs/resolver-binding-linux-arm-gnueabihf/1.9.0:
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  /@unrs/resolver-binding-linux-arm-musleabihf/1.9.0:
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  /@unrs/resolver-binding-linux-arm64-gnu/1.9.0:
    '@unrs/resolver-binding-linux-arm64-gnu': private
  /@unrs/resolver-binding-linux-arm64-musl/1.9.0:
    '@unrs/resolver-binding-linux-arm64-musl': private
  /@unrs/resolver-binding-linux-ppc64-gnu/1.9.0:
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  /@unrs/resolver-binding-linux-riscv64-gnu/1.9.0:
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  /@unrs/resolver-binding-linux-riscv64-musl/1.9.0:
    '@unrs/resolver-binding-linux-riscv64-musl': private
  /@unrs/resolver-binding-linux-s390x-gnu/1.9.0:
    '@unrs/resolver-binding-linux-s390x-gnu': private
  /@unrs/resolver-binding-linux-x64-gnu/1.9.0:
    '@unrs/resolver-binding-linux-x64-gnu': private
  /@unrs/resolver-binding-linux-x64-musl/1.9.0:
    '@unrs/resolver-binding-linux-x64-musl': private
  /@unrs/resolver-binding-wasm32-wasi/1.9.0:
    '@unrs/resolver-binding-wasm32-wasi': private
  /@unrs/resolver-binding-win32-arm64-msvc/1.9.0:
    '@unrs/resolver-binding-win32-arm64-msvc': private
  /@unrs/resolver-binding-win32-ia32-msvc/1.9.0:
    '@unrs/resolver-binding-win32-ia32-msvc': private
  /@unrs/resolver-binding-win32-x64-msvc/1.9.0:
    '@unrs/resolver-binding-win32-x64-msvc': private
  /abbrev/2.0.0:
    abbrev: private
  /acorn-jsx/5.3.2(acorn@8.15.0):
    acorn-jsx: private
  /acorn/8.15.0:
    acorn: private
  /ajv/6.12.6:
    ajv: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/4.3.0:
    ansi-styles: private
  /any-promise/1.3.0:
    any-promise: private
  /anymatch/3.1.3:
    anymatch: private
  /arg/5.0.2:
    arg: private
  /argparse/1.0.10:
    argparse: private
  /aria-query/5.3.2:
    aria-query: private
  /array-buffer-byte-length/1.0.2:
    array-buffer-byte-length: private
  /array-includes/3.1.9:
    array-includes: private
  /array-timsort/1.0.3:
    array-timsort: private
  /array-union/2.1.0:
    array-union: private
  /array.prototype.findlast/1.2.5:
    array.prototype.findlast: private
  /array.prototype.findlastindex/1.2.6:
    array.prototype.findlastindex: private
  /array.prototype.flat/1.3.3:
    array.prototype.flat: private
  /array.prototype.flatmap/1.3.3:
    array.prototype.flatmap: private
  /array.prototype.tosorted/1.1.4:
    array.prototype.tosorted: private
  /arraybuffer.prototype.slice/1.0.4:
    arraybuffer.prototype.slice: private
  /ast-types-flow/0.0.8:
    ast-types-flow: private
  /astring/1.9.0:
    astring: private
  /async-function/1.0.0:
    async-function: private
  /available-typed-arrays/1.0.7:
    available-typed-arrays: private
  /axe-core/4.10.3:
    axe-core: private
  /axobject-query/4.1.0:
    axobject-query: private
  /bail/2.0.2:
    bail: private
  /balanced-match/1.0.2:
    balanced-match: private
  /binary-extensions/2.3.0:
    binary-extensions: private
  /brace-expansion/1.1.12:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browserslist/4.25.0:
    browserslist: private
  /buffer-from/1.1.2:
    buffer-from: private
  /busboy/1.6.0:
    busboy: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bind/1.0.8:
    call-bind: private
  /call-bound/1.0.4:
    call-bound: private
  /callsites/3.1.0:
    callsites: private
  /camel-case/4.1.2:
    camel-case: private
  /camelcase-css/2.0.1:
    camelcase-css: private
  /caniuse-lite/1.0.30001723:
    caniuse-lite: private
  /ccount/2.0.1:
    ccount: private
  /chalk/4.1.2:
    chalk: private
  /character-entities-html4/2.1.0:
    character-entities-html4: private
  /character-entities-legacy/3.0.0:
    character-entities-legacy: private
  /character-entities/2.0.2:
    character-entities: private
  /character-reference-invalid/2.0.1:
    character-reference-invalid: private
  /chokidar/3.6.0:
    chokidar: private
  /client-only/0.0.1:
    client-only: private
  /clipanion/3.2.1(typanion@3.14.0):
    clipanion: private
  /cliui/8.0.1:
    cliui: private
  /color-convert/2.0.1:
    color-convert: private
  /color-name/1.1.4:
    color-name: private
  /comma-separated-tokens/2.0.3:
    comma-separated-tokens: private
  /commander/4.1.1:
    commander: private
  /comment-json/4.2.5:
    comment-json: private
  /concat-map/0.0.1:
    concat-map: private
  /config-chain/1.1.13:
    config-chain: private
  /core-js/3.43.0:
    core-js: private
  /core-util-is/1.0.3:
    core-util-is: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /cssesc/3.0.0:
    cssesc: private
  /csstype/3.1.3:
    csstype: private
  /damerau-levenshtein/1.0.8:
    damerau-levenshtein: private
  /data-uri-to-buffer/4.0.1:
    data-uri-to-buffer: private
  /data-view-buffer/1.0.2:
    data-view-buffer: private
  /data-view-byte-length/1.0.2:
    data-view-byte-length: private
  /data-view-byte-offset/1.0.1:
    data-view-byte-offset: private
  /debug/4.4.1:
    debug: private
  /decode-named-character-reference/1.2.0:
    decode-named-character-reference: private
  /deep-is/0.1.4:
    deep-is: private
  /deepmerge/4.3.1:
    deepmerge: private
  /define-data-property/1.1.4:
    define-data-property: private
  /define-properties/1.2.1:
    define-properties: private
  /dequal/2.0.3:
    dequal: private
  /devlop/1.1.0:
    devlop: private
  /didyoumean/1.2.2:
    didyoumean: private
  /diff/5.2.0:
    diff: private
  /dir-glob/3.0.1:
    dir-glob: private
  /dlv/1.1.3:
    dlv: private
  /doctrine/3.0.0:
    doctrine: private
  /dom-serializer/2.0.0:
    dom-serializer: private
  /domelementtype/2.3.0:
    domelementtype: private
  /domhandler/5.0.3:
    domhandler: private
  /domutils/3.2.2:
    domutils: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /editorconfig/1.0.4:
    editorconfig: private
  /electron-to-chromium/1.5.167:
    electron-to-chromium: private
  /emoji-regex/9.2.2:
    emoji-regex: private
  /entities/4.5.0:
    entities: private
  /es-abstract/1.24.0:
    es-abstract: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-iterator-helpers/1.2.1:
    es-iterator-helpers: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /es-set-tostringtag/2.1.0:
    es-set-tostringtag: private
  /es-shim-unscopables/1.1.0:
    es-shim-unscopables: private
  /es-to-primitive/1.3.0:
    es-to-primitive: private
  /esbuild/0.25.5:
    esbuild: private
  /escalade/3.2.0:
    escalade: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /eslint-import-resolver-node/0.3.9:
    eslint-import-resolver-node: public
  /eslint-import-resolver-typescript/3.10.1(eslint-plugin-import@2.31.0)(eslint@8.57.1):
    eslint-import-resolver-typescript: public
  /eslint-module-utils/2.12.0(@typescript-eslint/parser@6.21.0)(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-module-utils: public
  /eslint-plugin-import/2.31.0(@typescript-eslint/parser@6.21.0)(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-plugin-import: public
  /eslint-plugin-jsx-a11y/6.10.2(eslint@8.57.1):
    eslint-plugin-jsx-a11y: public
  /eslint-plugin-react-hooks/5.0.0-canary-7118f5dd7-20230705(eslint@8.57.1):
    eslint-plugin-react-hooks: public
  /eslint-plugin-react/7.37.5(eslint@8.57.1):
    eslint-plugin-react: public
  /eslint-scope/7.2.2:
    eslint-scope: public
  /eslint-utils/3.0.0(eslint@8.57.1):
    eslint-utils: public
  /eslint-visitor-keys/3.4.3:
    eslint-visitor-keys: public
  /espree/9.6.1:
    espree: private
  /esprima/4.0.1:
    esprima: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/5.3.0:
    estraverse: private
  /estree-util-attach-comments/2.1.1:
    estree-util-attach-comments: private
  /estree-util-build-jsx/2.2.2:
    estree-util-build-jsx: private
  /estree-util-is-identifier-name/1.1.0:
    estree-util-is-identifier-name: private
  /estree-util-to-js/1.2.0:
    estree-util-to-js: private
  /estree-util-value-to-estree/1.3.0:
    estree-util-value-to-estree: private
  /estree-util-visit/1.2.1:
    estree-util-visit: private
  /estree-walker/3.0.3:
    estree-walker: private
  /esutils/2.0.3:
    esutils: private
  /extend-shallow/2.0.1:
    extend-shallow: private
  /extend/3.0.2:
    extend: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fastq/1.19.1:
    fastq: private
  /fault/2.0.1:
    fault: private
  /fdir/6.4.6(picomatch@4.0.2):
    fdir: private
  /fetch-blob/3.2.0:
    fetch-blob: private
  /file-entry-cache/6.0.1:
    file-entry-cache: private
  /fill-range/7.1.1:
    fill-range: private
  /find-up/5.0.0:
    find-up: private
  /flat-cache/3.2.0:
    flat-cache: private
  /flatted/3.3.3:
    flatted: private
  /for-each/0.3.5:
    for-each: private
  /foreground-child/3.3.1:
    foreground-child: private
  /format/0.2.2:
    format: private
  /formdata-polyfill/4.0.10:
    formdata-polyfill: private
  /fraction.js/4.3.7:
    fraction.js: private
  /fs-monkey/1.0.6:
    fs-monkey: private
  /fs.realpath/1.0.0:
    fs.realpath: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /function.prototype.name/1.1.8:
    function.prototype.name: private
  /functions-have-names/1.2.3:
    functions-have-names: private
  /get-caller-file/2.0.5:
    get-caller-file: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-proto/1.0.1:
    get-proto: private
  /get-symbol-description/1.1.0:
    get-symbol-description: private
  /get-tsconfig/4.10.1:
    get-tsconfig: private
  /github-slugger/2.0.0:
    github-slugger: private
  /glob-parent/6.0.2:
    glob-parent: private
  /glob/10.3.10:
    glob: private
  /globals/16.2.0:
    globals: private
  /globalthis/1.0.4:
    globalthis: private
  /globby/11.1.0:
    globby: private
  /gopd/1.2.0:
    gopd: private
  /graceful-fs/4.2.11:
    graceful-fs: private
  /graphemer/1.4.0:
    graphemer: private
  /has-bigints/1.1.0:
    has-bigints: private
  /has-flag/4.0.0:
    has-flag: private
  /has-own-prop/2.0.0:
    has-own-prop: private
  /has-property-descriptors/1.0.2:
    has-property-descriptors: private
  /has-proto/1.2.0:
    has-proto: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /hash-wasm/4.12.0:
    hash-wasm: private
  /hasown/2.0.2:
    hasown: private
  /hast-util-from-parse5/7.1.2:
    hast-util-from-parse5: private
  /hast-util-heading-rank/3.0.0:
    hast-util-heading-rank: private
  /hast-util-is-element/3.0.0:
    hast-util-is-element: private
  /hast-util-parse-selector/3.1.1:
    hast-util-parse-selector: private
  /hast-util-raw/7.2.3:
    hast-util-raw: private
  /hast-util-sanitize/5.0.2:
    hast-util-sanitize: private
  /hast-util-to-estree/2.3.3:
    hast-util-to-estree: private
  /hast-util-to-html/9.0.5:
    hast-util-to-html: private
  /hast-util-to-parse5/7.1.0:
    hast-util-to-parse5: private
  /hast-util-to-string/3.0.1:
    hast-util-to-string: private
  /hast-util-to-text/4.0.2:
    hast-util-to-text: private
  /hast-util-whitespace/3.0.0:
    hast-util-whitespace: private
  /hastscript/7.2.0:
    hastscript: private
  /highlight.js/11.11.1:
    highlight.js: private
  /hoist-non-react-statics/3.3.2:
    hoist-non-react-statics: private
  /html-parse-stringify/3.0.1:
    html-parse-stringify: private
  /html-to-text/9.0.5:
    html-to-text: private
  /html-void-elements/3.0.0:
    html-void-elements: private
  /htmlparser2/8.0.2:
    htmlparser2: private
  /i18next-fs-backend/2.6.0:
    i18next-fs-backend: private
  /ignore/5.3.2:
    ignore: private
  /imagescript/1.3.1:
    imagescript: private
  /import-fresh/3.3.1:
    import-fresh: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /inflection/2.0.1:
    inflection: private
  /inflight/1.0.6:
    inflight: private
  /inherits/2.0.4:
    inherits: private
  /ini/1.3.8:
    ini: private
  /inline-style-parser/0.1.1:
    inline-style-parser: private
  /internal-slot/1.1.0:
    internal-slot: private
  /is-alphabetical/2.0.1:
    is-alphabetical: private
  /is-alphanumerical/2.0.1:
    is-alphanumerical: private
  /is-array-buffer/3.0.5:
    is-array-buffer: private
  /is-async-function/2.1.1:
    is-async-function: private
  /is-bigint/1.1.0:
    is-bigint: private
  /is-binary-path/2.1.0:
    is-binary-path: private
  /is-boolean-object/1.2.2:
    is-boolean-object: private
  /is-buffer/2.0.5:
    is-buffer: private
  /is-bun-module/2.0.0:
    is-bun-module: private
  /is-callable/1.2.7:
    is-callable: private
  /is-core-module/2.16.1:
    is-core-module: private
  /is-data-view/1.0.2:
    is-data-view: private
  /is-date-object/1.1.0:
    is-date-object: private
  /is-decimal/2.0.1:
    is-decimal: private
  /is-extendable/0.1.1:
    is-extendable: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-finalizationregistry/1.1.1:
    is-finalizationregistry: private
  /is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  /is-generator-function/1.1.0:
    is-generator-function: private
  /is-glob/4.0.3:
    is-glob: private
  /is-hexadecimal/2.0.1:
    is-hexadecimal: private
  /is-map/2.0.3:
    is-map: private
  /is-negative-zero/2.0.3:
    is-negative-zero: private
  /is-number-object/1.1.1:
    is-number-object: private
  /is-number/7.0.0:
    is-number: private
  /is-path-inside/3.0.3:
    is-path-inside: private
  /is-plain-obj/4.1.0:
    is-plain-obj: private
  /is-reference/3.0.3:
    is-reference: private
  /is-regex/1.2.1:
    is-regex: private
  /is-set/2.0.3:
    is-set: private
  /is-shared-array-buffer/1.0.4:
    is-shared-array-buffer: private
  /is-string/1.1.1:
    is-string: private
  /is-symbol/1.1.1:
    is-symbol: private
  /is-typed-array/1.1.15:
    is-typed-array: private
  /is-weakmap/2.0.2:
    is-weakmap: private
  /is-weakref/1.1.1:
    is-weakref: private
  /is-weakset/2.0.4:
    is-weakset: private
  /isarray/2.0.5:
    isarray: private
  /isexe/2.0.0:
    isexe: private
  /iterator.prototype/1.1.5:
    iterator.prototype: private
  /jackspeak/2.3.6:
    jackspeak: private
  /jiti/1.21.7:
    jiti: private
  /js-beautify/1.15.4:
    js-beautify: private
  /js-cookie/3.0.5:
    js-cookie: private
  /js-tokens/4.0.0:
    js-tokens: private
  /js-yaml/4.1.0:
    js-yaml: private
  /jsbi/4.3.2:
    jsbi: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /json5/1.0.2:
    json5: private
  /jsx-ast-utils/3.3.5:
    jsx-ast-utils: private
  /keyv/4.5.4:
    keyv: private
  /kind-of/6.0.3:
    kind-of: private
  /kleur/4.1.5:
    kleur: private
  /language-subtag-registry/0.3.23:
    language-subtag-registry: private
  /language-tags/1.0.9:
    language-tags: private
  /leac/0.6.0:
    leac: private
  /levn/0.4.1:
    levn: private
  /lilconfig/3.1.3:
    lilconfig: private
  /lines-and-columns/1.2.4:
    lines-and-columns: private
  /locate-path/6.0.0:
    locate-path: private
  /lodash.camelcase/4.3.0:
    lodash.camelcase: private
  /lodash.castarray/4.4.0:
    lodash.castarray: private
  /lodash.isplainobject/4.0.6:
    lodash.isplainobject: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /long/5.3.2:
    long: private
  /longest-streak/3.1.0:
    longest-streak: private
  /loose-envify/1.4.0:
    loose-envify: private
  /lower-case/2.0.2:
    lower-case: private
  /lowlight/3.3.0:
    lowlight: private
  /lru-cache/10.4.3:
    lru-cache: private
  /markdown-extensions/1.1.1:
    markdown-extensions: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /mdast-util-definitions/5.1.2:
    mdast-util-definitions: private
  /mdast-util-from-markdown/2.0.2:
    mdast-util-from-markdown: private
  /mdast-util-frontmatter/1.0.1:
    mdast-util-frontmatter: private
  /mdast-util-mdx-expression/1.3.2:
    mdast-util-mdx-expression: private
  /mdast-util-mdx-jsx/2.1.4:
    mdast-util-mdx-jsx: private
  /mdast-util-mdx/2.0.1:
    mdast-util-mdx: private
  /mdast-util-mdxjs-esm/1.3.1:
    mdast-util-mdxjs-esm: private
  /mdast-util-phrasing/4.1.0:
    mdast-util-phrasing: private
  /mdast-util-to-hast/13.2.0:
    mdast-util-to-hast: private
  /mdast-util-to-markdown/2.1.2:
    mdast-util-to-markdown: private
  /mdast-util-to-string/4.0.0:
    mdast-util-to-string: private
  /mdx-bundler/9.2.1(esbuild@0.25.5):
    mdx-bundler: private
  /memfs/3.5.3:
    memfs: private
  /merge2/1.4.1:
    merge2: private
  /micromark-core-commonmark/2.0.3:
    micromark-core-commonmark: private
  /micromark-extension-frontmatter/1.1.1:
    micromark-extension-frontmatter: private
  /micromark-extension-mdx-expression/1.0.8:
    micromark-extension-mdx-expression: private
  /micromark-extension-mdx-jsx/1.0.5:
    micromark-extension-mdx-jsx: private
  /micromark-extension-mdx-md/1.0.1:
    micromark-extension-mdx-md: private
  /micromark-extension-mdxjs-esm/1.0.5:
    micromark-extension-mdxjs-esm: private
  /micromark-extension-mdxjs/1.0.1:
    micromark-extension-mdxjs: private
  /micromark-factory-destination/2.0.1:
    micromark-factory-destination: private
  /micromark-factory-label/2.0.1:
    micromark-factory-label: private
  /micromark-factory-mdx-expression/1.0.9:
    micromark-factory-mdx-expression: private
  /micromark-factory-space/2.0.1:
    micromark-factory-space: private
  /micromark-factory-title/2.0.1:
    micromark-factory-title: private
  /micromark-factory-whitespace/2.0.1:
    micromark-factory-whitespace: private
  /micromark-util-character/1.2.0:
    micromark-util-character: private
  /micromark-util-chunked/2.0.1:
    micromark-util-chunked: private
  /micromark-util-classify-character/2.0.1:
    micromark-util-classify-character: private
  /micromark-util-combine-extensions/2.0.1:
    micromark-util-combine-extensions: private
  /micromark-util-decode-numeric-character-reference/2.0.2:
    micromark-util-decode-numeric-character-reference: private
  /micromark-util-decode-string/2.0.1:
    micromark-util-decode-string: private
  /micromark-util-encode/2.0.1:
    micromark-util-encode: private
  /micromark-util-events-to-acorn/1.2.3:
    micromark-util-events-to-acorn: private
  /micromark-util-html-tag-name/2.0.1:
    micromark-util-html-tag-name: private
  /micromark-util-normalize-identifier/2.0.1:
    micromark-util-normalize-identifier: private
  /micromark-util-resolve-all/2.0.1:
    micromark-util-resolve-all: private
  /micromark-util-sanitize-uri/2.0.1:
    micromark-util-sanitize-uri: private
  /micromark-util-subtokenize/2.1.0:
    micromark-util-subtokenize: private
  /micromark-util-symbol/2.0.1:
    micromark-util-symbol: private
  /micromark-util-types/2.0.2:
    micromark-util-types: private
  /micromark/4.0.2:
    micromark: private
  /micromatch/4.0.8:
    micromatch: private
  /mini-svg-data-uri/1.4.4:
    mini-svg-data-uri: private
  /minimatch/3.1.2:
    minimatch: private
  /minimist/1.2.8:
    minimist: private
  /minipass/7.1.2:
    minipass: private
  /mri/1.2.0:
    mri: private
  /ms/2.1.3:
    ms: private
  /mz/2.7.0:
    mz: private
  /nanoid/3.3.11:
    nanoid: private
  /napi-postinstall/0.2.4:
    napi-postinstall: private
  /natural-compare/1.4.0:
    natural-compare: private
  /no-case/3.0.4:
    no-case: private
  /node-domexception/1.0.0:
    node-domexception: private
  /node-fetch/3.3.2:
    node-fetch: private
  /node-releases/2.0.19:
    node-releases: private
  /nopt/7.2.1:
    nopt: private
  /normalize-path/3.0.0:
    normalize-path: private
  /normalize-range/0.1.2:
    normalize-range: private
  /object-assign/4.1.1:
    object-assign: private
  /object-hash/3.0.0:
    object-hash: private
  /object-inspect/1.13.4:
    object-inspect: private
  /object-keys/1.1.1:
    object-keys: private
  /object.assign/4.1.7:
    object.assign: private
  /object.entries/1.1.9:
    object.entries: private
  /object.fromentries/2.0.8:
    object.fromentries: private
  /object.groupby/1.0.3:
    object.groupby: private
  /object.values/1.2.1:
    object.values: private
  /once/1.4.0:
    once: private
  /oo-ascii-tree/1.112.0:
    oo-ascii-tree: private
  /optionator/0.9.4:
    optionator: private
  /own-keys/1.0.1:
    own-keys: private
  /p-limit/3.1.0:
    p-limit: private
  /p-locate/5.0.0:
    p-locate: private
  /package-json-from-dist/1.0.1:
    package-json-from-dist: private
  /parent-module/1.0.1:
    parent-module: private
  /parse-entities/4.0.2:
    parse-entities: private
  /parse5/6.0.1:
    parse5: private
  /parseley/0.12.1:
    parseley: private
  /pascal-case/3.1.2:
    pascal-case: private
  /path-exists/4.0.0:
    path-exists: private
  /path-is-absolute/1.0.1:
    path-is-absolute: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /path-scurry/1.11.1:
    path-scurry: private
  /path-type/4.0.0:
    path-type: private
  /peberminta/0.9.0:
    peberminta: private
  /periscopic/3.1.0:
    periscopic: private
  /picocolors/1.1.1:
    picocolors: private
  /picomatch/2.3.1:
    picomatch: private
  /pify/2.3.0:
    pify: private
  /pirates/4.0.7:
    pirates: private
  /possible-typed-array-names/1.1.0:
    possible-typed-array-names: private
  /postcss-import/15.1.0(postcss@8.5.5):
    postcss-import: private
  /postcss-js/4.0.1(postcss@8.5.5):
    postcss-js: private
  /postcss-load-config/4.0.2(postcss@8.5.5):
    postcss-load-config: private
  /postcss-nested/6.2.0(postcss@8.5.5):
    postcss-nested: private
  /postcss-selector-parser/6.0.10:
    postcss-selector-parser: private
  /postcss-value-parser/4.2.0:
    postcss-value-parser: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /prop-types/15.8.1:
    prop-types: private
  /property-information/7.1.0:
    property-information: private
  /proto-list/1.2.4:
    proto-list: private
  /protobufjs/7.5.3:
    protobufjs: private
  /punycode/2.3.1:
    punycode: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /react-is/16.13.1:
    react-is: private
  /react-promise-suspense/0.3.4:
    react-promise-suspense: private
  /read-cache/1.0.0:
    read-cache: private
  /readdirp/3.6.0:
    readdirp: private
  /reflect.getprototypeof/1.0.10:
    reflect.getprototypeof: private
  /regexp.prototype.flags/1.5.4:
    regexp.prototype.flags: private
  /rehype-stringify/9.0.4:
    rehype-stringify: private
  /remark-frontmatter/4.0.1:
    remark-frontmatter: private
  /remark-mdx-frontmatter/1.1.1:
    remark-mdx-frontmatter: private
  /remark-mdx/2.3.0:
    remark-mdx: private
  /remark-parse/11.0.0:
    remark-parse: private
  /remark-rehype/10.1.0:
    remark-rehype: private
  /remark-stringify/11.0.0:
    remark-stringify: private
  /repeat-string/1.6.1:
    repeat-string: private
  /require-directory/2.1.1:
    require-directory: private
  /resolve-from/4.0.0:
    resolve-from: private
  /resolve-pkg-maps/1.0.0:
    resolve-pkg-maps: private
  /resolve/1.22.10:
    resolve: private
  /reusify/1.1.0:
    reusify: private
  /rimraf/3.0.2:
    rimraf: private
  /run-parallel/1.2.0:
    run-parallel: private
  /sade/1.8.1:
    sade: private
  /safe-array-concat/1.1.3:
    safe-array-concat: private
  /safe-push-apply/1.0.0:
    safe-push-apply: private
  /safe-regex-test/1.1.0:
    safe-regex-test: private
  /scheduler/0.23.2:
    scheduler: private
  /section-matter/1.0.0:
    section-matter: private
  /selderee/0.11.0:
    selderee: private
  /semver/7.7.2:
    semver: private
  /set-function-length/1.2.2:
    set-function-length: private
  /set-function-name/2.0.2:
    set-function-name: private
  /set-proto/1.0.0:
    set-proto: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /side-channel-list/1.0.0:
    side-channel-list: private
  /side-channel-map/1.0.1:
    side-channel-map: private
  /side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  /side-channel/1.1.0:
    side-channel: private
  /signal-exit/4.1.0:
    signal-exit: private
  /slash/3.0.0:
    slash: private
  /source-map-js/1.2.1:
    source-map-js: private
  /source-map-support/0.5.21:
    source-map-support: private
  /source-map/0.6.1:
    source-map: private
  /space-separated-tokens/2.0.2:
    space-separated-tokens: private
  /sprintf-js/1.0.3:
    sprintf-js: private
  /stable-hash/0.0.5:
    stable-hash: private
  /stop-iteration-iterator/1.1.0:
    stop-iteration-iterator: private
  /streamsearch/1.1.0:
    streamsearch: private
  /string-width/4.2.3:
    string-width-cjs: private
  /string-width/5.1.2:
    string-width: private
  /string.prototype.includes/2.0.1:
    string.prototype.includes: private
  /string.prototype.matchall/4.0.12:
    string.prototype.matchall: private
  /string.prototype.repeat/1.0.0:
    string.prototype.repeat: private
  /string.prototype.trim/1.2.10:
    string.prototype.trim: private
  /string.prototype.trimend/1.0.9:
    string.prototype.trimend: private
  /string.prototype.trimstart/1.0.8:
    string.prototype.trimstart: private
  /stringify-entities/4.0.4:
    stringify-entities: private
  /strip-ansi/6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  /strip-bom-string/1.0.0:
    strip-bom-string: private
  /strip-bom/3.0.0:
    strip-bom: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /style-to-object/0.4.4:
    style-to-object: private
  /styled-jsx/5.1.1(react@18.3.1):
    styled-jsx: private
  /sucrase/3.35.0:
    sucrase: private
  /supports-color/7.2.0:
    supports-color: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /text-table/0.2.0:
    text-table: private
  /thenify-all/1.6.0:
    thenify-all: private
  /thenify/3.3.1:
    thenify: private
  /tinyglobby/0.2.14:
    tinyglobby: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /toml/3.0.0:
    toml: private
  /trim-lines/3.0.1:
    trim-lines: private
  /trough/2.2.0:
    trough: private
  /ts-api-utils/1.4.3(typescript@5.8.3):
    ts-api-utils: private
  /ts-interface-checker/0.1.13:
    ts-interface-checker: private
  /ts-pattern/4.3.0:
    ts-pattern: private
  /tsconfig-paths/3.15.0:
    tsconfig-paths: private
  /tslib/2.8.1:
    tslib: private
  /typanion/3.14.0:
    typanion: private
  /type-check/0.4.0:
    type-check: private
  /type-fest/3.13.1:
    type-fest: private
  /typed-array-buffer/1.0.3:
    typed-array-buffer: private
  /typed-array-byte-length/1.0.3:
    typed-array-byte-length: private
  /typed-array-byte-offset/1.0.4:
    typed-array-byte-offset: private
  /typed-array-length/1.0.7:
    typed-array-length: private
  /unbox-primitive/1.1.0:
    unbox-primitive: private
  /undici-types/6.21.0:
    undici-types: private
  /unified/11.0.5:
    unified: private
  /unist-util-find-after/5.0.0:
    unist-util-find-after: private
  /unist-util-generated/2.0.1:
    unist-util-generated: private
  /unist-util-is/6.0.0:
    unist-util-is: private
  /unist-util-position-from-estree/1.1.2:
    unist-util-position-from-estree: private
  /unist-util-position/5.0.0:
    unist-util-position: private
  /unist-util-remove-position/4.0.2:
    unist-util-remove-position: private
  /unist-util-stringify-position/4.0.0:
    unist-util-stringify-position: private
  /unist-util-visit-parents/6.0.1:
    unist-util-visit-parents: private
  /unist-util-visit/5.0.0:
    unist-util-visit: private
  /unrs-resolver/1.9.0:
    unrs-resolver: private
  /update-browserslist-db/1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  /uri-js/4.4.1:
    uri-js: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /uuid/8.3.2:
    uuid: private
  /uvu/0.5.6:
    uvu: private
  /vfile-location/4.1.0:
    vfile-location: private
  /vfile-message/4.0.2:
    vfile-message: private
  /vfile/6.0.3:
    vfile: private
  /void-elements/3.1.0:
    void-elements: private
  /web-namespaces/2.0.1:
    web-namespaces: private
  /web-streams-polyfill/3.3.3:
    web-streams-polyfill: private
  /which-boxed-primitive/1.1.1:
    which-boxed-primitive: private
  /which-builtin-type/1.2.1:
    which-builtin-type: private
  /which-collection/1.0.2:
    which-collection: private
  /which-typed-array/1.1.19:
    which-typed-array: private
  /which/2.0.2:
    which: private
  /word-wrap/1.2.5:
    word-wrap: private
  /wrap-ansi/7.0.0:
    wrap-ansi-cjs: private
  /wrap-ansi/8.1.0:
    wrap-ansi: private
  /wrappy/1.0.2:
    wrappy: private
  /y18n/5.0.8:
    y18n: private
  /yaml/2.8.0:
    yaml: private
  /yargs-parser/21.1.1:
    yargs-parser: private
  /yargs/17.7.2:
    yargs: private
  /yocto-queue/0.1.0:
    yocto-queue: private
  /zwitch/2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.15.1
pendingBuilds: []
prunedAt: Sat, 14 Jun 2025 15:28:20 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /@emnapi/core/1.4.3
  - /@emnapi/runtime/1.4.3
  - /@emnapi/wasi-threads/1.0.2
  - /@esbuild/aix-ppc64/0.25.5
  - /@esbuild/android-arm/0.25.5
  - /@esbuild/android-arm64/0.25.5
  - /@esbuild/android-x64/0.25.5
  - /@esbuild/darwin-x64/0.25.5
  - /@esbuild/freebsd-arm64/0.25.5
  - /@esbuild/freebsd-x64/0.25.5
  - /@esbuild/linux-arm/0.25.5
  - /@esbuild/linux-arm64/0.25.5
  - /@esbuild/linux-ia32/0.25.5
  - /@esbuild/linux-loong64/0.25.5
  - /@esbuild/linux-mips64el/0.25.5
  - /@esbuild/linux-ppc64/0.25.5
  - /@esbuild/linux-riscv64/0.25.5
  - /@esbuild/linux-s390x/0.25.5
  - /@esbuild/linux-x64/0.25.5
  - /@esbuild/netbsd-arm64/0.25.5
  - /@esbuild/netbsd-x64/0.25.5
  - /@esbuild/openbsd-arm64/0.25.5
  - /@esbuild/openbsd-x64/0.25.5
  - /@esbuild/sunos-x64/0.25.5
  - /@esbuild/win32-arm64/0.25.5
  - /@esbuild/win32-ia32/0.25.5
  - /@esbuild/win32-x64/0.25.5
  - /@napi-rs/wasm-runtime/0.2.11
  - /@next/swc-darwin-x64/14.2.30
  - /@next/swc-linux-arm64-gnu/14.2.30
  - /@next/swc-linux-arm64-musl/14.2.30
  - /@next/swc-linux-x64-gnu/14.2.30
  - /@next/swc-linux-x64-musl/14.2.30
  - /@next/swc-win32-arm64-msvc/14.2.30
  - /@next/swc-win32-ia32-msvc/14.2.30
  - /@next/swc-win32-x64-msvc/14.2.30
  - /@tybys/wasm-util/0.9.0
  - /@unrs/resolver-binding-android-arm-eabi/1.9.0
  - /@unrs/resolver-binding-android-arm64/1.9.0
  - /@unrs/resolver-binding-darwin-x64/1.9.0
  - /@unrs/resolver-binding-freebsd-x64/1.9.0
  - /@unrs/resolver-binding-linux-arm-gnueabihf/1.9.0
  - /@unrs/resolver-binding-linux-arm-musleabihf/1.9.0
  - /@unrs/resolver-binding-linux-arm64-gnu/1.9.0
  - /@unrs/resolver-binding-linux-arm64-musl/1.9.0
  - /@unrs/resolver-binding-linux-ppc64-gnu/1.9.0
  - /@unrs/resolver-binding-linux-riscv64-gnu/1.9.0
  - /@unrs/resolver-binding-linux-riscv64-musl/1.9.0
  - /@unrs/resolver-binding-linux-s390x-gnu/1.9.0
  - /@unrs/resolver-binding-linux-x64-gnu/1.9.0
  - /@unrs/resolver-binding-linux-x64-musl/1.9.0
  - /@unrs/resolver-binding-wasm32-wasi/1.9.0
  - /@unrs/resolver-binding-win32-arm64-msvc/1.9.0
  - /@unrs/resolver-binding-win32-ia32-msvc/1.9.0
  - /@unrs/resolver-binding-win32-x64-msvc/1.9.0
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
