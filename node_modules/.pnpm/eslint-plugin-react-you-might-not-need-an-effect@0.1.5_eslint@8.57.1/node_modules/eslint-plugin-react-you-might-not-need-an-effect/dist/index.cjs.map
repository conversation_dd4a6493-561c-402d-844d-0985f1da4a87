{"version": 3, "sources": ["../src/messages.js", "../node_modules/eslint-utils/node_modules/eslint-visitor-keys/lib/visitor-keys.json", "../node_modules/eslint-utils/node_modules/eslint-visitor-keys/lib/index.js", "../node_modules/eslint-utils/src/get-innermost-scope.js", "../node_modules/eslint-utils/src/find-variable.js", "../node_modules/eslint-utils/src/token-predicate.js", "../node_modules/eslint-utils/src/get-function-head-location.js", "../node_modules/eslint-utils/src/get-static-value.js", "../node_modules/eslint-utils/src/get-string-if-constant.js", "../node_modules/eslint-utils/src/get-property-name.js", "../node_modules/eslint-utils/src/get-function-name-with-kind.js", "../node_modules/eslint-utils/src/has-side-effect.js", "../node_modules/eslint-utils/src/is-parenthesized.js", "../node_modules/eslint-utils/src/pattern-matcher.js", "../node_modules/eslint-utils/src/reference-tracker.js", "../node_modules/eslint-utils/src/index.js", "../src/util/ast.js", "../src/util/react.js", "../src/util/javascript.js", "../src/rule.js", "../node_modules/globals/globals.json", "../node_modules/globals/index.js", "../src/index.js", "../src/index.cjs"], "sourcesContent": ["export const messageIds = {\n  avoidEmptyEffect: \"avoidEmptyEffect\",\n  avoidDerivedState: \"avoidDerivedState\",\n  avoidInitializingState: \"avoidInitializingState\",\n  avoidChainingState: \"avoidChainingState\",\n  avoidParentChildCoupling: \"avoidParentChildCoupling\",\n  avoidResettingStateFromProps: \"avoidResettingStateFromProps\",\n  // TODO: This would be nice, but I'm not sure it can be done accurately\n  // Maybe we can accurately warn about this when the state being reacted to is one of our own `useState`s?\n  // Because if we have a setter then we have a callback.\n  // But, I think that would also warn about valid uses that synchronize internal state to external state.\n  // avoidEventHandler: \"avoidEventHandler\",\n  // TODO: Possible to detect when `useSyncExternalStore` should be preferred?\n};\n\n// TODO: Could include more info in messages, like the relevant node\nexport const messages = {\n  [messageIds.avoidEmptyEffect]: \"This effect is empty and could be removed.\",\n  [messageIds.avoidDerivedState]:\n    'Avoid storing derived state. Compute \"{{state}}\" directly during render, optionally with `useMemo` if it\\'s expensive.',\n  [messageIds.avoidInitializingState]:\n    'Avoid initializing state in an effect. Instead, pass \"{{state}}\"\\'s initial value to its `useState`.',\n  [messageIds.avoidChainingState]:\n    \"Avoid chaining state changes. When possible, update all relevant state simultaneously.\",\n  [messageIds.avoidParentChildCoupling]:\n    \"Avoid coupling parent behavior or state to a child component. Instead, lift shared logic or state up to the parent.\",\n  [messageIds.avoidResettingStateFromProps]:\n    'Avoid resetting state from props. If \"{{prop}}\" is a key, pass it as `key` instead so React will reset the component.',\n  // [messages.avoidEventHandler]:\n  //   \"Avoid using state as an event handler. Instead, call the event handler directly.\",\n};\n", "{\n    \"AssignmentExpression\": [\n        \"left\",\n        \"right\"\n    ],\n    \"AssignmentPattern\": [\n        \"left\",\n        \"right\"\n    ],\n    \"ArrayExpression\": [\n        \"elements\"\n    ],\n    \"ArrayPattern\": [\n        \"elements\"\n    ],\n    \"ArrowFunctionExpression\": [\n        \"params\",\n        \"body\"\n    ],\n    \"AwaitExpression\": [\n        \"argument\"\n    ],\n    \"BlockStatement\": [\n        \"body\"\n    ],\n    \"BinaryExpression\": [\n        \"left\",\n        \"right\"\n    ],\n    \"BreakStatement\": [\n        \"label\"\n    ],\n    \"CallExpression\": [\n        \"callee\",\n        \"arguments\"\n    ],\n    \"CatchClause\": [\n        \"param\",\n        \"body\"\n    ],\n    \"ChainExpression\": [\n        \"expression\"\n    ],\n    \"ClassBody\": [\n        \"body\"\n    ],\n    \"ClassDeclaration\": [\n        \"id\",\n        \"superClass\",\n        \"body\"\n    ],\n    \"ClassExpression\": [\n        \"id\",\n        \"superClass\",\n        \"body\"\n    ],\n    \"ConditionalExpression\": [\n        \"test\",\n        \"consequent\",\n        \"alternate\"\n    ],\n    \"ContinueStatement\": [\n        \"label\"\n    ],\n    \"DebuggerStatement\": [],\n    \"DoWhileStatement\": [\n        \"body\",\n        \"test\"\n    ],\n    \"EmptyStatement\": [],\n    \"ExportAllDeclaration\": [\n        \"exported\",\n        \"source\"\n    ],\n    \"ExportDefaultDeclaration\": [\n        \"declaration\"\n    ],\n    \"ExportNamedDeclaration\": [\n        \"declaration\",\n        \"specifiers\",\n        \"source\"\n    ],\n    \"ExportSpecifier\": [\n        \"exported\",\n        \"local\"\n    ],\n    \"ExpressionStatement\": [\n        \"expression\"\n    ],\n    \"ExperimentalRestProperty\": [\n        \"argument\"\n    ],\n    \"ExperimentalSpreadProperty\": [\n        \"argument\"\n    ],\n    \"ForStatement\": [\n        \"init\",\n        \"test\",\n        \"update\",\n        \"body\"\n    ],\n    \"ForInStatement\": [\n        \"left\",\n        \"right\",\n        \"body\"\n    ],\n    \"ForOfStatement\": [\n        \"left\",\n        \"right\",\n        \"body\"\n    ],\n    \"FunctionDeclaration\": [\n        \"id\",\n        \"params\",\n        \"body\"\n    ],\n    \"FunctionExpression\": [\n        \"id\",\n        \"params\",\n        \"body\"\n    ],\n    \"Identifier\": [],\n    \"IfStatement\": [\n        \"test\",\n        \"consequent\",\n        \"alternate\"\n    ],\n    \"ImportDeclaration\": [\n        \"specifiers\",\n        \"source\"\n    ],\n    \"ImportDefaultSpecifier\": [\n        \"local\"\n    ],\n    \"ImportExpression\": [\n        \"source\"\n    ],\n    \"ImportNamespaceSpecifier\": [\n        \"local\"\n    ],\n    \"ImportSpecifier\": [\n        \"imported\",\n        \"local\"\n    ],\n    \"JSXAttribute\": [\n        \"name\",\n        \"value\"\n    ],\n    \"JSXClosingElement\": [\n        \"name\"\n    ],\n    \"JSXElement\": [\n        \"openingElement\",\n        \"children\",\n        \"closingElement\"\n    ],\n    \"JSXEmptyExpression\": [],\n    \"JSXExpressionContainer\": [\n        \"expression\"\n    ],\n    \"JSXIdentifier\": [],\n    \"JSXMemberExpression\": [\n        \"object\",\n        \"property\"\n    ],\n    \"JSXNamespacedName\": [\n        \"namespace\",\n        \"name\"\n    ],\n    \"JSXOpeningElement\": [\n        \"name\",\n        \"attributes\"\n    ],\n    \"JSXSpreadAttribute\": [\n        \"argument\"\n    ],\n    \"JSXText\": [],\n    \"JSXFragment\": [\n        \"openingFragment\",\n        \"children\",\n        \"closingFragment\"\n    ],\n    \"Literal\": [],\n    \"LabeledStatement\": [\n        \"label\",\n        \"body\"\n    ],\n    \"LogicalExpression\": [\n        \"left\",\n        \"right\"\n    ],\n    \"MemberExpression\": [\n        \"object\",\n        \"property\"\n    ],\n    \"MetaProperty\": [\n        \"meta\",\n        \"property\"\n    ],\n    \"MethodDefinition\": [\n        \"key\",\n        \"value\"\n    ],\n    \"NewExpression\": [\n        \"callee\",\n        \"arguments\"\n    ],\n    \"ObjectExpression\": [\n        \"properties\"\n    ],\n    \"ObjectPattern\": [\n        \"properties\"\n    ],\n    \"PrivateIdentifier\": [],\n    \"Program\": [\n        \"body\"\n    ],\n    \"Property\": [\n        \"key\",\n        \"value\"\n    ],\n    \"PropertyDefinition\": [\n        \"key\",\n        \"value\"\n    ],\n    \"RestElement\": [\n        \"argument\"\n    ],\n    \"ReturnStatement\": [\n        \"argument\"\n    ],\n    \"SequenceExpression\": [\n        \"expressions\"\n    ],\n    \"SpreadElement\": [\n        \"argument\"\n    ],\n    \"Super\": [],\n    \"SwitchStatement\": [\n        \"discriminant\",\n        \"cases\"\n    ],\n    \"SwitchCase\": [\n        \"test\",\n        \"consequent\"\n    ],\n    \"TaggedTemplateExpression\": [\n        \"tag\",\n        \"quasi\"\n    ],\n    \"TemplateElement\": [],\n    \"TemplateLiteral\": [\n        \"quasis\",\n        \"expressions\"\n    ],\n    \"ThisExpression\": [],\n    \"ThrowStatement\": [\n        \"argument\"\n    ],\n    \"TryStatement\": [\n        \"block\",\n        \"handler\",\n        \"finalizer\"\n    ],\n    \"UnaryExpression\": [\n        \"argument\"\n    ],\n    \"UpdateExpression\": [\n        \"argument\"\n    ],\n    \"VariableDeclaration\": [\n        \"declarations\"\n    ],\n    \"VariableDeclarator\": [\n        \"id\",\n        \"init\"\n    ],\n    \"WhileStatement\": [\n        \"test\",\n        \"body\"\n    ],\n    \"WithStatement\": [\n        \"object\",\n        \"body\"\n    ],\n    \"YieldExpression\": [\n        \"argument\"\n    ]\n}\n", "/**\n * <AUTHOR> <https://github.com/mysticatea>\n * See LICENSE file in root directory for full license.\n */\n\"use strict\";\n\nconst KEYS = require(\"./visitor-keys.json\");\n\n// Types.\nconst NODE_TYPES = Object.freeze(Object.keys(KEYS));\n\n// Freeze the keys.\nfor (const type of NODE_TYPES) {\n    Object.freeze(KEYS[type]);\n}\nObject.freeze(KEYS);\n\n// List to ignore keys.\nconst KEY_BLACKLIST = new Set([\n    \"parent\",\n    \"leadingComments\",\n    \"trailingComments\"\n]);\n\n/**\n * Check whether a given key should be used or not.\n * @param {string} key The key to check.\n * @returns {boolean} `true` if the key should be used.\n */\nfunction filterKey(key) {\n    return !KEY_BLACKLIST.has(key) && key[0] !== \"_\";\n}\n\n//------------------------------------------------------------------------------\n// Public interfaces\n//------------------------------------------------------------------------------\n\nmodule.exports = Object.freeze({\n\n    /**\n     * Visitor keys.\n     * @type {{ [type: string]: string[] | undefined }}\n     */\n    KEYS,\n\n    /**\n     * Get visitor keys of a given node.\n     * @param {Object} node The AST node to get keys.\n     * @returns {string[]} Visitor keys of the node.\n     */\n    getKeys(node) {\n        return Object.keys(node).filter(filterKey);\n    },\n\n    // Disable valid-jsdoc rule because it reports syntax error on the type of @returns.\n    // eslint-disable-next-line valid-jsdoc\n    /**\n     * Make the union set with `KEYS` and given keys.\n     * @param {Object} additionalKeys The additional keys.\n     * @returns {{ [type: string]: string[] | undefined }} The union set.\n     */\n    unionWith(additionalKeys) {\n        const retv = Object.assign({}, KEYS);\n\n        for (const type of Object.keys(additionalKeys)) {\n            if (retv.hasOwnProperty(type)) {\n                const keys = new Set(additionalKeys[type]);\n\n                for (const key of retv[type]) {\n                    keys.add(key);\n                }\n\n                retv[type] = Object.freeze(Array.from(keys));\n            } else {\n                retv[type] = Object.freeze(Array.from(additionalKeys[type]));\n            }\n        }\n\n        return Object.freeze(retv);\n    }\n});\n", "/**\n * Get the innermost scope which contains a given location.\n * @param {Scope} initialScope The initial scope to search.\n * @param {Node} node The location to search.\n * @returns {Scope} The innermost scope.\n */\nexport function getInnermostScope(initialScope, node) {\n    const location = node.range[0]\n\n    let scope = initialScope\n    let found = false\n    do {\n        found = false\n        for (const childScope of scope.childScopes) {\n            const range = childScope.block.range\n\n            if (range[0] <= location && location < range[1]) {\n                scope = childScope\n                found = true\n                break\n            }\n        }\n    } while (found)\n\n    return scope\n}\n", "import { getInnermostScope } from \"./get-innermost-scope\"\n\n/**\n * Find the variable of a given name.\n * @param {Scope} initialScope The scope to start finding.\n * @param {string|Node} nameOrNode The variable name to find. If this is a Node object then it should be an Identifier node.\n * @returns {Variable|null} The found variable or null.\n */\nexport function findVariable(initialScope, nameOrNode) {\n    let name = \"\"\n    let scope = initialScope\n\n    if (typeof nameOrNode === \"string\") {\n        name = nameOrNode\n    } else {\n        name = nameOrNode.name\n        scope = getInnermostScope(scope, nameOrNode)\n    }\n\n    while (scope != null) {\n        const variable = scope.set.get(name)\n        if (variable != null) {\n            return variable\n        }\n        scope = scope.upper\n    }\n\n    return null\n}\n", "/**\n * Negate the result of `this` calling.\n * @param {Token} token The token to check.\n * @returns {boolean} `true` if the result of `this(token)` is `false`.\n */\nfunction negate0(token) {\n    return !this(token) //eslint-disable-line no-invalid-this\n}\n\n/**\n * Creates the negate function of the given function.\n * @param {function(Token):boolean} f - The function to negate.\n * @returns {function(Token):boolean} Negated function.\n */\nfunction negate(f) {\n    return negate0.bind(f)\n}\n\n/**\n * Checks if the given token is a PunctuatorToken with the given value\n * @param {Token} token - The token to check.\n * @param {string} value - The value to check.\n * @returns {boolean} `true` if the token is a PunctuatorToken with the given value.\n */\nfunction isPunctuatorTokenWithValue(token, value) {\n    return token.type === \"Punctuator\" && token.value === value\n}\n\n/**\n * Checks if the given token is an arrow token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is an arrow token.\n */\nexport function isArrowToken(token) {\n    return isPunctuatorTokenWithValue(token, \"=>\")\n}\n\n/**\n * Checks if the given token is a comma token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a comma token.\n */\nexport function isCommaToken(token) {\n    return isPunctuatorTokenWithValue(token, \",\")\n}\n\n/**\n * Checks if the given token is a semicolon token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a semicolon token.\n */\nexport function isSemicolonToken(token) {\n    return isPunctuatorTokenWithValue(token, \";\")\n}\n\n/**\n * Checks if the given token is a colon token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a colon token.\n */\nexport function isColonToken(token) {\n    return isPunctuatorTokenWithValue(token, \":\")\n}\n\n/**\n * Checks if the given token is an opening parenthesis token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is an opening parenthesis token.\n */\nexport function isOpeningParenToken(token) {\n    return isPunctuatorTokenWithValue(token, \"(\")\n}\n\n/**\n * Checks if the given token is a closing parenthesis token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a closing parenthesis token.\n */\nexport function isClosingParenToken(token) {\n    return isPunctuatorTokenWithValue(token, \")\")\n}\n\n/**\n * Checks if the given token is an opening square bracket token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is an opening square bracket token.\n */\nexport function isOpeningBracketToken(token) {\n    return isPunctuatorTokenWithValue(token, \"[\")\n}\n\n/**\n * Checks if the given token is a closing square bracket token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a closing square bracket token.\n */\nexport function isClosingBracketToken(token) {\n    return isPunctuatorTokenWithValue(token, \"]\")\n}\n\n/**\n * Checks if the given token is an opening brace token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is an opening brace token.\n */\nexport function isOpeningBraceToken(token) {\n    return isPunctuatorTokenWithValue(token, \"{\")\n}\n\n/**\n * Checks if the given token is a closing brace token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a closing brace token.\n */\nexport function isClosingBraceToken(token) {\n    return isPunctuatorTokenWithValue(token, \"}\")\n}\n\n/**\n * Checks if the given token is a comment token or not.\n * @param {Token} token - The token to check.\n * @returns {boolean} `true` if the token is a comment token.\n */\nexport function isCommentToken(token) {\n    return [\"Block\", \"Line\", \"Shebang\"].includes(token.type)\n}\n\nexport const isNotArrowToken = negate(isArrowToken)\nexport const isNotCommaToken = negate(isCommaToken)\nexport const isNotSemicolonToken = negate(isSemicolonToken)\nexport const isNotColonToken = negate(isColonToken)\nexport const isNotOpeningParenToken = negate(isOpeningParenToken)\nexport const isNotClosingParenToken = negate(isClosingParenToken)\nexport const isNotOpeningBracketToken = negate(isOpeningBracketToken)\nexport const isNotClosingBracketToken = negate(isClosingBracketToken)\nexport const isNotOpeningBraceToken = negate(isOpeningBraceToken)\nexport const isNotClosingBraceToken = negate(isClosingBraceToken)\nexport const isNotCommentToken = negate(isCommentToken)\n", "import { isArrowToken, isOpening<PERSON>arenToken } from \"./token-predicate\"\n\n/**\n * Get the `(` token of the given function node.\n * @param {Node} node - The function node to get.\n * @param {SourceCode} sourceCode - The source code object to get tokens.\n * @returns {Token} `(` token.\n */\nfunction getOpeningParenOfParams(node, sourceCode) {\n    return node.id\n        ? sourceCode.getTokenAfter(node.id, isOpeningParenToken)\n        : sourceCode.getFirstToken(node, isOpeningParenToken)\n}\n\n/**\n * Get the location of the given function node for reporting.\n * @param {Node} node - The function node to get.\n * @param {SourceCode} sourceCode - The source code object to get tokens.\n * @returns {string} The location of the function node for reporting.\n */\nexport function getFunctionHeadLocation(node, sourceCode) {\n    const parent = node.parent\n    let start = null\n    let end = null\n\n    if (node.type === \"ArrowFunctionExpression\") {\n        const arrowToken = sourceCode.getTokenBefore(node.body, isArrowToken)\n\n        start = arrowToken.loc.start\n        end = arrowToken.loc.end\n    } else if (\n        parent.type === \"Property\" ||\n        parent.type === \"MethodDefinition\" ||\n        parent.type === \"PropertyDefinition\"\n    ) {\n        start = parent.loc.start\n        end = getOpeningParenOfParams(node, sourceCode).loc.start\n    } else {\n        start = node.loc.start\n        end = getOpeningParenOfParams(node, sourceCode).loc.start\n    }\n\n    return {\n        start: { ...start },\n        end: { ...end },\n    }\n}\n", "/* globals globalThis, global, self, window */\n\nimport { findVariable } from \"./find-variable\"\n\nconst globalObject =\n    typeof globalThis !== \"undefined\"\n        ? globalThis\n        : typeof self !== \"undefined\"\n        ? self\n        : typeof window !== \"undefined\"\n        ? window\n        : typeof global !== \"undefined\"\n        ? global\n        : {}\n\nconst builtinNames = Object.freeze(\n    new Set([\n        \"Array\",\n        \"ArrayBuffer\",\n        \"BigInt\",\n        \"BigInt64Array\",\n        \"BigUint64Array\",\n        \"Boolean\",\n        \"DataView\",\n        \"Date\",\n        \"decodeURI\",\n        \"decodeURIComponent\",\n        \"encodeURI\",\n        \"encodeURIComponent\",\n        \"escape\",\n        \"Float32Array\",\n        \"Float64Array\",\n        \"Function\",\n        \"Infinity\",\n        \"Int16Array\",\n        \"Int32Array\",\n        \"Int8Array\",\n        \"isFinite\",\n        \"isNaN\",\n        \"isPrototypeOf\",\n        \"JSON\",\n        \"Map\",\n        \"Math\",\n        \"NaN\",\n        \"Number\",\n        \"Object\",\n        \"parseFloat\",\n        \"parseInt\",\n        \"Promise\",\n        \"Proxy\",\n        \"Reflect\",\n        \"RegExp\",\n        \"Set\",\n        \"String\",\n        \"Symbol\",\n        \"Uint16Array\",\n        \"Uint32Array\",\n        \"Uint8Array\",\n        \"Uint8ClampedArray\",\n        \"undefined\",\n        \"unescape\",\n        \"WeakMap\",\n        \"WeakSet\",\n    ]),\n)\nconst callAllowed = new Set(\n    [\n        Array.isArray,\n        typeof BigInt === \"function\" ? BigInt : undefined,\n        Boolean,\n        Date,\n        Date.parse,\n        decodeURI,\n        decodeURIComponent,\n        encodeURI,\n        encodeURIComponent,\n        escape,\n        isFinite,\n        isNaN,\n        isPrototypeOf,\n        ...Object.getOwnPropertyNames(Math)\n            .map((k) => Math[k])\n            .filter((f) => typeof f === \"function\"),\n        Number,\n        Number.isFinite,\n        Number.isNaN,\n        Number.parseFloat,\n        Number.parseInt,\n        Object,\n        Object.entries,\n        Object.is,\n        Object.isExtensible,\n        Object.isFrozen,\n        Object.isSealed,\n        Object.keys,\n        Object.values,\n        parseFloat,\n        parseInt,\n        RegExp,\n        String,\n        String.fromCharCode,\n        String.fromCodePoint,\n        String.raw,\n        Symbol.for,\n        Symbol.keyFor,\n        unescape,\n    ].filter((f) => typeof f === \"function\"),\n)\nconst callPassThrough = new Set([\n    Object.freeze,\n    Object.preventExtensions,\n    Object.seal,\n])\n\n/**\n * Get the property descriptor.\n * @param {object} object The object to get.\n * @param {string|number|symbol} name The property name to get.\n */\nfunction getPropertyDescriptor(object, name) {\n    let x = object\n    while ((typeof x === \"object\" || typeof x === \"function\") && x !== null) {\n        const d = Object.getOwnPropertyDescriptor(x, name)\n        if (d) {\n            return d\n        }\n        x = Object.getPrototypeOf(x)\n    }\n    return null\n}\n\n/**\n * Check if a property is getter or not.\n * @param {object} object The object to check.\n * @param {string|number|symbol} name The property name to check.\n */\nfunction isGetter(object, name) {\n    const d = getPropertyDescriptor(object, name)\n    return d != null && d.get != null\n}\n\n/**\n * Get the element values of a given node list.\n * @param {Node[]} nodeList The node list to get values.\n * @param {Scope|undefined} initialScope The initial scope to find variables.\n * @returns {any[]|null} The value list if all nodes are constant. Otherwise, null.\n */\nfunction getElementValues(nodeList, initialScope) {\n    const valueList = []\n\n    for (let i = 0; i < nodeList.length; ++i) {\n        const elementNode = nodeList[i]\n\n        if (elementNode == null) {\n            valueList.length = i + 1\n        } else if (elementNode.type === \"SpreadElement\") {\n            const argument = getStaticValueR(elementNode.argument, initialScope)\n            if (argument == null) {\n                return null\n            }\n            valueList.push(...argument.value)\n        } else {\n            const element = getStaticValueR(elementNode, initialScope)\n            if (element == null) {\n                return null\n            }\n            valueList.push(element.value)\n        }\n    }\n\n    return valueList\n}\n\nconst operations = Object.freeze({\n    ArrayExpression(node, initialScope) {\n        const elements = getElementValues(node.elements, initialScope)\n        return elements != null ? { value: elements } : null\n    },\n\n    AssignmentExpression(node, initialScope) {\n        if (node.operator === \"=\") {\n            return getStaticValueR(node.right, initialScope)\n        }\n        return null\n    },\n\n    //eslint-disable-next-line complexity\n    BinaryExpression(node, initialScope) {\n        if (node.operator === \"in\" || node.operator === \"instanceof\") {\n            // Not supported.\n            return null\n        }\n\n        const left = getStaticValueR(node.left, initialScope)\n        const right = getStaticValueR(node.right, initialScope)\n        if (left != null && right != null) {\n            switch (node.operator) {\n                case \"==\":\n                    return { value: left.value == right.value } //eslint-disable-line eqeqeq\n                case \"!=\":\n                    return { value: left.value != right.value } //eslint-disable-line eqeqeq\n                case \"===\":\n                    return { value: left.value === right.value }\n                case \"!==\":\n                    return { value: left.value !== right.value }\n                case \"<\":\n                    return { value: left.value < right.value }\n                case \"<=\":\n                    return { value: left.value <= right.value }\n                case \">\":\n                    return { value: left.value > right.value }\n                case \">=\":\n                    return { value: left.value >= right.value }\n                case \"<<\":\n                    return { value: left.value << right.value }\n                case \">>\":\n                    return { value: left.value >> right.value }\n                case \">>>\":\n                    return { value: left.value >>> right.value }\n                case \"+\":\n                    return { value: left.value + right.value }\n                case \"-\":\n                    return { value: left.value - right.value }\n                case \"*\":\n                    return { value: left.value * right.value }\n                case \"/\":\n                    return { value: left.value / right.value }\n                case \"%\":\n                    return { value: left.value % right.value }\n                case \"**\":\n                    return { value: Math.pow(left.value, right.value) }\n                case \"|\":\n                    return { value: left.value | right.value }\n                case \"^\":\n                    return { value: left.value ^ right.value }\n                case \"&\":\n                    return { value: left.value & right.value }\n\n                // no default\n            }\n        }\n\n        return null\n    },\n\n    CallExpression(node, initialScope) {\n        const calleeNode = node.callee\n        const args = getElementValues(node.arguments, initialScope)\n\n        if (args != null) {\n            if (calleeNode.type === \"MemberExpression\") {\n                if (calleeNode.property.type === \"PrivateIdentifier\") {\n                    return null\n                }\n                const object = getStaticValueR(calleeNode.object, initialScope)\n                if (object != null) {\n                    if (\n                        object.value == null &&\n                        (object.optional || node.optional)\n                    ) {\n                        return { value: undefined, optional: true }\n                    }\n                    const property = getStaticPropertyNameValue(\n                        calleeNode,\n                        initialScope,\n                    )\n\n                    if (property != null) {\n                        const receiver = object.value\n                        const methodName = property.value\n                        if (callAllowed.has(receiver[methodName])) {\n                            return { value: receiver[methodName](...args) }\n                        }\n                        if (callPassThrough.has(receiver[methodName])) {\n                            return { value: args[0] }\n                        }\n                    }\n                }\n            } else {\n                const callee = getStaticValueR(calleeNode, initialScope)\n                if (callee != null) {\n                    if (callee.value == null && node.optional) {\n                        return { value: undefined, optional: true }\n                    }\n                    const func = callee.value\n                    if (callAllowed.has(func)) {\n                        return { value: func(...args) }\n                    }\n                    if (callPassThrough.has(func)) {\n                        return { value: args[0] }\n                    }\n                }\n            }\n        }\n\n        return null\n    },\n\n    ConditionalExpression(node, initialScope) {\n        const test = getStaticValueR(node.test, initialScope)\n        if (test != null) {\n            return test.value\n                ? getStaticValueR(node.consequent, initialScope)\n                : getStaticValueR(node.alternate, initialScope)\n        }\n        return null\n    },\n\n    ExpressionStatement(node, initialScope) {\n        return getStaticValueR(node.expression, initialScope)\n    },\n\n    Identifier(node, initialScope) {\n        if (initialScope != null) {\n            const variable = findVariable(initialScope, node)\n\n            // Built-in globals.\n            if (\n                variable != null &&\n                variable.defs.length === 0 &&\n                builtinNames.has(variable.name) &&\n                variable.name in globalObject\n            ) {\n                return { value: globalObject[variable.name] }\n            }\n\n            // Constants.\n            if (variable != null && variable.defs.length === 1) {\n                const def = variable.defs[0]\n                if (\n                    def.parent &&\n                    def.parent.kind === \"const\" &&\n                    // TODO(mysticatea): don't support destructuring here.\n                    def.node.id.type === \"Identifier\"\n                ) {\n                    return getStaticValueR(def.node.init, initialScope)\n                }\n            }\n        }\n        return null\n    },\n\n    Literal(node) {\n        //istanbul ignore if : this is implementation-specific behavior.\n        if ((node.regex != null || node.bigint != null) && node.value == null) {\n            // It was a RegExp/BigInt literal, but Node.js didn't support it.\n            return null\n        }\n        return { value: node.value }\n    },\n\n    LogicalExpression(node, initialScope) {\n        const left = getStaticValueR(node.left, initialScope)\n        if (left != null) {\n            if (\n                (node.operator === \"||\" && Boolean(left.value) === true) ||\n                (node.operator === \"&&\" && Boolean(left.value) === false) ||\n                (node.operator === \"??\" && left.value != null)\n            ) {\n                return left\n            }\n\n            const right = getStaticValueR(node.right, initialScope)\n            if (right != null) {\n                return right\n            }\n        }\n\n        return null\n    },\n\n    MemberExpression(node, initialScope) {\n        if (node.property.type === \"PrivateIdentifier\") {\n            return null\n        }\n        const object = getStaticValueR(node.object, initialScope)\n        if (object != null) {\n            if (object.value == null && (object.optional || node.optional)) {\n                return { value: undefined, optional: true }\n            }\n            const property = getStaticPropertyNameValue(node, initialScope)\n\n            if (property != null && !isGetter(object.value, property.value)) {\n                return { value: object.value[property.value] }\n            }\n        }\n        return null\n    },\n\n    ChainExpression(node, initialScope) {\n        const expression = getStaticValueR(node.expression, initialScope)\n        if (expression != null) {\n            return { value: expression.value }\n        }\n        return null\n    },\n\n    NewExpression(node, initialScope) {\n        const callee = getStaticValueR(node.callee, initialScope)\n        const args = getElementValues(node.arguments, initialScope)\n\n        if (callee != null && args != null) {\n            const Func = callee.value\n            if (callAllowed.has(Func)) {\n                return { value: new Func(...args) }\n            }\n        }\n\n        return null\n    },\n\n    ObjectExpression(node, initialScope) {\n        const object = {}\n\n        for (const propertyNode of node.properties) {\n            if (propertyNode.type === \"Property\") {\n                if (propertyNode.kind !== \"init\") {\n                    return null\n                }\n                const key = getStaticPropertyNameValue(\n                    propertyNode,\n                    initialScope,\n                )\n                const value = getStaticValueR(propertyNode.value, initialScope)\n                if (key == null || value == null) {\n                    return null\n                }\n                object[key.value] = value.value\n            } else if (\n                propertyNode.type === \"SpreadElement\" ||\n                propertyNode.type === \"ExperimentalSpreadProperty\"\n            ) {\n                const argument = getStaticValueR(\n                    propertyNode.argument,\n                    initialScope,\n                )\n                if (argument == null) {\n                    return null\n                }\n                Object.assign(object, argument.value)\n            } else {\n                return null\n            }\n        }\n\n        return { value: object }\n    },\n\n    SequenceExpression(node, initialScope) {\n        const last = node.expressions[node.expressions.length - 1]\n        return getStaticValueR(last, initialScope)\n    },\n\n    TaggedTemplateExpression(node, initialScope) {\n        const tag = getStaticValueR(node.tag, initialScope)\n        const expressions = getElementValues(\n            node.quasi.expressions,\n            initialScope,\n        )\n\n        if (tag != null && expressions != null) {\n            const func = tag.value\n            const strings = node.quasi.quasis.map((q) => q.value.cooked)\n            strings.raw = node.quasi.quasis.map((q) => q.value.raw)\n\n            if (func === String.raw) {\n                return { value: func(strings, ...expressions) }\n            }\n        }\n\n        return null\n    },\n\n    TemplateLiteral(node, initialScope) {\n        const expressions = getElementValues(node.expressions, initialScope)\n        if (expressions != null) {\n            let value = node.quasis[0].value.cooked\n            for (let i = 0; i < expressions.length; ++i) {\n                value += expressions[i]\n                value += node.quasis[i + 1].value.cooked\n            }\n            return { value }\n        }\n        return null\n    },\n\n    UnaryExpression(node, initialScope) {\n        if (node.operator === \"delete\") {\n            // Not supported.\n            return null\n        }\n        if (node.operator === \"void\") {\n            return { value: undefined }\n        }\n\n        const arg = getStaticValueR(node.argument, initialScope)\n        if (arg != null) {\n            switch (node.operator) {\n                case \"-\":\n                    return { value: -arg.value }\n                case \"+\":\n                    return { value: +arg.value } //eslint-disable-line no-implicit-coercion\n                case \"!\":\n                    return { value: !arg.value }\n                case \"~\":\n                    return { value: ~arg.value }\n                case \"typeof\":\n                    return { value: typeof arg.value }\n\n                // no default\n            }\n        }\n\n        return null\n    },\n})\n\n/**\n * Get the value of a given node if it's a static value.\n * @param {Node} node The node to get.\n * @param {Scope|undefined} initialScope The scope to start finding variable.\n * @returns {{value:any}|{value:undefined,optional?:true}|null} The static value of the node, or `null`.\n */\nfunction getStaticValueR(node, initialScope) {\n    if (node != null && Object.hasOwnProperty.call(operations, node.type)) {\n        return operations[node.type](node, initialScope)\n    }\n    return null\n}\n\n/**\n * Get the static value of property name from a MemberExpression node or a Property node.\n * @param {Node} node The node to get.\n * @param {Scope} [initialScope] The scope to start finding variable. Optional. If the node is a computed property node and this scope was given, this checks the computed property name by the `getStringIfConstant` function with the scope, and returns the value of it.\n * @returns {{value:any}|{value:undefined,optional?:true}|null} The static value of the property name of the node, or `null`.\n */\nfunction getStaticPropertyNameValue(node, initialScope) {\n    const nameNode = node.type === \"Property\" ? node.key : node.property\n\n    if (node.computed) {\n        return getStaticValueR(nameNode, initialScope)\n    }\n\n    if (nameNode.type === \"Identifier\") {\n        return { value: nameNode.name }\n    }\n\n    if (nameNode.type === \"Literal\") {\n        if (nameNode.bigint) {\n            return { value: nameNode.bigint }\n        }\n        return { value: String(nameNode.value) }\n    }\n\n    return null\n}\n\n/**\n * Get the value of a given node if it's a static value.\n * @param {Node} node The node to get.\n * @param {Scope} [initialScope] The scope to start finding variable. Optional. If this scope was given, this tries to resolve identifier references which are in the given node as much as possible.\n * @returns {{value:any}|{value:undefined,optional?:true}|null} The static value of the node, or `null`.\n */\nexport function getStaticValue(node, initialScope = null) {\n    try {\n        return getStaticValueR(node, initialScope)\n    } catch (_error) {\n        return null\n    }\n}\n", "import { getStaticValue } from \"./get-static-value\"\n\n/**\n * Get the value of a given node if it's a literal or a template literal.\n * @param {Node} node The node to get.\n * @param {Scope} [initialScope] The scope to start finding variable. Optional. If the node is an Identifier node and this scope was given, this checks the variable of the identifier, and returns the value of it if the variable is a constant.\n * @returns {string|null} The value of the node, or `null`.\n */\nexport function getStringIfConstant(node, initialScope = null) {\n    // Handle the literals that the platform doesn't support natively.\n    if (node && node.type === \"Literal\" && node.value === null) {\n        if (node.regex) {\n            return `/${node.regex.pattern}/${node.regex.flags}`\n        }\n        if (node.bigint) {\n            return node.bigint\n        }\n    }\n\n    const evaluated = getStaticValue(node, initialScope)\n    return evaluated && String(evaluated.value)\n}\n", "import { getStringIfConstant } from \"./get-string-if-constant\"\n\n/**\n * Get the property name from a MemberExpression node or a Property node.\n * @param {Node} node The node to get.\n * @param {Scope} [initialScope] The scope to start finding variable. Optional. If the node is a computed property node and this scope was given, this checks the computed property name by the `getStringIfConstant` function with the scope, and returns the value of it.\n * @returns {string|null} The property name of the node.\n */\nexport function getPropertyName(node, initialScope) {\n    switch (node.type) {\n        case \"MemberExpression\":\n            if (node.computed) {\n                return getStringIfConstant(node.property, initialScope)\n            }\n            if (node.property.type === \"PrivateIdentifier\") {\n                return null\n            }\n            return node.property.name\n\n        case \"Property\":\n        case \"MethodDefinition\":\n        case \"PropertyDefinition\":\n            if (node.computed) {\n                return getStringIfConstant(node.key, initialScope)\n            }\n            if (node.key.type === \"Literal\") {\n                return String(node.key.value)\n            }\n            if (node.key.type === \"PrivateIdentifier\") {\n                return null\n            }\n            return node.key.name\n\n        // no default\n    }\n\n    return null\n}\n", "import { getProperty<PERSON>ame } from \"./get-property-name\"\n\n/**\n * Get the name and kind of the given function node.\n * @param {ASTNode} node - The function node to get.\n * @param {SourceCode} [sourceCode] The source code object to get the code of computed property keys.\n * @returns {string} The name and kind of the function node.\n */\n// eslint-disable-next-line complexity\nexport function getFunctionNameWithKind(node, sourceCode) {\n    const parent = node.parent\n    const tokens = []\n    const isObjectMethod = parent.type === \"Property\" && parent.value === node\n    const isClassMethod =\n        parent.type === \"MethodDefinition\" && parent.value === node\n    const isClassFieldMethod =\n        parent.type === \"PropertyDefinition\" && parent.value === node\n\n    // Modifiers.\n    if (isClassMethod || isClassFieldMethod) {\n        if (parent.static) {\n            tokens.push(\"static\")\n        }\n        if (parent.key.type === \"PrivateIdentifier\") {\n            tokens.push(\"private\")\n        }\n    }\n    if (node.async) {\n        tokens.push(\"async\")\n    }\n    if (node.generator) {\n        tokens.push(\"generator\")\n    }\n\n    // Kinds.\n    if (isObjectMethod || isClassMethod) {\n        if (parent.kind === \"constructor\") {\n            return \"constructor\"\n        }\n        if (parent.kind === \"get\") {\n            tokens.push(\"getter\")\n        } else if (parent.kind === \"set\") {\n            tokens.push(\"setter\")\n        } else {\n            tokens.push(\"method\")\n        }\n    } else if (isClassFieldMethod) {\n        tokens.push(\"method\")\n    } else {\n        if (node.type === \"ArrowFunctionExpression\") {\n            tokens.push(\"arrow\")\n        }\n        tokens.push(\"function\")\n    }\n\n    // Names.\n    if (isObjectMethod || isClassMethod || isClassFieldMethod) {\n        if (parent.key.type === \"PrivateIdentifier\") {\n            tokens.push(`#${parent.key.name}`)\n        } else {\n            const name = getPropertyName(parent)\n            if (name) {\n                tokens.push(`'${name}'`)\n            } else if (sourceCode) {\n                const keyText = sourceCode.getText(parent.key)\n                if (!keyText.includes(\"\\n\")) {\n                    tokens.push(`[${keyText}]`)\n                }\n            }\n        }\n    } else if (node.id) {\n        tokens.push(`'${node.id.name}'`)\n    } else if (\n        parent.type === \"VariableDeclarator\" &&\n        parent.id &&\n        parent.id.type === \"Identifier\"\n    ) {\n        tokens.push(`'${parent.id.name}'`)\n    } else if (\n        (parent.type === \"AssignmentExpression\" ||\n            parent.type === \"AssignmentPattern\") &&\n        parent.left &&\n        parent.left.type === \"Identifier\"\n    ) {\n        tokens.push(`'${parent.left.name}'`)\n    }\n\n    return tokens.join(\" \")\n}\n", "import evk from \"eslint-visitor-keys\"\n\nconst typeConversionBinaryOps = Object.freeze(\n    new Set([\n        \"==\",\n        \"!=\",\n        \"<\",\n        \"<=\",\n        \">\",\n        \">=\",\n        \"<<\",\n        \">>\",\n        \">>>\",\n        \"+\",\n        \"-\",\n        \"*\",\n        \"/\",\n        \"%\",\n        \"|\",\n        \"^\",\n        \"&\",\n        \"in\",\n    ]),\n)\nconst typeConversionUnaryOps = Object.freeze(new Set([\"-\", \"+\", \"!\", \"~\"]))\n\n/**\n * Check whether the given value is an ASTNode or not.\n * @param {any} x The value to check.\n * @returns {boolean} `true` if the value is an ASTNode.\n */\nfunction isNode(x) {\n    return x !== null && typeof x === \"object\" && typeof x.type === \"string\"\n}\n\nconst visitor = Object.freeze(\n    Object.assign(Object.create(null), {\n        $visit(node, options, visitorKeys) {\n            const { type } = node\n\n            if (typeof this[type] === \"function\") {\n                return this[type](node, options, visitorKeys)\n            }\n\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n\n        $visitChildren(node, options, visitorKeys) {\n            const { type } = node\n\n            for (const key of visitorKeys[type] || evk.getKeys(node)) {\n                const value = node[key]\n\n                if (Array.isArray(value)) {\n                    for (const element of value) {\n                        if (\n                            isNode(element) &&\n                            this.$visit(element, options, visitorKeys)\n                        ) {\n                            return true\n                        }\n                    }\n                } else if (\n                    isNode(value) &&\n                    this.$visit(value, options, visitorKeys)\n                ) {\n                    return true\n                }\n            }\n\n            return false\n        },\n\n        ArrowFunctionExpression() {\n            return false\n        },\n        AssignmentExpression() {\n            return true\n        },\n        AwaitExpression() {\n            return true\n        },\n        BinaryExpression(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                typeConversionBinaryOps.has(node.operator) &&\n                (node.left.type !== \"Literal\" || node.right.type !== \"Literal\")\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        CallExpression() {\n            return true\n        },\n        FunctionExpression() {\n            return false\n        },\n        ImportExpression() {\n            return true\n        },\n        MemberExpression(node, options, visitorKeys) {\n            if (options.considerGetters) {\n                return true\n            }\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.property.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        MethodDefinition(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.key.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        NewExpression() {\n            return true\n        },\n        Property(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.key.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        PropertyDefinition(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.key.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        UnaryExpression(node, options, visitorKeys) {\n            if (node.operator === \"delete\") {\n                return true\n            }\n            if (\n                options.considerImplicitTypeConversion &&\n                typeConversionUnaryOps.has(node.operator) &&\n                node.argument.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        UpdateExpression() {\n            return true\n        },\n        YieldExpression() {\n            return true\n        },\n    }),\n)\n\n/**\n * Check whether a given node has any side effect or not.\n * @param {Node} node The node to get.\n * @param {SourceCode} sourceCode The source code object.\n * @param {object} [options] The option object.\n * @param {boolean} [options.considerGetters=false] If `true` then it considers member accesses as the node which has side effects.\n * @param {boolean} [options.considerImplicitTypeConversion=false] If `true` then it considers implicit type conversion as the node which has side effects.\n * @param {object} [options.visitorKeys=evk.KEYS] The keys to traverse nodes. Use `context.getSourceCode().visitorKeys`.\n * @returns {boolean} `true` if the node has a certain side effect.\n */\nexport function hasSideEffect(\n    node,\n    sourceCode,\n    { considerGetters = false, considerImplicitTypeConversion = false } = {},\n) {\n    return visitor.$visit(\n        node,\n        { considerGetters, considerImplicitTypeConversion },\n        sourceCode.visitorKeys || evk.KEYS,\n    )\n}\n", "import { isClosingParenToken, isOpeningParenToken } from \"./token-predicate\"\n\n/**\n * Get the left parenthesis of the parent node syntax if it exists.\n * E.g., `if (a) {}` then the `(`.\n * @param {Node} node The AST node to check.\n * @param {SourceCode} sourceCode The source code object to get tokens.\n * @returns {Token|null} The left parenthesis of the parent node syntax\n */\nfunction getParentSyntaxParen(node, sourceCode) {\n    const parent = node.parent\n\n    switch (parent.type) {\n        case \"CallExpression\":\n        case \"NewExpression\":\n            if (parent.arguments.length === 1 && parent.arguments[0] === node) {\n                return sourceCode.getTokenAfter(\n                    parent.callee,\n                    isOpeningParenToken,\n                )\n            }\n            return null\n\n        case \"DoWhileStatement\":\n            if (parent.test === node) {\n                return sourceCode.getTokenAfter(\n                    parent.body,\n                    isOpeningParenToken,\n                )\n            }\n            return null\n\n        case \"IfStatement\":\n        case \"WhileStatement\":\n            if (parent.test === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        case \"ImportExpression\":\n            if (parent.source === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        case \"SwitchStatement\":\n            if (parent.discriminant === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        case \"WithStatement\":\n            if (parent.object === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        default:\n            return null\n    }\n}\n\n/**\n * Check whether a given node is parenthesized or not.\n * @param {number} times The number of parantheses.\n * @param {Node} node The AST node to check.\n * @param {SourceCode} sourceCode The source code object to get tokens.\n * @returns {boolean} `true` if the node is parenthesized the given times.\n */\n/**\n * Check whether a given node is parenthesized or not.\n * @param {Node} node The AST node to check.\n * @param {SourceCode} sourceCode The source code object to get tokens.\n * @returns {boolean} `true` if the node is parenthesized.\n */\nexport function isParenthesized(\n    timesOrNode,\n    nodeOrSourceCode,\n    optionalSourceCode,\n) {\n    let times, node, sourceCode, maybeLeftParen, maybeRightParen\n    if (typeof timesOrNode === \"number\") {\n        times = timesOrNode | 0\n        node = nodeOrSourceCode\n        sourceCode = optionalSourceCode\n        if (!(times >= 1)) {\n            throw new TypeError(\"'times' should be a positive integer.\")\n        }\n    } else {\n        times = 1\n        node = timesOrNode\n        sourceCode = nodeOrSourceCode\n    }\n\n    if (\n        node == null ||\n        // `CatchClause.param` can't be parenthesized, example `try {} catch (error) {}`\n        (node.parent.type === \"CatchClause\" && node.parent.param === node)\n    ) {\n        return false\n    }\n\n    maybeLeftParen = maybeRightParen = node\n    do {\n        maybeLeftParen = sourceCode.getTokenBefore(maybeLeftParen)\n        maybeRightParen = sourceCode.getTokenAfter(maybeRightParen)\n    } while (\n        maybeLeftParen != null &&\n        maybeRightParen != null &&\n        isOpeningParenToken(maybeLeftParen) &&\n        isClosingParenToken(maybeRightParen) &&\n        // Avoid false positive such as `if (a) {}`\n        maybeLeftParen !== getParentSyntaxParen(node, sourceCode) &&\n        --times > 0\n    )\n\n    return times === 0\n}\n", "/**\n * <AUTHOR> <https://github.com/mysticatea>\n * See LICENSE file in root directory for full license.\n */\n\nconst placeholder = /\\$(?:[$&`']|[1-9][0-9]?)/gu\n\n/** @type {WeakMap<PatternMatcher, {pattern:RegExp,escaped:boolean}>} */\nconst internal = new WeakMap()\n\n/**\n * Check whether a given character is escaped or not.\n * @param {string} str The string to check.\n * @param {number} index The location of the character to check.\n * @returns {boolean} `true` if the character is escaped.\n */\nfunction isEscaped(str, index) {\n    let escaped = false\n    for (let i = index - 1; i >= 0 && str.charCodeAt(i) === 0x5c; --i) {\n        escaped = !escaped\n    }\n    return escaped\n}\n\n/**\n * Replace a given string by a given matcher.\n * @param {PatternMatcher} matcher The pattern matcher.\n * @param {string} str The string to be replaced.\n * @param {string} replacement The new substring to replace each matched part.\n * @returns {string} The replaced string.\n */\nfunction replaceS(matcher, str, replacement) {\n    const chunks = []\n    let index = 0\n\n    /** @type {RegExpExecArray} */\n    let match = null\n\n    /**\n     * @param {string} key The placeholder.\n     * @returns {string} The replaced string.\n     */\n    function replacer(key) {\n        switch (key) {\n            case \"$$\":\n                return \"$\"\n            case \"$&\":\n                return match[0]\n            case \"$`\":\n                return str.slice(0, match.index)\n            case \"$'\":\n                return str.slice(match.index + match[0].length)\n            default: {\n                const i = key.slice(1)\n                if (i in match) {\n                    return match[i]\n                }\n                return key\n            }\n        }\n    }\n\n    for (match of matcher.execAll(str)) {\n        chunks.push(str.slice(index, match.index))\n        chunks.push(replacement.replace(placeholder, replacer))\n        index = match.index + match[0].length\n    }\n    chunks.push(str.slice(index))\n\n    return chunks.join(\"\")\n}\n\n/**\n * Replace a given string by a given matcher.\n * @param {PatternMatcher} matcher The pattern matcher.\n * @param {string} str The string to be replaced.\n * @param {(...strs[])=>string} replace The function to replace each matched part.\n * @returns {string} The replaced string.\n */\nfunction replaceF(matcher, str, replace) {\n    const chunks = []\n    let index = 0\n\n    for (const match of matcher.execAll(str)) {\n        chunks.push(str.slice(index, match.index))\n        chunks.push(String(replace(...match, match.index, match.input)))\n        index = match.index + match[0].length\n    }\n    chunks.push(str.slice(index))\n\n    return chunks.join(\"\")\n}\n\n/**\n * The class to find patterns as considering escape sequences.\n */\nexport class PatternMatcher {\n    /**\n     * Initialize this matcher.\n     * @param {RegExp} pattern The pattern to match.\n     * @param {{escaped:boolean}} options The options.\n     */\n    constructor(pattern, { escaped = false } = {}) {\n        if (!(pattern instanceof RegExp)) {\n            throw new TypeError(\"'pattern' should be a RegExp instance.\")\n        }\n        if (!pattern.flags.includes(\"g\")) {\n            throw new Error(\"'pattern' should contains 'g' flag.\")\n        }\n\n        internal.set(this, {\n            pattern: new RegExp(pattern.source, pattern.flags),\n            escaped: Boolean(escaped),\n        })\n    }\n\n    /**\n     * Find the pattern in a given string.\n     * @param {string} str The string to find.\n     * @returns {IterableIterator<RegExpExecArray>} The iterator which iterate the matched information.\n     */\n    *execAll(str) {\n        const { pattern, escaped } = internal.get(this)\n        let match = null\n        let lastIndex = 0\n\n        pattern.lastIndex = 0\n        while ((match = pattern.exec(str)) != null) {\n            if (escaped || !isEscaped(str, match.index)) {\n                lastIndex = pattern.lastIndex\n                yield match\n                pattern.lastIndex = lastIndex\n            }\n        }\n    }\n\n    /**\n     * Check whether the pattern is found in a given string.\n     * @param {string} str The string to check.\n     * @returns {boolean} `true` if the pattern was found in the string.\n     */\n    test(str) {\n        const it = this.execAll(str)\n        const ret = it.next()\n        return !ret.done\n    }\n\n    /**\n     * Replace a given string.\n     * @param {string} str The string to be replaced.\n     * @param {(string|((...strs:string[])=>string))} replacer The string or function to replace. This is the same as the 2nd argument of `String.prototype.replace`.\n     * @returns {string} The replaced string.\n     */\n    [Symbol.replace](str, replacer) {\n        return typeof replacer === \"function\"\n            ? replaceF(this, String(str), replacer)\n            : replaceS(this, String(str), String(replacer))\n    }\n}\n", "import { findVariable } from \"./find-variable\"\nimport { getPropertyName } from \"./get-property-name\"\nimport { getStringIfConstant } from \"./get-string-if-constant\"\n\nconst IMPORT_TYPE = /^(?:Import|Export(?:All|Default|Named))Declaration$/u\nconst has = Function.call.bind(Object.hasOwnProperty)\n\nexport const READ = Symbol(\"read\")\nexport const CALL = Symbol(\"call\")\nexport const CONSTRUCT = Symbol(\"construct\")\nexport const ESM = Symbol(\"esm\")\n\nconst requireCall = { require: { [CALL]: true } }\n\n/**\n * Check whether a given variable is modified or not.\n * @param {Variable} variable The variable to check.\n * @returns {boolean} `true` if the variable is modified.\n */\nfunction isModifiedGlobal(variable) {\n    return (\n        variable == null ||\n        variable.defs.length !== 0 ||\n        variable.references.some((r) => r.isWrite())\n    )\n}\n\n/**\n * Check if the value of a given node is passed through to the parent syntax as-is.\n * For example, `a` and `b` in (`a || b` and `c ? a : b`) are passed through.\n * @param {Node} node A node to check.\n * @returns {boolean} `true` if the node is passed through.\n */\nfunction isPassThrough(node) {\n    const parent = node.parent\n\n    switch (parent && parent.type) {\n        case \"ConditionalExpression\":\n            return parent.consequent === node || parent.alternate === node\n        case \"LogicalExpression\":\n            return true\n        case \"SequenceExpression\":\n            return parent.expressions[parent.expressions.length - 1] === node\n        case \"ChainExpression\":\n            return true\n\n        default:\n            return false\n    }\n}\n\n/**\n * The reference tracker.\n */\nexport class ReferenceTracker {\n    /**\n     * Initialize this tracker.\n     * @param {Scope} globalScope The global scope.\n     * @param {object} [options] The options.\n     * @param {\"legacy\"|\"strict\"} [options.mode=\"strict\"] The mode to determine the ImportDeclaration's behavior for CJS modules.\n     * @param {string[]} [options.globalObjectNames=[\"global\",\"globalThis\",\"self\",\"window\"]] The variable names for Global Object.\n     */\n    constructor(\n        globalScope,\n        {\n            mode = \"strict\",\n            globalObjectNames = [\"global\", \"globalThis\", \"self\", \"window\"],\n        } = {},\n    ) {\n        this.variableStack = []\n        this.globalScope = globalScope\n        this.mode = mode\n        this.globalObjectNames = globalObjectNames.slice(0)\n    }\n\n    /**\n     * Iterate the references of global variables.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *iterateGlobalReferences(traceMap) {\n        for (const key of Object.keys(traceMap)) {\n            const nextTraceMap = traceMap[key]\n            const path = [key]\n            const variable = this.globalScope.set.get(key)\n\n            if (isModifiedGlobal(variable)) {\n                continue\n            }\n\n            yield* this._iterateVariableReferences(\n                variable,\n                path,\n                nextTraceMap,\n                true,\n            )\n        }\n\n        for (const key of this.globalObjectNames) {\n            const path = []\n            const variable = this.globalScope.set.get(key)\n\n            if (isModifiedGlobal(variable)) {\n                continue\n            }\n\n            yield* this._iterateVariableReferences(\n                variable,\n                path,\n                traceMap,\n                false,\n            )\n        }\n    }\n\n    /**\n     * Iterate the references of CommonJS modules.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *iterateCjsReferences(traceMap) {\n        for (const { node } of this.iterateGlobalReferences(requireCall)) {\n            const key = getStringIfConstant(node.arguments[0])\n            if (key == null || !has(traceMap, key)) {\n                continue\n            }\n\n            const nextTraceMap = traceMap[key]\n            const path = [key]\n\n            if (nextTraceMap[READ]) {\n                yield {\n                    node,\n                    path,\n                    type: READ,\n                    info: nextTraceMap[READ],\n                }\n            }\n            yield* this._iteratePropertyReferences(node, path, nextTraceMap)\n        }\n    }\n\n    /**\n     * Iterate the references of ES modules.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *iterateEsmReferences(traceMap) {\n        const programNode = this.globalScope.block\n\n        for (const node of programNode.body) {\n            if (!IMPORT_TYPE.test(node.type) || node.source == null) {\n                continue\n            }\n            const moduleId = node.source.value\n\n            if (!has(traceMap, moduleId)) {\n                continue\n            }\n            const nextTraceMap = traceMap[moduleId]\n            const path = [moduleId]\n\n            if (nextTraceMap[READ]) {\n                yield { node, path, type: READ, info: nextTraceMap[READ] }\n            }\n\n            if (node.type === \"ExportAllDeclaration\") {\n                for (const key of Object.keys(nextTraceMap)) {\n                    const exportTraceMap = nextTraceMap[key]\n                    if (exportTraceMap[READ]) {\n                        yield {\n                            node,\n                            path: path.concat(key),\n                            type: READ,\n                            info: exportTraceMap[READ],\n                        }\n                    }\n                }\n            } else {\n                for (const specifier of node.specifiers) {\n                    const esm = has(nextTraceMap, ESM)\n                    const it = this._iterateImportReferences(\n                        specifier,\n                        path,\n                        esm\n                            ? nextTraceMap\n                            : this.mode === \"legacy\"\n                            ? { default: nextTraceMap, ...nextTraceMap }\n                            : { default: nextTraceMap },\n                    )\n\n                    if (esm) {\n                        yield* it\n                    } else {\n                        for (const report of it) {\n                            report.path = report.path.filter(exceptDefault)\n                            if (\n                                report.path.length >= 2 ||\n                                report.type !== READ\n                            ) {\n                                yield report\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    /**\n     * Iterate the references for a given variable.\n     * @param {Variable} variable The variable to iterate that references.\n     * @param {string[]} path The current path.\n     * @param {object} traceMap The trace map.\n     * @param {boolean} shouldReport = The flag to report those references.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *_iterateVariableReferences(variable, path, traceMap, shouldReport) {\n        if (this.variableStack.includes(variable)) {\n            return\n        }\n        this.variableStack.push(variable)\n        try {\n            for (const reference of variable.references) {\n                if (!reference.isRead()) {\n                    continue\n                }\n                const node = reference.identifier\n\n                if (shouldReport && traceMap[READ]) {\n                    yield { node, path, type: READ, info: traceMap[READ] }\n                }\n                yield* this._iteratePropertyReferences(node, path, traceMap)\n            }\n        } finally {\n            this.variableStack.pop()\n        }\n    }\n\n    /**\n     * Iterate the references for a given AST node.\n     * @param rootNode The AST node to iterate references.\n     * @param {string[]} path The current path.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    //eslint-disable-next-line complexity\n    *_iteratePropertyReferences(rootNode, path, traceMap) {\n        let node = rootNode\n        while (isPassThrough(node)) {\n            node = node.parent\n        }\n\n        const parent = node.parent\n        if (parent.type === \"MemberExpression\") {\n            if (parent.object === node) {\n                const key = getPropertyName(parent)\n                if (key == null || !has(traceMap, key)) {\n                    return\n                }\n\n                path = path.concat(key) //eslint-disable-line no-param-reassign\n                const nextTraceMap = traceMap[key]\n                if (nextTraceMap[READ]) {\n                    yield {\n                        node: parent,\n                        path,\n                        type: READ,\n                        info: nextTraceMap[READ],\n                    }\n                }\n                yield* this._iteratePropertyReferences(\n                    parent,\n                    path,\n                    nextTraceMap,\n                )\n            }\n            return\n        }\n        if (parent.type === \"CallExpression\") {\n            if (parent.callee === node && traceMap[CALL]) {\n                yield { node: parent, path, type: CALL, info: traceMap[CALL] }\n            }\n            return\n        }\n        if (parent.type === \"NewExpression\") {\n            if (parent.callee === node && traceMap[CONSTRUCT]) {\n                yield {\n                    node: parent,\n                    path,\n                    type: CONSTRUCT,\n                    info: traceMap[CONSTRUCT],\n                }\n            }\n            return\n        }\n        if (parent.type === \"AssignmentExpression\") {\n            if (parent.right === node) {\n                yield* this._iterateLhsReferences(parent.left, path, traceMap)\n                yield* this._iteratePropertyReferences(parent, path, traceMap)\n            }\n            return\n        }\n        if (parent.type === \"AssignmentPattern\") {\n            if (parent.right === node) {\n                yield* this._iterateLhsReferences(parent.left, path, traceMap)\n            }\n            return\n        }\n        if (parent.type === \"VariableDeclarator\") {\n            if (parent.init === node) {\n                yield* this._iterateLhsReferences(parent.id, path, traceMap)\n            }\n        }\n    }\n\n    /**\n     * Iterate the references for a given Pattern node.\n     * @param {Node} patternNode The Pattern node to iterate references.\n     * @param {string[]} path The current path.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *_iterateLhsReferences(patternNode, path, traceMap) {\n        if (patternNode.type === \"Identifier\") {\n            const variable = findVariable(this.globalScope, patternNode)\n            if (variable != null) {\n                yield* this._iterateVariableReferences(\n                    variable,\n                    path,\n                    traceMap,\n                    false,\n                )\n            }\n            return\n        }\n        if (patternNode.type === \"ObjectPattern\") {\n            for (const property of patternNode.properties) {\n                const key = getPropertyName(property)\n\n                if (key == null || !has(traceMap, key)) {\n                    continue\n                }\n\n                const nextPath = path.concat(key)\n                const nextTraceMap = traceMap[key]\n                if (nextTraceMap[READ]) {\n                    yield {\n                        node: property,\n                        path: nextPath,\n                        type: READ,\n                        info: nextTraceMap[READ],\n                    }\n                }\n                yield* this._iterateLhsReferences(\n                    property.value,\n                    nextPath,\n                    nextTraceMap,\n                )\n            }\n            return\n        }\n        if (patternNode.type === \"AssignmentPattern\") {\n            yield* this._iterateLhsReferences(patternNode.left, path, traceMap)\n        }\n    }\n\n    /**\n     * Iterate the references for a given ModuleSpecifier node.\n     * @param {Node} specifierNode The ModuleSpecifier node to iterate references.\n     * @param {string[]} path The current path.\n     * @param {object} traceMap The trace map.\n     * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.\n     */\n    *_iterateImportReferences(specifierNode, path, traceMap) {\n        const type = specifierNode.type\n\n        if (type === \"ImportSpecifier\" || type === \"ImportDefaultSpecifier\") {\n            const key =\n                type === \"ImportDefaultSpecifier\"\n                    ? \"default\"\n                    : specifierNode.imported.name\n            if (!has(traceMap, key)) {\n                return\n            }\n\n            path = path.concat(key) //eslint-disable-line no-param-reassign\n            const nextTraceMap = traceMap[key]\n            if (nextTraceMap[READ]) {\n                yield {\n                    node: specifierNode,\n                    path,\n                    type: READ,\n                    info: nextTraceMap[READ],\n                }\n            }\n            yield* this._iterateVariableReferences(\n                findVariable(this.globalScope, specifierNode.local),\n                path,\n                nextTraceMap,\n                false,\n            )\n\n            return\n        }\n\n        if (type === \"ImportNamespaceSpecifier\") {\n            yield* this._iterateVariableReferences(\n                findVariable(this.globalScope, specifierNode.local),\n                path,\n                traceMap,\n                false,\n            )\n            return\n        }\n\n        if (type === \"ExportSpecifier\") {\n            const key = specifierNode.local.name\n            if (!has(traceMap, key)) {\n                return\n            }\n\n            path = path.concat(key) //eslint-disable-line no-param-reassign\n            const nextTraceMap = traceMap[key]\n            if (nextTraceMap[READ]) {\n                yield {\n                    node: specifierNode,\n                    path,\n                    type: READ,\n                    info: nextTraceMap[READ],\n                }\n            }\n        }\n    }\n}\n\nReferenceTracker.READ = READ\nReferenceTracker.CALL = CALL\nReferenceTracker.CONSTRUCT = CONSTRUCT\nReferenceTracker.ESM = ESM\n\n/**\n * This is a predicate function for Array#filter.\n * @param {string} name A name part.\n * @param {number} index The index of the name.\n * @returns {boolean} `false` if it's default.\n */\nfunction exceptDefault(name, index) {\n    return !(index === 1 && name === \"default\")\n}\n", "import { findVariable } from \"./find-variable\"\nimport { getFunctionHeadLocation } from \"./get-function-head-location\"\nimport { getFunctionNameWithKind } from \"./get-function-name-with-kind\"\nimport { getInnermostScope } from \"./get-innermost-scope\"\nimport { getPropertyName } from \"./get-property-name\"\nimport { getStaticValue } from \"./get-static-value\"\nimport { getStringIfConstant } from \"./get-string-if-constant\"\nimport { hasSideEffect } from \"./has-side-effect\"\nimport { isParenthesized } from \"./is-parenthesized\"\nimport { PatternMatcher } from \"./pattern-matcher\"\nimport {\n    CALL,\n    CONSTRUCT,\n    ESM,\n    READ,\n    ReferenceTracker,\n} from \"./reference-tracker\"\nimport {\n    isArrowToken,\n    isClosingBraceToken,\n    isClosingBracketToken,\n    isClosingParenToken,\n    isColonToken,\n    isCommaToken,\n    isCommentToken,\n    isNotArrowToken,\n    isNotClosingBraceToken,\n    isNotClosingBracketToken,\n    isNotClosing<PERSON>arenToken,\n    isNotColonToken,\n    isNotCommaToken,\n    isNotCommentToken,\n    isNotOpeningBraceToken,\n    isNotOpeningBracketToken,\n    isNotOpeningParenToken,\n    isNotSemicolonToken,\n    isOpeningBraceToken,\n    isOpeningBracketToken,\n    isOpeningParenToken,\n    isSemicolonToken,\n} from \"./token-predicate\"\n\nexport default {\n    CALL,\n    CONSTRUCT,\n    ESM,\n    findVariable,\n    getFunctionHeadLocation,\n    getFunctionNameWithKind,\n    getInnermostScope,\n    getPropertyName,\n    getStaticValue,\n    getStringIfConstant,\n    hasSideEffect,\n    isArrowToken,\n    isClosingBraceToken,\n    isClosingBracketToken,\n    isClosingParenToken,\n    isColonToken,\n    isCommaToken,\n    isCommentToken,\n    isNotArrowToken,\n    isNotClosingBraceToken,\n    isNotClosingBracketToken,\n    isNotClosingParenToken,\n    isNotColonToken,\n    isNotCommaToken,\n    isNotCommentToken,\n    isNotOpeningBraceToken,\n    isNotOpeningBracketToken,\n    isNotOpeningParenToken,\n    isNotSemicolonToken,\n    isOpeningBraceToken,\n    isOpeningBracketToken,\n    isOpeningParenToken,\n    isParenthesized,\n    isSemicolonToken,\n    PatternMatcher,\n    READ,\n    ReferenceTracker,\n}\nexport {\n    CALL,\n    CONSTRUCT,\n    ESM,\n    findVariable,\n    getFunctionHeadLocation,\n    getFunctionNameWithKind,\n    getInnermostScope,\n    getPropertyName,\n    getStaticValue,\n    getStringIfConstant,\n    hasSideEffect,\n    isArrowToken,\n    isClosingBraceToken,\n    isClosingBracketToken,\n    isClosingParenToken,\n    isColonToken,\n    isCommaToken,\n    isCommentToken,\n    isNotArrowToken,\n    isNotClosingBraceToken,\n    isNotClosingBracketToken,\n    isNotClosingParenToken,\n    isNotColonToken,\n    isNotCommaToken,\n    isNotCommentToken,\n    isNotOpeningBraceToken,\n    isNotOpeningBracketToken,\n    isNotOpeningParenToken,\n    isNotSemicolonToken,\n    isOpeningBraceToken,\n    isOpeningBracketToken,\n    isOpeningParenToken,\n    isParenthesized,\n    isSemicolonToken,\n    PatternMatcher,\n    READ,\n    ReferenceTracker,\n}\n", "import { findVariable } from \"eslint-utils\";\n\nexport const traverse = (context, node, visit, visited = new Set()) => {\n  if (visited.has(node)) {\n    return;\n  }\n\n  visited.add(node);\n  visit(node);\n\n  (context.sourceCode.visitorKeys[node.type] || [])\n    .map((key) => node[key])\n    // Some `visitorKeys` are optional, e.g. `IfStatement.alternate`.\n    .filter(Boolean)\n    // Can be an array, like `CallExpression.arguments`\n    .flatMap((child) => (Array.isArray(child) ? child : [child]))\n    // Can rarely be `null`, e.g. `ArrayPattern.elements[1]` when an element is skipped - `const [a, , b] = arr`\n    .filter(Boolean)\n    // Check it's a valid AST node\n    .filter((child) => typeof child.type === \"string\")\n    .forEach((child) => traverse(context, child, visit, visited));\n};\n\nconst getDownstreamIdentifiers = (context, rootNode) => {\n  const identifiers = [];\n  traverse(context, rootNode, (node) => {\n    if (node.type === \"Identifier\") {\n      identifiers.push(node);\n    }\n  });\n  return identifiers;\n};\n\nexport const getUpstreamVariables = (\n  context,\n  node,\n  filter,\n  visited = new Set(),\n) => {\n  if (visited.has(node)) {\n    return [];\n  }\n\n  visited.add(node);\n\n  const variable = findVariable(context.sourceCode.getScope(node), node);\n  if (!variable) {\n    // I think this only happens when:\n    // 1. There's genuinely no variable, i.e. `node` is a literal\n    // 2. Import statement is missing\n    // 3. ESLint globals are misconfigured\n    return [];\n  }\n\n  const upstreamVariables = variable.defs\n    .filter((def) => !!def.node.init)\n    .filter((def) => filter(def.node))\n    .flatMap((def) => getDownstreamIdentifiers(context, def.node.init))\n    .flatMap((identifier) =>\n      getUpstreamVariables(context, identifier, filter, visited),\n    );\n\n  // Ultimately return only leaf variables\n  return upstreamVariables.length === 0 ? [variable] : upstreamVariables;\n};\n\nexport const getDownstreamRefs = (context, node) =>\n  getDownstreamIdentifiers(context, node)\n    .map((identifier) => getRef(context, identifier))\n    .filter(Boolean);\n\nconst getRef = (context, identifier) =>\n  findVariable(\n    context.sourceCode.getScope(identifier),\n    identifier,\n  )?.references.find((ref) => ref.identifier === identifier);\n\nexport const getCallExpr = (ref, current = ref.identifier.parent) => {\n  if (current.type === \"CallExpression\") {\n    // We've reached the top - confirm that the ref is the (eventual) callee, as opposed to an argument.\n    let node = ref.identifier;\n    while (node.parent.type === \"MemberExpression\") {\n      node = node.parent;\n    }\n\n    if (current.callee === node) {\n      return current;\n    }\n  }\n\n  if (current.type === \"MemberExpression\") {\n    return getCallExpr(ref, current.parent);\n  }\n\n  return undefined;\n};\n\nexport const isIIFE = (node) =>\n  node.type === \"CallExpression\" &&\n  (node.callee.type === \"ArrowFunctionExpression\" ||\n    node.callee.type === \"FunctionExpression\");\n", "import {\n  traverse,\n  getUpstreamVariables,\n  getDownstreamRefs,\n  getCallExpr,\n  isIIFE,\n} from \"./ast.js\";\n\nexport const isReactFunctionalComponent = (node) =>\n  (node.type === \"FunctionDeclaration\" ||\n    (node.type === \"VariableDeclarator\" &&\n      (node.init.type === \"ArrowFunctionExpression\" ||\n        node.init.type === \"CallExpression\"))) &&\n  node.id.type === \"Identifier\" &&\n  node.id.name[0].toUpperCase() === node.id.name[0];\n\n// NOTE: Returns false for known pure HOCs -- `memo` and `forwardRef`.\n// TODO: Will not detect when they define the component normally and then export it wrapped in the HOC.\n// e.g. `const MyComponent = (props) => {...}; export default memo(MyComponent);`\nexport const isReactFunctionalHOC = (node) =>\n  node.type === \"VariableDeclarator\" &&\n  node.init &&\n  node.init.type === \"CallExpression\" &&\n  node.init.callee.type === \"Identifier\" &&\n  ![\"memo\", \"forwardRef\"].includes(node.init.callee.name) &&\n  node.init.arguments.length > 0 &&\n  (node.init.arguments[0].type === \"ArrowFunctionExpression\" ||\n    node.init.arguments[0].type === \"FunctionExpression\") &&\n  node.id.type === \"Identifier\" &&\n  node.id.name[0].toUpperCase() === node.id.name[0];\n\nexport const isCustomHook = (node) =>\n  (node.type === \"FunctionDeclaration\" ||\n    (node.type === \"VariableDeclarator\" &&\n      node.init &&\n      (node.init.type === \"ArrowFunctionExpression\" ||\n        node.init.type === \"FunctionExpression\"))) &&\n  node.id.type === \"Identifier\" &&\n  node.id.name.startsWith(\"use\") &&\n  node.id.name[3] === node.id.name[3].toUpperCase();\n\nexport const isUseState = (node) =>\n  node.type === \"VariableDeclarator\" &&\n  node.init &&\n  node.init.type === \"CallExpression\" &&\n  node.init.callee.name === \"useState\" &&\n  node.id.type === \"ArrayPattern\" &&\n  // Not sure its usecase, but may just have the setter\n  (node.id.elements.length === 1 || node.id.elements.length === 2) &&\n  node.id.elements.every((el) => {\n    // Apparently skipping the state element is a valid use.\n    // I suppose technically the state can still be read via setter callback.\n    return !el || el.type === \"Identifier\";\n  });\n\nexport const isUseEffect = (node) =>\n  node.type === \"CallExpression\" &&\n  ((node.callee.type === \"Identifier\" &&\n    (node.callee.name === \"useEffect\" ||\n      node.callee.name === \"useLayoutEffect\")) ||\n    (node.callee.type === \"MemberExpression\" &&\n      node.callee.object.name === \"React\" &&\n      (node.callee.property.name === \"useEffect\" ||\n        node.callee.property.name === \"useLayoutEffect\")));\n\nexport const getEffectFn = (node) => {\n  if (!isUseEffect(node) || node.arguments.length < 1) {\n    return undefined;\n  }\n\n  const effectFn = node.arguments[0];\n  if (\n    effectFn.type !== \"ArrowFunctionExpression\" &&\n    effectFn.type !== \"FunctionExpression\"\n  ) {\n    return undefined;\n  }\n\n  return effectFn;\n};\n\n// NOTE: When `MemberExpression` (even nested ones), a `Reference` is only the root object, not the function.\nexport const getEffectFnRefs = (context, node) => {\n  const effectFn = getEffectFn(node);\n  if (!effectFn) {\n    return null;\n  }\n\n  return getDownstreamRefs(context, effectFn);\n};\n\nexport function getDependenciesRefs(context, node) {\n  if (!isUseEffect(node) || node.arguments.length < 2) {\n    return undefined;\n  }\n\n  const depsArr = node.arguments[1];\n  if (depsArr.type !== \"ArrayExpression\") {\n    return undefined;\n  }\n\n  return getDownstreamRefs(context, depsArr);\n}\n\nexport const isFnRef = (ref) => getCallExpr(ref) !== undefined;\n\n// NOTE: These return true for state with CallExpressions, like `list.concat()`.\n// Arguably preferable, as mutating the state is functionally the same as calling the setter.\n// (Even though that is not recommended and should be prevented by a different rule).\n// And in the case of a prop, we can't differentiate state mutations from callbacks anyway.\nexport const isStateSetter = (context, ref) =>\n  isFnRef(ref) &&\n  getUpstreamReactVariables(context, ref.identifier).notEmptyEvery((variable) =>\n    isState(variable),\n  );\nexport const isPropCallback = (context, ref) =>\n  isFnRef(ref) &&\n  getUpstreamReactVariables(context, ref.identifier).notEmptyEvery((variable) =>\n    isProp(variable),\n  );\n\n// NOTE: Global variables (like `JSON` in `JSON.stringify()`) have an empty `defs`; fortunately `[].some() === false`.\n// Also, I'm not sure so far when `defs.length > 1`... haven't seen it with shadowed variables or even redeclared variables with `var`.\nexport const isState = (variable) =>\n  variable.defs.some((def) => isUseState(def.node));\nexport const isProp = (variable) =>\n  variable.defs.some(\n    (def) =>\n      def.type === \"Parameter\" &&\n      (isReactFunctionalComponent(getDeclNode(def.node)) ||\n        isCustomHook(getDeclNode(def.node))),\n  );\nexport const isHOCProp = (variable) =>\n  variable.defs.some(\n    (def) =>\n      def.type === \"Parameter\" && isReactFunctionalHOC(getDeclNode(def.node)),\n  );\n\nconst getDeclNode = (node) =>\n  node.type === \"ArrowFunctionExpression\"\n    ? node.parent.type === \"CallExpression\"\n      ? node.parent.parent\n      : node.parent\n    : node;\n\nexport const getUseStateNode = (context, ref) => {\n  return getUpstreamReactVariables(context, ref.identifier)\n    .find((variable) => isState(variable))\n    ?.defs.find((def) => isUseState(def.node))?.node;\n};\n\n// Returns true if the node is called directly inside a `useEffect`.\n// Note IIFEs do not break the \"direct\" chain because they're invoked immediately, as opposed to being a callback.\nexport const isDirectCall = (node) => {\n  if (!node) {\n    return false;\n  } else if (\n    (node.type === \"ArrowFunctionExpression\" ||\n      node.type === \"FunctionExpression\") &&\n    !isIIFE(node.parent)\n  ) {\n    return isUseEffect(node.parent);\n  } else {\n    return isDirectCall(node.parent);\n  }\n};\n\nexport const findPropUsedToResetAllState = (\n  context,\n  effectFnRefs,\n  depsRefs,\n  useEffectNode,\n) => {\n  const stateSetterRefs = effectFnRefs.filter((ref) =>\n    isStateSetter(context, ref),\n  );\n\n  const isAllStateReset =\n    stateSetterRefs.length > 0 &&\n    stateSetterRefs.every((ref) => isSetStateToInitialValue(context, ref)) &&\n    stateSetterRefs.length ===\n      countUseStates(context, findContainingNode(useEffectNode));\n\n  return isAllStateReset\n    ? depsRefs.find((ref) => isProp(ref.resolved))\n    : undefined;\n};\n\nconst isSetStateToInitialValue = (context, setterRef) => {\n  const setStateToValue = getCallExpr(setterRef).arguments[0];\n  const stateInitialValue = getUseStateNode(context, setterRef).init\n    .arguments[0];\n\n  // `useState()` (with no args) defaults to `undefined`,\n  // so ommitting the arg is equivalent to passing `undefined`.\n  // Technically this would false positive if they shadowed\n  // `undefined` in only one of the scopes (only possible via `var`),\n  // but I hope no one would do that.\n  const isUndefined = (node) => node === undefined || node.name === \"undefined\";\n  if (isUndefined(setStateToValue) && isUndefined(stateInitialValue)) {\n    return true;\n  }\n\n  // `sourceCode.getText()` returns the entire file when passed null/undefined - let's short circuit that\n  if (setStateToValue === null && stateInitialValue === null) {\n    return true;\n  } else if (\n    (setStateToValue && !stateInitialValue) ||\n    (!setStateToValue && stateInitialValue)\n  ) {\n    return false;\n  }\n\n  return (\n    context.sourceCode.getText(setStateToValue) ===\n    context.sourceCode.getText(stateInitialValue)\n  );\n};\n\nconst countUseStates = (context, componentNode) => {\n  let count = 0;\n\n  traverse(context, componentNode, (node) => {\n    if (isUseState(node)) {\n      count++;\n    }\n  });\n\n  return count;\n};\n\nexport const countStateSetterCalls = (stateSetterRef) =>\n  stateSetterRef.resolved.references.length - 1; // -1 for the initial declaration\n\n// Returns the component or custom hook that contains the `useEffect` node\nconst findContainingNode = (node) => {\n  if (!node) {\n    return undefined;\n  } else if (\n    isReactFunctionalComponent(node) ||\n    isReactFunctionalHOC(node) ||\n    isCustomHook(node)\n  ) {\n    return node;\n  } else {\n    return findContainingNode(node.parent);\n  }\n};\n\nexport const getUpstreamReactVariables = (context, node) =>\n  getUpstreamVariables(\n    context,\n    node,\n    // Stop at the *usage* of `useState` - don't go up to the `useState` variable.\n    // Not needed for props - they don't go \"too far\".\n    // We could remove this and check for the `useState` variable instead,\n    // but then all our tests need to import it so we can traverse up to it.\n    // And would need to change `getUseStateNode()` too?\n    // TODO: Could probably organize these filters better.\n    (node) => !isUseState(node),\n  ).filter(\n    (variable) =>\n      isProp(variable) ||\n      variable.defs.every((def) => def.type !== \"Parameter\"),\n  );\n", "Array.prototype.notEmptyEvery = function (predicate) {\n  return this.length > 0 && this.every(predicate);\n};\n\nexport const arraysEqual = (arr1, arr2) => {\n  if (arr1.length !== arr2.length) {\n    return false;\n  }\n  return arr1.every((element, index) => element === arr2[index]);\n};\n", "import { messageIds, messages } from \"./messages.js\";\nimport { getCallExpr, getDownstreamRefs } from \"./util/ast.js\";\nimport {\n  findPropUsedToResetAllState,\n  isUseEffect,\n  getUseStateNode,\n  getEffectFnRefs,\n  getDependenciesRefs,\n  isStateSetter,\n  isPropCallback,\n  isDirectCall,\n  getUpstreamReactVariables,\n  isState,\n  isProp,\n  isHOCProp,\n  countStateSetterCalls,\n} from \"./util/react.js\";\nimport { arraysEqual } from \"./util/javascript.js\";\n\nexport const name = \"you-might-not-need-an-effect\";\n\nexport const rule = {\n  meta: {\n    type: \"suggestion\",\n    docs: {\n      description: \"Catch unnecessary React useEffect hooks.\",\n      url: \"https://react.dev/learn/you-might-not-need-an-effect\",\n    },\n    schema: [],\n    messages: messages,\n  },\n  create: (context) => ({\n    CallExpression: (node) => {\n      if (!isUseEffect(node)) {\n        return;\n      }\n\n      const effectFnRefs = getEffectFnRefs(context, node);\n      const depsRefs = getDependenciesRefs(context, node);\n\n      if (!effectFnRefs || !depsRefs) {\n        return;\n      } else if (effectFnRefs.length === 0) {\n        // Hopefully it's obvious the effect can be removed.\n        // More a follow-up for once they fix/remove other issues.\n        context.report({\n          node,\n          messageId: messageIds.avoidEmptyEffect,\n        });\n        return;\n      }\n\n      const propUsedToResetAllState = findPropUsedToResetAllState(\n        context,\n        effectFnRefs,\n        depsRefs,\n        node,\n      );\n      if (propUsedToResetAllState) {\n        const propName = propUsedToResetAllState.identifier.name;\n        context.report({\n          node: node,\n          messageId: messageIds.avoidResettingStateFromProps,\n          data: { prop: propName },\n        });\n        // Don't flag anything else -- confusing, and this should be fixed first.\n        return;\n      }\n\n      effectFnRefs\n        .filter(\n          (ref) =>\n            isStateSetter(context, ref) ||\n            (isPropCallback(context, ref) &&\n              // Don't analyze HOC prop callbacks -- we don't have control over them to lift state or logic\n              !isHOCProp(ref.resolved)),\n        )\n        // Non-direct calls are likely inside a callback passed to an external system like `window.addEventListener`,\n        // or a Promise chain that (probably) retrieves external data.\n        // Note we'll still analyze derived setters because isStateSetter considers that.\n        // Heuristic inspired by https://eslint-react.xyz/docs/rules/hooks-extra-no-direct-set-state-in-use-effect\n        .filter((ref) => isDirectCall(ref.identifier))\n        .forEach((ref) => {\n          const callExpr = getCallExpr(ref);\n\n          if (isStateSetter(context, ref)) {\n            const useStateNode = getUseStateNode(context, ref);\n            const stateName = (\n              useStateNode.id.elements[0] ?? useStateNode.id.elements[1]\n            )?.name;\n\n            if (depsRefs.length === 0) {\n              context.report({\n                node: callExpr,\n                messageId: messageIds.avoidInitializingState,\n                data: { state: stateName },\n              });\n            }\n\n            // TODO: Make more readable (and performant)\n            const isAllArgsInternal = callExpr.arguments\n              .flatMap((arg) => getDownstreamRefs(context, arg))\n              .flatMap((ref) =>\n                getUpstreamReactVariables(context, ref.identifier),\n              )\n              .notEmptyEvery(\n                (variable) =>\n                  isState(variable) ||\n                  (isProp(variable) && !isHOCProp(variable)),\n              );\n            const isSomeArgsExternal = callExpr.arguments\n              .flatMap((arg) => getDownstreamRefs(context, arg))\n              .flatMap((ref) =>\n                getUpstreamReactVariables(context, ref.identifier),\n              )\n              .some(\n                (variable) =>\n                  (!isState(variable) && !isProp(variable)) ||\n                  isHOCProp(variable),\n              );\n            const isAllArgsInDeps = callExpr.arguments\n              .flatMap((arg) => getDownstreamRefs(context, arg))\n              // Need to do this prematurely here because we call notEmptyEvery on the refs,\n              // not on the upstream variables (which also filters out parameters)\n              // TODO: Think about how to centralize that.\n              .filter((ref) =>\n                ref.resolved.defs.every((def) => def.type !== \"Parameter\"),\n              )\n              .notEmptyEvery((argRef) =>\n                depsRefs.some((depRef) =>\n                  // If they have the same upstream variables, they're equivalent\n                  arraysEqual(\n                    getUpstreamReactVariables(context, argRef.identifier),\n                    getUpstreamReactVariables(context, depRef.identifier),\n                  ),\n                ),\n              );\n            const isAllDepsInternal = depsRefs\n              .flatMap((ref) =>\n                getUpstreamReactVariables(context, ref.identifier),\n              )\n              .notEmptyEvery(\n                (variable) =>\n                  isState(variable) ||\n                  (isProp(variable) && !isHOCProp(variable)),\n              );\n\n            if (\n              isAllArgsInternal ||\n              // They are always in sync, regardless of source - could compute during render\n              // TODO: Should we *always* check that the args are in deps?\n              // Should/could that replace isArgsInternal?\n              // Should it be chained state when not?\n              (isAllArgsInDeps && countStateSetterCalls(ref) === 1)\n            ) {\n              context.report({\n                node: callExpr,\n                messageId: messageIds.avoidDerivedState,\n                data: { state: stateName },\n              });\n            }\n\n            if (\n              !isAllArgsInternal &&\n              !isSomeArgsExternal &&\n              isAllDepsInternal\n            ) {\n              context.report({\n                node: callExpr,\n                messageId: messageIds.avoidChainingState,\n              });\n            }\n          } else if (isPropCallback(context, ref)) {\n            // I'm pretty sure we can flag this regardless of the arguments, including none...\n            //\n            // Because we are either:\n            // 1. Passing live state updates to the parent\n            // 2. Using state as an event handler to pass final state to the parent\n            //\n            // Both are bad. However I'm not yet sure how we could differentiate #2 to give a better warning.\n            //\n            // TODO: Can we thus safely assume that state is used as an event handler when the ref is a prop?\n            // Normally we can't warn about that because we don't know what the event handler does externally.\n            // But when it's a prop, it's internal.\n            // I guess it could still be valid when the dep is external state? Or in that case,\n            // the issue is the state should be lifted to the parent?\n            context.report({\n              node: callExpr,\n              messageId: messageIds.avoidParentChildCoupling,\n            });\n          }\n        });\n    },\n  }),\n};\n", "{\n\t\"amd\": {\n\t\t\"define\": false,\n\t\t\"require\": false\n\t},\n\t\"applescript\": {\n\t\t\"$\": false,\n\t\t\"Application\": false,\n\t\t\"Automation\": false,\n\t\t\"console\": false,\n\t\t\"delay\": false,\n\t\t\"Library\": false,\n\t\t\"ObjC\": false,\n\t\t\"ObjectSpecifier\": false,\n\t\t\"Path\": false,\n\t\t\"Progress\": false,\n\t\t\"Ref\": false\n\t},\n\t\"atomtest\": {\n\t\t\"advanceClock\": false,\n\t\t\"atom\": false,\n\t\t\"fakeClearInterval\": false,\n\t\t\"fakeClearTimeout\": false,\n\t\t\"fakeSetInterval\": false,\n\t\t\"fakeSetTimeout\": false,\n\t\t\"resetTimeouts\": false,\n\t\t\"waitsForPromise\": false\n\t},\n\t\"browser\": {\n\t\t\"AbortController\": false,\n\t\t\"AbortSignal\": false,\n\t\t\"AbsoluteOrientationSensor\": false,\n\t\t\"AbstractRange\": false,\n\t\t\"Accelerometer\": false,\n\t\t\"addEventListener\": false,\n\t\t\"ai\": false,\n\t\t\"AI\": false,\n\t\t\"AICreateMonitor\": false,\n\t\t\"AITextSession\": false,\n\t\t\"alert\": false,\n\t\t\"AnalyserNode\": false,\n\t\t\"Animation\": false,\n\t\t\"AnimationEffect\": false,\n\t\t\"AnimationEvent\": false,\n\t\t\"AnimationPlaybackEvent\": false,\n\t\t\"AnimationTimeline\": false,\n\t\t\"AsyncDisposableStack\": false,\n\t\t\"atob\": false,\n\t\t\"Attr\": false,\n\t\t\"Audio\": false,\n\t\t\"AudioBuffer\": false,\n\t\t\"AudioBufferSourceNode\": false,\n\t\t\"AudioContext\": false,\n\t\t\"AudioData\": false,\n\t\t\"AudioDecoder\": false,\n\t\t\"AudioDestinationNode\": false,\n\t\t\"AudioEncoder\": false,\n\t\t\"AudioListener\": false,\n\t\t\"AudioNode\": false,\n\t\t\"AudioParam\": false,\n\t\t\"AudioParamMap\": false,\n\t\t\"AudioProcessingEvent\": false,\n\t\t\"AudioScheduledSourceNode\": false,\n\t\t\"AudioSinkInfo\": false,\n\t\t\"AudioWorklet\": false,\n\t\t\"AudioWorkletGlobalScope\": false,\n\t\t\"AudioWorkletNode\": false,\n\t\t\"AudioWorkletProcessor\": false,\n\t\t\"AuthenticatorAssertionResponse\": false,\n\t\t\"AuthenticatorAttestationResponse\": false,\n\t\t\"AuthenticatorResponse\": false,\n\t\t\"BackgroundFetchManager\": false,\n\t\t\"BackgroundFetchRecord\": false,\n\t\t\"BackgroundFetchRegistration\": false,\n\t\t\"BarcodeDetector\": false,\n\t\t\"BarProp\": false,\n\t\t\"BaseAudioContext\": false,\n\t\t\"BatteryManager\": false,\n\t\t\"BeforeUnloadEvent\": false,\n\t\t\"BiquadFilterNode\": false,\n\t\t\"Blob\": false,\n\t\t\"BlobEvent\": false,\n\t\t\"Bluetooth\": false,\n\t\t\"BluetoothCharacteristicProperties\": false,\n\t\t\"BluetoothDevice\": false,\n\t\t\"BluetoothRemoteGATTCharacteristic\": false,\n\t\t\"BluetoothRemoteGATTDescriptor\": false,\n\t\t\"BluetoothRemoteGATTServer\": false,\n\t\t\"BluetoothRemoteGATTService\": false,\n\t\t\"BluetoothUUID\": false,\n\t\t\"blur\": false,\n\t\t\"BroadcastChannel\": false,\n\t\t\"BrowserCaptureMediaStreamTrack\": false,\n\t\t\"btoa\": false,\n\t\t\"ByteLengthQueuingStrategy\": false,\n\t\t\"Cache\": false,\n\t\t\"caches\": false,\n\t\t\"CacheStorage\": false,\n\t\t\"cancelAnimationFrame\": false,\n\t\t\"cancelIdleCallback\": false,\n\t\t\"CanvasCaptureMediaStream\": false,\n\t\t\"CanvasCaptureMediaStreamTrack\": false,\n\t\t\"CanvasGradient\": false,\n\t\t\"CanvasPattern\": false,\n\t\t\"CanvasRenderingContext2D\": false,\n\t\t\"CaptureController\": false,\n\t\t\"CaretPosition\": false,\n\t\t\"CDATASection\": false,\n\t\t\"ChannelMergerNode\": false,\n\t\t\"ChannelSplitterNode\": false,\n\t\t\"ChapterInformation\": false,\n\t\t\"CharacterBoundsUpdateEvent\": false,\n\t\t\"CharacterData\": false,\n\t\t\"clearInterval\": false,\n\t\t\"clearTimeout\": false,\n\t\t\"clientInformation\": false,\n\t\t\"Clipboard\": false,\n\t\t\"ClipboardEvent\": false,\n\t\t\"ClipboardItem\": false,\n\t\t\"close\": false,\n\t\t\"closed\": false,\n\t\t\"CloseEvent\": false,\n\t\t\"CloseWatcher\": false,\n\t\t\"CommandEvent\": false,\n\t\t\"Comment\": false,\n\t\t\"CompositionEvent\": false,\n\t\t\"CompressionStream\": false,\n\t\t\"confirm\": false,\n\t\t\"console\": false,\n\t\t\"ConstantSourceNode\": false,\n\t\t\"ContentVisibilityAutoStateChangeEvent\": false,\n\t\t\"ConvolverNode\": false,\n\t\t\"CookieChangeEvent\": false,\n\t\t\"CookieDeprecationLabel\": false,\n\t\t\"cookieStore\": false,\n\t\t\"CookieStore\": false,\n\t\t\"CookieStoreManager\": false,\n\t\t\"CountQueuingStrategy\": false,\n\t\t\"createImageBitmap\": false,\n\t\t\"Credential\": false,\n\t\t\"credentialless\": false,\n\t\t\"CredentialsContainer\": false,\n\t\t\"CropTarget\": false,\n\t\t\"crossOriginIsolated\": false,\n\t\t\"crypto\": false,\n\t\t\"Crypto\": false,\n\t\t\"CryptoKey\": false,\n\t\t\"CSPViolationReportBody\": false,\n\t\t\"CSS\": false,\n\t\t\"CSSAnimation\": false,\n\t\t\"CSSConditionRule\": false,\n\t\t\"CSSContainerRule\": false,\n\t\t\"CSSCounterStyleRule\": false,\n\t\t\"CSSFontFaceRule\": false,\n\t\t\"CSSFontFeatureValuesRule\": false,\n\t\t\"CSSFontPaletteValuesRule\": false,\n\t\t\"CSSGroupingRule\": false,\n\t\t\"CSSImageValue\": false,\n\t\t\"CSSImportRule\": false,\n\t\t\"CSSKeyframeRule\": false,\n\t\t\"CSSKeyframesRule\": false,\n\t\t\"CSSKeywordValue\": false,\n\t\t\"CSSLayerBlockRule\": false,\n\t\t\"CSSLayerStatementRule\": false,\n\t\t\"CSSMarginRule\": false,\n\t\t\"CSSMathClamp\": false,\n\t\t\"CSSMathInvert\": false,\n\t\t\"CSSMathMax\": false,\n\t\t\"CSSMathMin\": false,\n\t\t\"CSSMathNegate\": false,\n\t\t\"CSSMathProduct\": false,\n\t\t\"CSSMathSum\": false,\n\t\t\"CSSMathValue\": false,\n\t\t\"CSSMatrixComponent\": false,\n\t\t\"CSSMediaRule\": false,\n\t\t\"CSSNamespaceRule\": false,\n\t\t\"CSSNestedDeclarations\": false,\n\t\t\"CSSNumericArray\": false,\n\t\t\"CSSNumericValue\": false,\n\t\t\"CSSPageDescriptors\": false,\n\t\t\"CSSPageRule\": false,\n\t\t\"CSSPerspective\": false,\n\t\t\"CSSPositionTryDescriptors\": false,\n\t\t\"CSSPositionTryRule\": false,\n\t\t\"CSSPositionValue\": false,\n\t\t\"CSSPropertyRule\": false,\n\t\t\"CSSRotate\": false,\n\t\t\"CSSRule\": false,\n\t\t\"CSSRuleList\": false,\n\t\t\"CSSScale\": false,\n\t\t\"CSSScopeRule\": false,\n\t\t\"CSSSkew\": false,\n\t\t\"CSSSkewX\": false,\n\t\t\"CSSSkewY\": false,\n\t\t\"CSSStartingStyleRule\": false,\n\t\t\"CSSStyleDeclaration\": false,\n\t\t\"CSSStyleRule\": false,\n\t\t\"CSSStyleSheet\": false,\n\t\t\"CSSStyleValue\": false,\n\t\t\"CSSSupportsRule\": false,\n\t\t\"CSSTransformComponent\": false,\n\t\t\"CSSTransformValue\": false,\n\t\t\"CSSTransition\": false,\n\t\t\"CSSTranslate\": false,\n\t\t\"CSSUnitValue\": false,\n\t\t\"CSSUnparsedValue\": false,\n\t\t\"CSSVariableReferenceValue\": false,\n\t\t\"CSSViewTransitionRule\": false,\n\t\t\"currentFrame\": false,\n\t\t\"currentTime\": false,\n\t\t\"CustomElementRegistry\": false,\n\t\t\"customElements\": false,\n\t\t\"CustomEvent\": false,\n\t\t\"CustomStateSet\": false,\n\t\t\"DataTransfer\": false,\n\t\t\"DataTransferItem\": false,\n\t\t\"DataTransferItemList\": false,\n\t\t\"DecompressionStream\": false,\n\t\t\"DelayNode\": false,\n\t\t\"DelegatedInkTrailPresenter\": false,\n\t\t\"DeviceMotionEvent\": false,\n\t\t\"DeviceMotionEventAcceleration\": false,\n\t\t\"DeviceMotionEventRotationRate\": false,\n\t\t\"DeviceOrientationEvent\": false,\n\t\t\"devicePixelRatio\": false,\n\t\t\"DevicePosture\": false,\n\t\t\"dispatchEvent\": false,\n\t\t\"DisposableStack\": false,\n\t\t\"document\": false,\n\t\t\"Document\": false,\n\t\t\"DocumentFragment\": false,\n\t\t\"documentPictureInPicture\": false,\n\t\t\"DocumentPictureInPicture\": false,\n\t\t\"DocumentPictureInPictureEvent\": false,\n\t\t\"DocumentTimeline\": false,\n\t\t\"DocumentType\": false,\n\t\t\"DOMError\": false,\n\t\t\"DOMException\": false,\n\t\t\"DOMImplementation\": false,\n\t\t\"DOMMatrix\": false,\n\t\t\"DOMMatrixReadOnly\": false,\n\t\t\"DOMParser\": false,\n\t\t\"DOMPoint\": false,\n\t\t\"DOMPointReadOnly\": false,\n\t\t\"DOMQuad\": false,\n\t\t\"DOMRect\": false,\n\t\t\"DOMRectList\": false,\n\t\t\"DOMRectReadOnly\": false,\n\t\t\"DOMStringList\": false,\n\t\t\"DOMStringMap\": false,\n\t\t\"DOMTokenList\": false,\n\t\t\"DragEvent\": false,\n\t\t\"DynamicsCompressorNode\": false,\n\t\t\"EditContext\": false,\n\t\t\"Element\": false,\n\t\t\"ElementInternals\": false,\n\t\t\"EncodedAudioChunk\": false,\n\t\t\"EncodedVideoChunk\": false,\n\t\t\"ErrorEvent\": false,\n\t\t\"event\": false,\n\t\t\"Event\": false,\n\t\t\"EventCounts\": false,\n\t\t\"EventSource\": false,\n\t\t\"EventTarget\": false,\n\t\t\"external\": false,\n\t\t\"External\": false,\n\t\t\"EyeDropper\": false,\n\t\t\"FeaturePolicy\": false,\n\t\t\"FederatedCredential\": false,\n\t\t\"fence\": false,\n\t\t\"Fence\": false,\n\t\t\"FencedFrameConfig\": false,\n\t\t\"fetch\": false,\n\t\t\"fetchLater\": false,\n\t\t\"FetchLaterResult\": false,\n\t\t\"File\": false,\n\t\t\"FileList\": false,\n\t\t\"FileReader\": false,\n\t\t\"FileSystem\": false,\n\t\t\"FileSystemDirectoryEntry\": false,\n\t\t\"FileSystemDirectoryHandle\": false,\n\t\t\"FileSystemDirectoryReader\": false,\n\t\t\"FileSystemEntry\": false,\n\t\t\"FileSystemFileEntry\": false,\n\t\t\"FileSystemFileHandle\": false,\n\t\t\"FileSystemHandle\": false,\n\t\t\"FileSystemObserver\": false,\n\t\t\"FileSystemWritableFileStream\": false,\n\t\t\"find\": false,\n\t\t\"focus\": false,\n\t\t\"FocusEvent\": false,\n\t\t\"FontData\": false,\n\t\t\"FontFace\": false,\n\t\t\"FontFaceSet\": false,\n\t\t\"FontFaceSetLoadEvent\": false,\n\t\t\"FormData\": false,\n\t\t\"FormDataEvent\": false,\n\t\t\"FragmentDirective\": false,\n\t\t\"frameElement\": false,\n\t\t\"frames\": false,\n\t\t\"GainNode\": false,\n\t\t\"Gamepad\": false,\n\t\t\"GamepadAxisMoveEvent\": false,\n\t\t\"GamepadButton\": false,\n\t\t\"GamepadButtonEvent\": false,\n\t\t\"GamepadEvent\": false,\n\t\t\"GamepadHapticActuator\": false,\n\t\t\"GamepadPose\": false,\n\t\t\"Geolocation\": false,\n\t\t\"GeolocationCoordinates\": false,\n\t\t\"GeolocationPosition\": false,\n\t\t\"GeolocationPositionError\": false,\n\t\t\"getComputedStyle\": false,\n\t\t\"getScreenDetails\": false,\n\t\t\"getSelection\": false,\n\t\t\"GPU\": false,\n\t\t\"GPUAdapter\": false,\n\t\t\"GPUAdapterInfo\": false,\n\t\t\"GPUBindGroup\": false,\n\t\t\"GPUBindGroupLayout\": false,\n\t\t\"GPUBuffer\": false,\n\t\t\"GPUBufferUsage\": false,\n\t\t\"GPUCanvasContext\": false,\n\t\t\"GPUColorWrite\": false,\n\t\t\"GPUCommandBuffer\": false,\n\t\t\"GPUCommandEncoder\": false,\n\t\t\"GPUCompilationInfo\": false,\n\t\t\"GPUCompilationMessage\": false,\n\t\t\"GPUComputePassEncoder\": false,\n\t\t\"GPUComputePipeline\": false,\n\t\t\"GPUDevice\": false,\n\t\t\"GPUDeviceLostInfo\": false,\n\t\t\"GPUError\": false,\n\t\t\"GPUExternalTexture\": false,\n\t\t\"GPUInternalError\": false,\n\t\t\"GPUMapMode\": false,\n\t\t\"GPUOutOfMemoryError\": false,\n\t\t\"GPUPipelineError\": false,\n\t\t\"GPUPipelineLayout\": false,\n\t\t\"GPUQuerySet\": false,\n\t\t\"GPUQueue\": false,\n\t\t\"GPURenderBundle\": false,\n\t\t\"GPURenderBundleEncoder\": false,\n\t\t\"GPURenderPassEncoder\": false,\n\t\t\"GPURenderPipeline\": false,\n\t\t\"GPUSampler\": false,\n\t\t\"GPUShaderModule\": false,\n\t\t\"GPUShaderStage\": false,\n\t\t\"GPUSupportedFeatures\": false,\n\t\t\"GPUSupportedLimits\": false,\n\t\t\"GPUTexture\": false,\n\t\t\"GPUTextureUsage\": false,\n\t\t\"GPUTextureView\": false,\n\t\t\"GPUUncapturedErrorEvent\": false,\n\t\t\"GPUValidationError\": false,\n\t\t\"GravitySensor\": false,\n\t\t\"Gyroscope\": false,\n\t\t\"HashChangeEvent\": false,\n\t\t\"Headers\": false,\n\t\t\"HID\": false,\n\t\t\"HIDConnectionEvent\": false,\n\t\t\"HIDDevice\": false,\n\t\t\"HIDInputReportEvent\": false,\n\t\t\"Highlight\": false,\n\t\t\"HighlightRegistry\": false,\n\t\t\"history\": false,\n\t\t\"History\": false,\n\t\t\"HTMLAllCollection\": false,\n\t\t\"HTMLAnchorElement\": false,\n\t\t\"HTMLAreaElement\": false,\n\t\t\"HTMLAudioElement\": false,\n\t\t\"HTMLBaseElement\": false,\n\t\t\"HTMLBodyElement\": false,\n\t\t\"HTMLBRElement\": false,\n\t\t\"HTMLButtonElement\": false,\n\t\t\"HTMLCanvasElement\": false,\n\t\t\"HTMLCollection\": false,\n\t\t\"HTMLDataElement\": false,\n\t\t\"HTMLDataListElement\": false,\n\t\t\"HTMLDetailsElement\": false,\n\t\t\"HTMLDialogElement\": false,\n\t\t\"HTMLDirectoryElement\": false,\n\t\t\"HTMLDivElement\": false,\n\t\t\"HTMLDListElement\": false,\n\t\t\"HTMLDocument\": false,\n\t\t\"HTMLElement\": false,\n\t\t\"HTMLEmbedElement\": false,\n\t\t\"HTMLFencedFrameElement\": false,\n\t\t\"HTMLFieldSetElement\": false,\n\t\t\"HTMLFontElement\": false,\n\t\t\"HTMLFormControlsCollection\": false,\n\t\t\"HTMLFormElement\": false,\n\t\t\"HTMLFrameElement\": false,\n\t\t\"HTMLFrameSetElement\": false,\n\t\t\"HTMLHeadElement\": false,\n\t\t\"HTMLHeadingElement\": false,\n\t\t\"HTMLHRElement\": false,\n\t\t\"HTMLHtmlElement\": false,\n\t\t\"HTMLIFrameElement\": false,\n\t\t\"HTMLImageElement\": false,\n\t\t\"HTMLInputElement\": false,\n\t\t\"HTMLLabelElement\": false,\n\t\t\"HTMLLegendElement\": false,\n\t\t\"HTMLLIElement\": false,\n\t\t\"HTMLLinkElement\": false,\n\t\t\"HTMLMapElement\": false,\n\t\t\"HTMLMarqueeElement\": false,\n\t\t\"HTMLMediaElement\": false,\n\t\t\"HTMLMenuElement\": false,\n\t\t\"HTMLMetaElement\": false,\n\t\t\"HTMLMeterElement\": false,\n\t\t\"HTMLModElement\": false,\n\t\t\"HTMLObjectElement\": false,\n\t\t\"HTMLOListElement\": false,\n\t\t\"HTMLOptGroupElement\": false,\n\t\t\"HTMLOptionElement\": false,\n\t\t\"HTMLOptionsCollection\": false,\n\t\t\"HTMLOutputElement\": false,\n\t\t\"HTMLParagraphElement\": false,\n\t\t\"HTMLParamElement\": false,\n\t\t\"HTMLPictureElement\": false,\n\t\t\"HTMLPreElement\": false,\n\t\t\"HTMLProgressElement\": false,\n\t\t\"HTMLQuoteElement\": false,\n\t\t\"HTMLScriptElement\": false,\n\t\t\"HTMLSelectedContentElement\": false,\n\t\t\"HTMLSelectElement\": false,\n\t\t\"HTMLSlotElement\": false,\n\t\t\"HTMLSourceElement\": false,\n\t\t\"HTMLSpanElement\": false,\n\t\t\"HTMLStyleElement\": false,\n\t\t\"HTMLTableCaptionElement\": false,\n\t\t\"HTMLTableCellElement\": false,\n\t\t\"HTMLTableColElement\": false,\n\t\t\"HTMLTableElement\": false,\n\t\t\"HTMLTableRowElement\": false,\n\t\t\"HTMLTableSectionElement\": false,\n\t\t\"HTMLTemplateElement\": false,\n\t\t\"HTMLTextAreaElement\": false,\n\t\t\"HTMLTimeElement\": false,\n\t\t\"HTMLTitleElement\": false,\n\t\t\"HTMLTrackElement\": false,\n\t\t\"HTMLUListElement\": false,\n\t\t\"HTMLUnknownElement\": false,\n\t\t\"HTMLVideoElement\": false,\n\t\t\"IDBCursor\": false,\n\t\t\"IDBCursorWithValue\": false,\n\t\t\"IDBDatabase\": false,\n\t\t\"IDBFactory\": false,\n\t\t\"IDBIndex\": false,\n\t\t\"IDBKeyRange\": false,\n\t\t\"IDBObjectStore\": false,\n\t\t\"IDBOpenDBRequest\": false,\n\t\t\"IDBRequest\": false,\n\t\t\"IDBTransaction\": false,\n\t\t\"IDBVersionChangeEvent\": false,\n\t\t\"IdentityCredential\": false,\n\t\t\"IdentityCredentialError\": false,\n\t\t\"IdentityProvider\": false,\n\t\t\"IdleDeadline\": false,\n\t\t\"IdleDetector\": false,\n\t\t\"IIRFilterNode\": false,\n\t\t\"Image\": false,\n\t\t\"ImageBitmap\": false,\n\t\t\"ImageBitmapRenderingContext\": false,\n\t\t\"ImageCapture\": false,\n\t\t\"ImageData\": false,\n\t\t\"ImageDecoder\": false,\n\t\t\"ImageTrack\": false,\n\t\t\"ImageTrackList\": false,\n\t\t\"indexedDB\": false,\n\t\t\"Ink\": false,\n\t\t\"innerHeight\": false,\n\t\t\"innerWidth\": false,\n\t\t\"InputDeviceCapabilities\": false,\n\t\t\"InputDeviceInfo\": false,\n\t\t\"InputEvent\": false,\n\t\t\"IntersectionObserver\": false,\n\t\t\"IntersectionObserverEntry\": false,\n\t\t\"isSecureContext\": false,\n\t\t\"Keyboard\": false,\n\t\t\"KeyboardEvent\": false,\n\t\t\"KeyboardLayoutMap\": false,\n\t\t\"KeyframeEffect\": false,\n\t\t\"LanguageDetector\": false,\n\t\t\"LargestContentfulPaint\": false,\n\t\t\"LaunchParams\": false,\n\t\t\"launchQueue\": false,\n\t\t\"LaunchQueue\": false,\n\t\t\"LayoutShift\": false,\n\t\t\"LayoutShiftAttribution\": false,\n\t\t\"length\": false,\n\t\t\"LinearAccelerationSensor\": false,\n\t\t\"localStorage\": false,\n\t\t\"location\": true,\n\t\t\"Location\": false,\n\t\t\"locationbar\": false,\n\t\t\"Lock\": false,\n\t\t\"LockManager\": false,\n\t\t\"matchMedia\": false,\n\t\t\"MathMLElement\": false,\n\t\t\"MediaCapabilities\": false,\n\t\t\"MediaCapabilitiesInfo\": false,\n\t\t\"MediaDeviceInfo\": false,\n\t\t\"MediaDevices\": false,\n\t\t\"MediaElementAudioSourceNode\": false,\n\t\t\"MediaEncryptedEvent\": false,\n\t\t\"MediaError\": false,\n\t\t\"MediaKeyError\": false,\n\t\t\"MediaKeyMessageEvent\": false,\n\t\t\"MediaKeys\": false,\n\t\t\"MediaKeySession\": false,\n\t\t\"MediaKeyStatusMap\": false,\n\t\t\"MediaKeySystemAccess\": false,\n\t\t\"MediaList\": false,\n\t\t\"MediaMetadata\": false,\n\t\t\"MediaQueryList\": false,\n\t\t\"MediaQueryListEvent\": false,\n\t\t\"MediaRecorder\": false,\n\t\t\"MediaRecorderErrorEvent\": false,\n\t\t\"MediaSession\": false,\n\t\t\"MediaSource\": false,\n\t\t\"MediaSourceHandle\": false,\n\t\t\"MediaStream\": false,\n\t\t\"MediaStreamAudioDestinationNode\": false,\n\t\t\"MediaStreamAudioSourceNode\": false,\n\t\t\"MediaStreamEvent\": false,\n\t\t\"MediaStreamTrack\": false,\n\t\t\"MediaStreamTrackAudioSourceNode\": false,\n\t\t\"MediaStreamTrackAudioStats\": false,\n\t\t\"MediaStreamTrackEvent\": false,\n\t\t\"MediaStreamTrackGenerator\": false,\n\t\t\"MediaStreamTrackProcessor\": false,\n\t\t\"MediaStreamTrackVideoStats\": false,\n\t\t\"menubar\": false,\n\t\t\"MessageChannel\": false,\n\t\t\"MessageEvent\": false,\n\t\t\"MessagePort\": false,\n\t\t\"MIDIAccess\": false,\n\t\t\"MIDIConnectionEvent\": false,\n\t\t\"MIDIInput\": false,\n\t\t\"MIDIInputMap\": false,\n\t\t\"MIDIMessageEvent\": false,\n\t\t\"MIDIOutput\": false,\n\t\t\"MIDIOutputMap\": false,\n\t\t\"MIDIPort\": false,\n\t\t\"MimeType\": false,\n\t\t\"MimeTypeArray\": false,\n\t\t\"model\": false,\n\t\t\"ModelGenericSession\": false,\n\t\t\"ModelManager\": false,\n\t\t\"MouseEvent\": false,\n\t\t\"moveBy\": false,\n\t\t\"moveTo\": false,\n\t\t\"MutationEvent\": false,\n\t\t\"MutationObserver\": false,\n\t\t\"MutationRecord\": false,\n\t\t\"name\": false,\n\t\t\"NamedNodeMap\": false,\n\t\t\"NavigateEvent\": false,\n\t\t\"navigation\": false,\n\t\t\"Navigation\": false,\n\t\t\"NavigationActivation\": false,\n\t\t\"NavigationCurrentEntryChangeEvent\": false,\n\t\t\"NavigationDestination\": false,\n\t\t\"NavigationHistoryEntry\": false,\n\t\t\"NavigationPreloadManager\": false,\n\t\t\"NavigationTransition\": false,\n\t\t\"navigator\": false,\n\t\t\"Navigator\": false,\n\t\t\"NavigatorLogin\": false,\n\t\t\"NavigatorManagedData\": false,\n\t\t\"NavigatorUAData\": false,\n\t\t\"NetworkInformation\": false,\n\t\t\"Node\": false,\n\t\t\"NodeFilter\": false,\n\t\t\"NodeIterator\": false,\n\t\t\"NodeList\": false,\n\t\t\"Notification\": false,\n\t\t\"NotifyPaintEvent\": false,\n\t\t\"NotRestoredReasonDetails\": false,\n\t\t\"NotRestoredReasons\": false,\n\t\t\"Observable\": false,\n\t\t\"OfflineAudioCompletionEvent\": false,\n\t\t\"OfflineAudioContext\": false,\n\t\t\"offscreenBuffering\": false,\n\t\t\"OffscreenCanvas\": false,\n\t\t\"OffscreenCanvasRenderingContext2D\": false,\n\t\t\"onabort\": true,\n\t\t\"onafterprint\": true,\n\t\t\"onanimationcancel\": true,\n\t\t\"onanimationend\": true,\n\t\t\"onanimationiteration\": true,\n\t\t\"onanimationstart\": true,\n\t\t\"onappinstalled\": true,\n\t\t\"onauxclick\": true,\n\t\t\"onbeforeinput\": true,\n\t\t\"onbeforeinstallprompt\": true,\n\t\t\"onbeforematch\": true,\n\t\t\"onbeforeprint\": true,\n\t\t\"onbeforetoggle\": true,\n\t\t\"onbeforeunload\": true,\n\t\t\"onbeforexrselect\": true,\n\t\t\"onblur\": true,\n\t\t\"oncancel\": true,\n\t\t\"oncanplay\": true,\n\t\t\"oncanplaythrough\": true,\n\t\t\"onchange\": true,\n\t\t\"onclick\": true,\n\t\t\"onclose\": true,\n\t\t\"oncommand\": true,\n\t\t\"oncontentvisibilityautostatechange\": true,\n\t\t\"oncontextlost\": true,\n\t\t\"oncontextmenu\": true,\n\t\t\"oncontextrestored\": true,\n\t\t\"oncopy\": true,\n\t\t\"oncuechange\": true,\n\t\t\"oncut\": true,\n\t\t\"ondblclick\": true,\n\t\t\"ondevicemotion\": true,\n\t\t\"ondeviceorientation\": true,\n\t\t\"ondeviceorientationabsolute\": true,\n\t\t\"ondrag\": true,\n\t\t\"ondragend\": true,\n\t\t\"ondragenter\": true,\n\t\t\"ondragleave\": true,\n\t\t\"ondragover\": true,\n\t\t\"ondragstart\": true,\n\t\t\"ondrop\": true,\n\t\t\"ondurationchange\": true,\n\t\t\"onemptied\": true,\n\t\t\"onended\": true,\n\t\t\"onerror\": true,\n\t\t\"onfocus\": true,\n\t\t\"onformdata\": true,\n\t\t\"ongamepadconnected\": true,\n\t\t\"ongamepaddisconnected\": true,\n\t\t\"ongotpointercapture\": true,\n\t\t\"onhashchange\": true,\n\t\t\"oninput\": true,\n\t\t\"oninvalid\": true,\n\t\t\"onkeydown\": true,\n\t\t\"onkeypress\": true,\n\t\t\"onkeyup\": true,\n\t\t\"onlanguagechange\": true,\n\t\t\"onload\": true,\n\t\t\"onloadeddata\": true,\n\t\t\"onloadedmetadata\": true,\n\t\t\"onloadstart\": true,\n\t\t\"onlostpointercapture\": true,\n\t\t\"onmessage\": true,\n\t\t\"onmessageerror\": true,\n\t\t\"onmousedown\": true,\n\t\t\"onmouseenter\": true,\n\t\t\"onmouseleave\": true,\n\t\t\"onmousemove\": true,\n\t\t\"onmouseout\": true,\n\t\t\"onmouseover\": true,\n\t\t\"onmouseup\": true,\n\t\t\"onmousewheel\": true,\n\t\t\"onoffline\": true,\n\t\t\"ononline\": true,\n\t\t\"onpagehide\": true,\n\t\t\"onpagereveal\": true,\n\t\t\"onpageshow\": true,\n\t\t\"onpageswap\": true,\n\t\t\"onpaste\": true,\n\t\t\"onpause\": true,\n\t\t\"onplay\": true,\n\t\t\"onplaying\": true,\n\t\t\"onpointercancel\": true,\n\t\t\"onpointerdown\": true,\n\t\t\"onpointerenter\": true,\n\t\t\"onpointerleave\": true,\n\t\t\"onpointermove\": true,\n\t\t\"onpointerout\": true,\n\t\t\"onpointerover\": true,\n\t\t\"onpointerrawupdate\": true,\n\t\t\"onpointerup\": true,\n\t\t\"onpopstate\": true,\n\t\t\"onprogress\": true,\n\t\t\"onratechange\": true,\n\t\t\"onrejectionhandled\": true,\n\t\t\"onreset\": true,\n\t\t\"onresize\": true,\n\t\t\"onscroll\": true,\n\t\t\"onscrollend\": true,\n\t\t\"onscrollsnapchange\": true,\n\t\t\"onscrollsnapchanging\": true,\n\t\t\"onsearch\": true,\n\t\t\"onsecuritypolicyviolation\": true,\n\t\t\"onseeked\": true,\n\t\t\"onseeking\": true,\n\t\t\"onselect\": true,\n\t\t\"onselectionchange\": true,\n\t\t\"onselectstart\": true,\n\t\t\"onslotchange\": true,\n\t\t\"onstalled\": true,\n\t\t\"onstorage\": true,\n\t\t\"onsubmit\": true,\n\t\t\"onsuspend\": true,\n\t\t\"ontimeupdate\": true,\n\t\t\"ontoggle\": true,\n\t\t\"ontransitioncancel\": true,\n\t\t\"ontransitionend\": true,\n\t\t\"ontransitionrun\": true,\n\t\t\"ontransitionstart\": true,\n\t\t\"onunhandledrejection\": true,\n\t\t\"onunload\": true,\n\t\t\"onvolumechange\": true,\n\t\t\"onwaiting\": true,\n\t\t\"onwheel\": true,\n\t\t\"open\": false,\n\t\t\"opener\": false,\n\t\t\"Option\": false,\n\t\t\"OrientationSensor\": false,\n\t\t\"origin\": false,\n\t\t\"originAgentCluster\": false,\n\t\t\"OscillatorNode\": false,\n\t\t\"OTPCredential\": false,\n\t\t\"outerHeight\": false,\n\t\t\"outerWidth\": false,\n\t\t\"OverconstrainedError\": false,\n\t\t\"PageRevealEvent\": false,\n\t\t\"PageSwapEvent\": false,\n\t\t\"PageTransitionEvent\": false,\n\t\t\"pageXOffset\": false,\n\t\t\"pageYOffset\": false,\n\t\t\"PannerNode\": false,\n\t\t\"parent\": false,\n\t\t\"PasswordCredential\": false,\n\t\t\"Path2D\": false,\n\t\t\"PaymentAddress\": false,\n\t\t\"PaymentManager\": false,\n\t\t\"PaymentMethodChangeEvent\": false,\n\t\t\"PaymentRequest\": false,\n\t\t\"PaymentRequestUpdateEvent\": false,\n\t\t\"PaymentResponse\": false,\n\t\t\"performance\": false,\n\t\t\"Performance\": false,\n\t\t\"PerformanceElementTiming\": false,\n\t\t\"PerformanceEntry\": false,\n\t\t\"PerformanceEventTiming\": false,\n\t\t\"PerformanceLongAnimationFrameTiming\": false,\n\t\t\"PerformanceLongTaskTiming\": false,\n\t\t\"PerformanceMark\": false,\n\t\t\"PerformanceMeasure\": false,\n\t\t\"PerformanceNavigation\": false,\n\t\t\"PerformanceNavigationTiming\": false,\n\t\t\"PerformanceObserver\": false,\n\t\t\"PerformanceObserverEntryList\": false,\n\t\t\"PerformancePaintTiming\": false,\n\t\t\"PerformanceResourceTiming\": false,\n\t\t\"PerformanceScriptTiming\": false,\n\t\t\"PerformanceServerTiming\": false,\n\t\t\"PerformanceTiming\": false,\n\t\t\"PeriodicSyncManager\": false,\n\t\t\"PeriodicWave\": false,\n\t\t\"Permissions\": false,\n\t\t\"PermissionStatus\": false,\n\t\t\"PERSISTENT\": false,\n\t\t\"personalbar\": false,\n\t\t\"PictureInPictureEvent\": false,\n\t\t\"PictureInPictureWindow\": false,\n\t\t\"Plugin\": false,\n\t\t\"PluginArray\": false,\n\t\t\"PointerEvent\": false,\n\t\t\"PopStateEvent\": false,\n\t\t\"postMessage\": false,\n\t\t\"Presentation\": false,\n\t\t\"PresentationAvailability\": false,\n\t\t\"PresentationConnection\": false,\n\t\t\"PresentationConnectionAvailableEvent\": false,\n\t\t\"PresentationConnectionCloseEvent\": false,\n\t\t\"PresentationConnectionList\": false,\n\t\t\"PresentationReceiver\": false,\n\t\t\"PresentationRequest\": false,\n\t\t\"PressureObserver\": false,\n\t\t\"PressureRecord\": false,\n\t\t\"print\": false,\n\t\t\"ProcessingInstruction\": false,\n\t\t\"Profiler\": false,\n\t\t\"ProgressEvent\": false,\n\t\t\"PromiseRejectionEvent\": false,\n\t\t\"prompt\": false,\n\t\t\"ProtectedAudience\": false,\n\t\t\"PublicKeyCredential\": false,\n\t\t\"PushManager\": false,\n\t\t\"PushSubscription\": false,\n\t\t\"PushSubscriptionOptions\": false,\n\t\t\"queryLocalFonts\": false,\n\t\t\"queueMicrotask\": false,\n\t\t\"RadioNodeList\": false,\n\t\t\"Range\": false,\n\t\t\"ReadableByteStreamController\": false,\n\t\t\"ReadableStream\": false,\n\t\t\"ReadableStreamBYOBReader\": false,\n\t\t\"ReadableStreamBYOBRequest\": false,\n\t\t\"ReadableStreamDefaultController\": false,\n\t\t\"ReadableStreamDefaultReader\": false,\n\t\t\"registerProcessor\": false,\n\t\t\"RelativeOrientationSensor\": false,\n\t\t\"RemotePlayback\": false,\n\t\t\"removeEventListener\": false,\n\t\t\"ReportBody\": false,\n\t\t\"reportError\": false,\n\t\t\"ReportingObserver\": false,\n\t\t\"Request\": false,\n\t\t\"requestAnimationFrame\": false,\n\t\t\"requestIdleCallback\": false,\n\t\t\"resizeBy\": false,\n\t\t\"ResizeObserver\": false,\n\t\t\"ResizeObserverEntry\": false,\n\t\t\"ResizeObserverSize\": false,\n\t\t\"resizeTo\": false,\n\t\t\"Response\": false,\n\t\t\"RestrictionTarget\": false,\n\t\t\"RTCCertificate\": false,\n\t\t\"RTCDataChannel\": false,\n\t\t\"RTCDataChannelEvent\": false,\n\t\t\"RTCDtlsTransport\": false,\n\t\t\"RTCDTMFSender\": false,\n\t\t\"RTCDTMFToneChangeEvent\": false,\n\t\t\"RTCEncodedAudioFrame\": false,\n\t\t\"RTCEncodedVideoFrame\": false,\n\t\t\"RTCError\": false,\n\t\t\"RTCErrorEvent\": false,\n\t\t\"RTCIceCandidate\": false,\n\t\t\"RTCIceTransport\": false,\n\t\t\"RTCPeerConnection\": false,\n\t\t\"RTCPeerConnectionIceErrorEvent\": false,\n\t\t\"RTCPeerConnectionIceEvent\": false,\n\t\t\"RTCRtpReceiver\": false,\n\t\t\"RTCRtpScriptTransform\": false,\n\t\t\"RTCRtpSender\": false,\n\t\t\"RTCRtpTransceiver\": false,\n\t\t\"RTCSctpTransport\": false,\n\t\t\"RTCSessionDescription\": false,\n\t\t\"RTCStatsReport\": false,\n\t\t\"RTCTrackEvent\": false,\n\t\t\"sampleRate\": false,\n\t\t\"scheduler\": false,\n\t\t\"Scheduler\": false,\n\t\t\"Scheduling\": false,\n\t\t\"screen\": false,\n\t\t\"Screen\": false,\n\t\t\"ScreenDetailed\": false,\n\t\t\"ScreenDetails\": false,\n\t\t\"screenLeft\": false,\n\t\t\"ScreenOrientation\": false,\n\t\t\"screenTop\": false,\n\t\t\"screenX\": false,\n\t\t\"screenY\": false,\n\t\t\"ScriptProcessorNode\": false,\n\t\t\"scroll\": false,\n\t\t\"scrollbars\": false,\n\t\t\"scrollBy\": false,\n\t\t\"ScrollTimeline\": false,\n\t\t\"scrollTo\": false,\n\t\t\"scrollX\": false,\n\t\t\"scrollY\": false,\n\t\t\"SecurityPolicyViolationEvent\": false,\n\t\t\"Selection\": false,\n\t\t\"self\": false,\n\t\t\"Sensor\": false,\n\t\t\"SensorErrorEvent\": false,\n\t\t\"Serial\": false,\n\t\t\"SerialPort\": false,\n\t\t\"ServiceWorker\": false,\n\t\t\"ServiceWorkerContainer\": false,\n\t\t\"ServiceWorkerRegistration\": false,\n\t\t\"sessionStorage\": false,\n\t\t\"setInterval\": false,\n\t\t\"setTimeout\": false,\n\t\t\"ShadowRoot\": false,\n\t\t\"sharedStorage\": false,\n\t\t\"SharedStorage\": false,\n\t\t\"SharedStorageAppendMethod\": false,\n\t\t\"SharedStorageClearMethod\": false,\n\t\t\"SharedStorageDeleteMethod\": false,\n\t\t\"SharedStorageModifierMethod\": false,\n\t\t\"SharedStorageSetMethod\": false,\n\t\t\"SharedStorageWorklet\": false,\n\t\t\"SharedWorker\": false,\n\t\t\"showDirectoryPicker\": false,\n\t\t\"showOpenFilePicker\": false,\n\t\t\"showSaveFilePicker\": false,\n\t\t\"SnapEvent\": false,\n\t\t\"SourceBuffer\": false,\n\t\t\"SourceBufferList\": false,\n\t\t\"speechSynthesis\": false,\n\t\t\"SpeechSynthesis\": false,\n\t\t\"SpeechSynthesisErrorEvent\": false,\n\t\t\"SpeechSynthesisEvent\": false,\n\t\t\"SpeechSynthesisUtterance\": false,\n\t\t\"SpeechSynthesisVoice\": false,\n\t\t\"StaticRange\": false,\n\t\t\"status\": false,\n\t\t\"statusbar\": false,\n\t\t\"StereoPannerNode\": false,\n\t\t\"stop\": false,\n\t\t\"Storage\": false,\n\t\t\"StorageBucket\": false,\n\t\t\"StorageBucketManager\": false,\n\t\t\"StorageEvent\": false,\n\t\t\"StorageManager\": false,\n\t\t\"structuredClone\": false,\n\t\t\"styleMedia\": false,\n\t\t\"StylePropertyMap\": false,\n\t\t\"StylePropertyMapReadOnly\": false,\n\t\t\"StyleSheet\": false,\n\t\t\"StyleSheetList\": false,\n\t\t\"SubmitEvent\": false,\n\t\t\"Subscriber\": false,\n\t\t\"SubtleCrypto\": false,\n\t\t\"SuppressedError\": false,\n\t\t\"SVGAElement\": false,\n\t\t\"SVGAngle\": false,\n\t\t\"SVGAnimatedAngle\": false,\n\t\t\"SVGAnimatedBoolean\": false,\n\t\t\"SVGAnimatedEnumeration\": false,\n\t\t\"SVGAnimatedInteger\": false,\n\t\t\"SVGAnimatedLength\": false,\n\t\t\"SVGAnimatedLengthList\": false,\n\t\t\"SVGAnimatedNumber\": false,\n\t\t\"SVGAnimatedNumberList\": false,\n\t\t\"SVGAnimatedPreserveAspectRatio\": false,\n\t\t\"SVGAnimatedRect\": false,\n\t\t\"SVGAnimatedString\": false,\n\t\t\"SVGAnimatedTransformList\": false,\n\t\t\"SVGAnimateElement\": false,\n\t\t\"SVGAnimateMotionElement\": false,\n\t\t\"SVGAnimateTransformElement\": false,\n\t\t\"SVGAnimationElement\": false,\n\t\t\"SVGCircleElement\": false,\n\t\t\"SVGClipPathElement\": false,\n\t\t\"SVGComponentTransferFunctionElement\": false,\n\t\t\"SVGDefsElement\": false,\n\t\t\"SVGDescElement\": false,\n\t\t\"SVGElement\": false,\n\t\t\"SVGEllipseElement\": false,\n\t\t\"SVGFEBlendElement\": false,\n\t\t\"SVGFEColorMatrixElement\": false,\n\t\t\"SVGFEComponentTransferElement\": false,\n\t\t\"SVGFECompositeElement\": false,\n\t\t\"SVGFEConvolveMatrixElement\": false,\n\t\t\"SVGFEDiffuseLightingElement\": false,\n\t\t\"SVGFEDisplacementMapElement\": false,\n\t\t\"SVGFEDistantLightElement\": false,\n\t\t\"SVGFEDropShadowElement\": false,\n\t\t\"SVGFEFloodElement\": false,\n\t\t\"SVGFEFuncAElement\": false,\n\t\t\"SVGFEFuncBElement\": false,\n\t\t\"SVGFEFuncGElement\": false,\n\t\t\"SVGFEFuncRElement\": false,\n\t\t\"SVGFEGaussianBlurElement\": false,\n\t\t\"SVGFEImageElement\": false,\n\t\t\"SVGFEMergeElement\": false,\n\t\t\"SVGFEMergeNodeElement\": false,\n\t\t\"SVGFEMorphologyElement\": false,\n\t\t\"SVGFEOffsetElement\": false,\n\t\t\"SVGFEPointLightElement\": false,\n\t\t\"SVGFESpecularLightingElement\": false,\n\t\t\"SVGFESpotLightElement\": false,\n\t\t\"SVGFETileElement\": false,\n\t\t\"SVGFETurbulenceElement\": false,\n\t\t\"SVGFilterElement\": false,\n\t\t\"SVGForeignObjectElement\": false,\n\t\t\"SVGGElement\": false,\n\t\t\"SVGGeometryElement\": false,\n\t\t\"SVGGradientElement\": false,\n\t\t\"SVGGraphicsElement\": false,\n\t\t\"SVGImageElement\": false,\n\t\t\"SVGLength\": false,\n\t\t\"SVGLengthList\": false,\n\t\t\"SVGLinearGradientElement\": false,\n\t\t\"SVGLineElement\": false,\n\t\t\"SVGMarkerElement\": false,\n\t\t\"SVGMaskElement\": false,\n\t\t\"SVGMatrix\": false,\n\t\t\"SVGMetadataElement\": false,\n\t\t\"SVGMPathElement\": false,\n\t\t\"SVGNumber\": false,\n\t\t\"SVGNumberList\": false,\n\t\t\"SVGPathElement\": false,\n\t\t\"SVGPatternElement\": false,\n\t\t\"SVGPoint\": false,\n\t\t\"SVGPointList\": false,\n\t\t\"SVGPolygonElement\": false,\n\t\t\"SVGPolylineElement\": false,\n\t\t\"SVGPreserveAspectRatio\": false,\n\t\t\"SVGRadialGradientElement\": false,\n\t\t\"SVGRect\": false,\n\t\t\"SVGRectElement\": false,\n\t\t\"SVGScriptElement\": false,\n\t\t\"SVGSetElement\": false,\n\t\t\"SVGStopElement\": false,\n\t\t\"SVGStringList\": false,\n\t\t\"SVGStyleElement\": false,\n\t\t\"SVGSVGElement\": false,\n\t\t\"SVGSwitchElement\": false,\n\t\t\"SVGSymbolElement\": false,\n\t\t\"SVGTextContentElement\": false,\n\t\t\"SVGTextElement\": false,\n\t\t\"SVGTextPathElement\": false,\n\t\t\"SVGTextPositioningElement\": false,\n\t\t\"SVGTitleElement\": false,\n\t\t\"SVGTransform\": false,\n\t\t\"SVGTransformList\": false,\n\t\t\"SVGTSpanElement\": false,\n\t\t\"SVGUnitTypes\": false,\n\t\t\"SVGUseElement\": false,\n\t\t\"SVGViewElement\": false,\n\t\t\"SyncManager\": false,\n\t\t\"TaskAttributionTiming\": false,\n\t\t\"TaskController\": false,\n\t\t\"TaskPriorityChangeEvent\": false,\n\t\t\"TaskSignal\": false,\n\t\t\"TEMPORARY\": false,\n\t\t\"Text\": false,\n\t\t\"TextDecoder\": false,\n\t\t\"TextDecoderStream\": false,\n\t\t\"TextEncoder\": false,\n\t\t\"TextEncoderStream\": false,\n\t\t\"TextEvent\": false,\n\t\t\"TextFormat\": false,\n\t\t\"TextFormatUpdateEvent\": false,\n\t\t\"TextMetrics\": false,\n\t\t\"TextTrack\": false,\n\t\t\"TextTrackCue\": false,\n\t\t\"TextTrackCueList\": false,\n\t\t\"TextTrackList\": false,\n\t\t\"TextUpdateEvent\": false,\n\t\t\"TimeEvent\": false,\n\t\t\"TimeRanges\": false,\n\t\t\"ToggleEvent\": false,\n\t\t\"toolbar\": false,\n\t\t\"top\": false,\n\t\t\"Touch\": false,\n\t\t\"TouchEvent\": false,\n\t\t\"TouchList\": false,\n\t\t\"TrackEvent\": false,\n\t\t\"TransformStream\": false,\n\t\t\"TransformStreamDefaultController\": false,\n\t\t\"TransitionEvent\": false,\n\t\t\"TreeWalker\": false,\n\t\t\"TrustedHTML\": false,\n\t\t\"TrustedScript\": false,\n\t\t\"TrustedScriptURL\": false,\n\t\t\"TrustedTypePolicy\": false,\n\t\t\"TrustedTypePolicyFactory\": false,\n\t\t\"trustedTypes\": false,\n\t\t\"UIEvent\": false,\n\t\t\"URL\": false,\n\t\t\"URLPattern\": false,\n\t\t\"URLSearchParams\": false,\n\t\t\"USB\": false,\n\t\t\"USBAlternateInterface\": false,\n\t\t\"USBConfiguration\": false,\n\t\t\"USBConnectionEvent\": false,\n\t\t\"USBDevice\": false,\n\t\t\"USBEndpoint\": false,\n\t\t\"USBInterface\": false,\n\t\t\"USBInTransferResult\": false,\n\t\t\"USBIsochronousInTransferPacket\": false,\n\t\t\"USBIsochronousInTransferResult\": false,\n\t\t\"USBIsochronousOutTransferPacket\": false,\n\t\t\"USBIsochronousOutTransferResult\": false,\n\t\t\"USBOutTransferResult\": false,\n\t\t\"UserActivation\": false,\n\t\t\"ValidityState\": false,\n\t\t\"VideoColorSpace\": false,\n\t\t\"VideoDecoder\": false,\n\t\t\"VideoEncoder\": false,\n\t\t\"VideoFrame\": false,\n\t\t\"VideoPlaybackQuality\": false,\n\t\t\"ViewTimeline\": false,\n\t\t\"ViewTransition\": false,\n\t\t\"ViewTransitionTypeSet\": false,\n\t\t\"VirtualKeyboard\": false,\n\t\t\"VirtualKeyboardGeometryChangeEvent\": false,\n\t\t\"VisibilityStateEntry\": false,\n\t\t\"visualViewport\": false,\n\t\t\"VisualViewport\": false,\n\t\t\"VTTCue\": false,\n\t\t\"VTTRegion\": false,\n\t\t\"WakeLock\": false,\n\t\t\"WakeLockSentinel\": false,\n\t\t\"WaveShaperNode\": false,\n\t\t\"WebAssembly\": false,\n\t\t\"WebGL2RenderingContext\": false,\n\t\t\"WebGLActiveInfo\": false,\n\t\t\"WebGLBuffer\": false,\n\t\t\"WebGLContextEvent\": false,\n\t\t\"WebGLFramebuffer\": false,\n\t\t\"WebGLObject\": false,\n\t\t\"WebGLProgram\": false,\n\t\t\"WebGLQuery\": false,\n\t\t\"WebGLRenderbuffer\": false,\n\t\t\"WebGLRenderingContext\": false,\n\t\t\"WebGLSampler\": false,\n\t\t\"WebGLShader\": false,\n\t\t\"WebGLShaderPrecisionFormat\": false,\n\t\t\"WebGLSync\": false,\n\t\t\"WebGLTexture\": false,\n\t\t\"WebGLTransformFeedback\": false,\n\t\t\"WebGLUniformLocation\": false,\n\t\t\"WebGLVertexArrayObject\": false,\n\t\t\"WebSocket\": false,\n\t\t\"WebSocketError\": false,\n\t\t\"WebSocketStream\": false,\n\t\t\"WebTransport\": false,\n\t\t\"WebTransportBidirectionalStream\": false,\n\t\t\"WebTransportDatagramDuplexStream\": false,\n\t\t\"WebTransportError\": false,\n\t\t\"WebTransportReceiveStream\": false,\n\t\t\"WebTransportSendStream\": false,\n\t\t\"WGSLLanguageFeatures\": false,\n\t\t\"WheelEvent\": false,\n\t\t\"when\": false,\n\t\t\"window\": false,\n\t\t\"Window\": false,\n\t\t\"WindowControlsOverlay\": false,\n\t\t\"WindowControlsOverlayGeometryChangeEvent\": false,\n\t\t\"Worker\": false,\n\t\t\"Worklet\": false,\n\t\t\"WorkletGlobalScope\": false,\n\t\t\"WritableStream\": false,\n\t\t\"WritableStreamDefaultController\": false,\n\t\t\"WritableStreamDefaultWriter\": false,\n\t\t\"XMLDocument\": false,\n\t\t\"XMLHttpRequest\": false,\n\t\t\"XMLHttpRequestEventTarget\": false,\n\t\t\"XMLHttpRequestUpload\": false,\n\t\t\"XMLSerializer\": false,\n\t\t\"XPathEvaluator\": false,\n\t\t\"XPathExpression\": false,\n\t\t\"XPathResult\": false,\n\t\t\"XRAnchor\": false,\n\t\t\"XRAnchorSet\": false,\n\t\t\"XRBoundedReferenceSpace\": false,\n\t\t\"XRCamera\": false,\n\t\t\"XRCPUDepthInformation\": false,\n\t\t\"XRDepthInformation\": false,\n\t\t\"XRDOMOverlayState\": false,\n\t\t\"XRFrame\": false,\n\t\t\"XRHand\": false,\n\t\t\"XRHitTestResult\": false,\n\t\t\"XRHitTestSource\": false,\n\t\t\"XRInputSource\": false,\n\t\t\"XRInputSourceArray\": false,\n\t\t\"XRInputSourceEvent\": false,\n\t\t\"XRInputSourcesChangeEvent\": false,\n\t\t\"XRJointPose\": false,\n\t\t\"XRJointSpace\": false,\n\t\t\"XRLayer\": false,\n\t\t\"XRLightEstimate\": false,\n\t\t\"XRLightProbe\": false,\n\t\t\"XRPose\": false,\n\t\t\"XRRay\": false,\n\t\t\"XRReferenceSpace\": false,\n\t\t\"XRReferenceSpaceEvent\": false,\n\t\t\"XRRenderState\": false,\n\t\t\"XRRigidTransform\": false,\n\t\t\"XRSession\": false,\n\t\t\"XRSessionEvent\": false,\n\t\t\"XRSpace\": false,\n\t\t\"XRSystem\": false,\n\t\t\"XRTransientInputHitTestResult\": false,\n\t\t\"XRTransientInputHitTestSource\": false,\n\t\t\"XRView\": false,\n\t\t\"XRViewerPose\": false,\n\t\t\"XRViewport\": false,\n\t\t\"XRWebGLBinding\": false,\n\t\t\"XRWebGLDepthInformation\": false,\n\t\t\"XRWebGLLayer\": false,\n\t\t\"XSLTProcessor\": false\n\t},\n\t\"builtin\": {\n\t\t\"AggregateError\": false,\n\t\t\"Array\": false,\n\t\t\"ArrayBuffer\": false,\n\t\t\"Atomics\": false,\n\t\t\"BigInt\": false,\n\t\t\"BigInt64Array\": false,\n\t\t\"BigUint64Array\": false,\n\t\t\"Boolean\": false,\n\t\t\"DataView\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"FinalizationRegistry\": false,\n\t\t\"Float16Array\": false,\n\t\t\"Float32Array\": false,\n\t\t\"Float64Array\": false,\n\t\t\"Function\": false,\n\t\t\"globalThis\": false,\n\t\t\"Infinity\": false,\n\t\t\"Int16Array\": false,\n\t\t\"Int32Array\": false,\n\t\t\"Int8Array\": false,\n\t\t\"Intl\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"Iterator\": false,\n\t\t\"JSON\": false,\n\t\t\"Map\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"Promise\": false,\n\t\t\"Proxy\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"Reflect\": false,\n\t\t\"RegExp\": false,\n\t\t\"Set\": false,\n\t\t\"SharedArrayBuffer\": false,\n\t\t\"String\": false,\n\t\t\"Symbol\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"Uint16Array\": false,\n\t\t\"Uint32Array\": false,\n\t\t\"Uint8Array\": false,\n\t\t\"Uint8ClampedArray\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false,\n\t\t\"WeakMap\": false,\n\t\t\"WeakRef\": false,\n\t\t\"WeakSet\": false\n\t},\n\t\"chai\": {\n\t\t\"assert\": true,\n\t\t\"expect\": true,\n\t\t\"should\": true\n\t},\n\t\"commonjs\": {\n\t\t\"exports\": true,\n\t\t\"global\": false,\n\t\t\"module\": false,\n\t\t\"require\": false\n\t},\n\t\"couch\": {\n\t\t\"emit\": false,\n\t\t\"exports\": false,\n\t\t\"getRow\": false,\n\t\t\"log\": false,\n\t\t\"module\": false,\n\t\t\"provides\": false,\n\t\t\"require\": false,\n\t\t\"respond\": false,\n\t\t\"send\": false,\n\t\t\"start\": false,\n\t\t\"sum\": false\n\t},\n\t\"devtools\": {\n\t\t\"$\": false,\n\t\t\"$_\": false,\n\t\t\"$$\": false,\n\t\t\"$0\": false,\n\t\t\"$1\": false,\n\t\t\"$2\": false,\n\t\t\"$3\": false,\n\t\t\"$4\": false,\n\t\t\"$x\": false,\n\t\t\"chrome\": false,\n\t\t\"clear\": false,\n\t\t\"copy\": false,\n\t\t\"debug\": false,\n\t\t\"dir\": false,\n\t\t\"dirxml\": false,\n\t\t\"getEventListeners\": false,\n\t\t\"inspect\": false,\n\t\t\"keys\": false,\n\t\t\"monitor\": false,\n\t\t\"monitorEvents\": false,\n\t\t\"profile\": false,\n\t\t\"profileEnd\": false,\n\t\t\"queryObjects\": false,\n\t\t\"table\": false,\n\t\t\"undebug\": false,\n\t\t\"unmonitor\": false,\n\t\t\"unmonitorEvents\": false,\n\t\t\"values\": false\n\t},\n\t\"embertest\": {\n\t\t\"andThen\": false,\n\t\t\"click\": false,\n\t\t\"currentPath\": false,\n\t\t\"currentRouteName\": false,\n\t\t\"currentURL\": false,\n\t\t\"fillIn\": false,\n\t\t\"find\": false,\n\t\t\"findAll\": false,\n\t\t\"findWithAssert\": false,\n\t\t\"keyEvent\": false,\n\t\t\"pauseTest\": false,\n\t\t\"resumeTest\": false,\n\t\t\"triggerEvent\": false,\n\t\t\"visit\": false,\n\t\t\"wait\": false\n\t},\n\t\"es2015\": {\n\t\t\"Array\": false,\n\t\t\"ArrayBuffer\": false,\n\t\t\"Boolean\": false,\n\t\t\"DataView\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"Float32Array\": false,\n\t\t\"Float64Array\": false,\n\t\t\"Function\": false,\n\t\t\"Infinity\": false,\n\t\t\"Int16Array\": false,\n\t\t\"Int32Array\": false,\n\t\t\"Int8Array\": false,\n\t\t\"Intl\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"JSON\": false,\n\t\t\"Map\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"Promise\": false,\n\t\t\"Proxy\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"Reflect\": false,\n\t\t\"RegExp\": false,\n\t\t\"Set\": false,\n\t\t\"String\": false,\n\t\t\"Symbol\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"Uint16Array\": false,\n\t\t\"Uint32Array\": false,\n\t\t\"Uint8Array\": false,\n\t\t\"Uint8ClampedArray\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false,\n\t\t\"WeakMap\": false,\n\t\t\"WeakSet\": false\n\t},\n\t\"es2016\": {\n\t\t\"Array\": false,\n\t\t\"ArrayBuffer\": false,\n\t\t\"Boolean\": false,\n\t\t\"DataView\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"Float32Array\": false,\n\t\t\"Float64Array\": false,\n\t\t\"Function\": false,\n\t\t\"Infinity\": false,\n\t\t\"Int16Array\": false,\n\t\t\"Int32Array\": false,\n\t\t\"Int8Array\": false,\n\t\t\"Intl\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"JSON\": false,\n\t\t\"Map\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"Promise\": false,\n\t\t\"Proxy\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"Reflect\": false,\n\t\t\"RegExp\": false,\n\t\t\"Set\": false,\n\t\t\"String\": false,\n\t\t\"Symbol\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"Uint16Array\": false,\n\t\t\"Uint32Array\": false,\n\t\t\"Uint8Array\": false,\n\t\t\"Uint8ClampedArray\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false,\n\t\t\"WeakMap\": false,\n\t\t\"WeakSet\": false\n\t},\n\t\"es2017\": {\n\t\t\"Array\": false,\n\t\t\"ArrayBuffer\": false,\n\t\t\"Atomics\": false,\n\t\t\"Boolean\": false,\n\t\t\"DataView\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"Float32Array\": false,\n\t\t\"Float64Array\": false,\n\t\t\"Function\": false,\n\t\t\"Infinity\": false,\n\t\t\"Int16Array\": false,\n\t\t\"Int32Array\": false,\n\t\t\"Int8Array\": false,\n\t\t\"Intl\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"JSON\": false,\n\t\t\"Map\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"Promise\": false,\n\t\t\"Proxy\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"Reflect\": false,\n\t\t\"RegExp\": false,\n\t\t\"Set\": false,\n\t\t\"SharedArrayBuffer\": false,\n\t\t\"String\": false,\n\t\t\"Symbol\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"Uint16Array\": false,\n\t\t\"Uint32Array\": false,\n\t\t\"Uint8Array\": false,\n\t\t\"Uint8ClampedArray\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false,\n\t\t\"WeakMap\": false,\n\t\t\"WeakSet\": false\n\t},\n\t\"es2018\": {\n\t\t\"Array\": false,\n\t\t\"ArrayBuffer\": false,\n\t\t\"Atomics\": false,\n\t\t\"Boolean\": false,\n\t\t\"DataView\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"Float32Array\": false,\n\t\t\"Float64Array\": false,\n\t\t\"Function\": false,\n\t\t\"Infinity\": false,\n\t\t\"Int16Array\": false,\n\t\t\"Int32Array\": false,\n\t\t\"Int8Array\": false,\n\t\t\"Intl\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"JSON\": false,\n\t\t\"Map\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"Promise\": false,\n\t\t\"Proxy\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"Reflect\": false,\n\t\t\"RegExp\": false,\n\t\t\"Set\": false,\n\t\t\"SharedArrayBuffer\": false,\n\t\t\"String\": false,\n\t\t\"Symbol\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"Uint16Array\": false,\n\t\t\"Uint32Array\": false,\n\t\t\"Uint8Array\": false,\n\t\t\"Uint8ClampedArray\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false,\n\t\t\"WeakMap\": false,\n\t\t\"WeakSet\": false\n\t},\n\t\"es2019\": {\n\t\t\"Array\": false,\n\t\t\"ArrayBuffer\": false,\n\t\t\"Atomics\": false,\n\t\t\"Boolean\": false,\n\t\t\"DataView\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"Float32Array\": false,\n\t\t\"Float64Array\": false,\n\t\t\"Function\": false,\n\t\t\"Infinity\": false,\n\t\t\"Int16Array\": false,\n\t\t\"Int32Array\": false,\n\t\t\"Int8Array\": false,\n\t\t\"Intl\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"JSON\": false,\n\t\t\"Map\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"Promise\": false,\n\t\t\"Proxy\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"Reflect\": false,\n\t\t\"RegExp\": false,\n\t\t\"Set\": false,\n\t\t\"SharedArrayBuffer\": false,\n\t\t\"String\": false,\n\t\t\"Symbol\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"Uint16Array\": false,\n\t\t\"Uint32Array\": false,\n\t\t\"Uint8Array\": false,\n\t\t\"Uint8ClampedArray\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false,\n\t\t\"WeakMap\": false,\n\t\t\"WeakSet\": false\n\t},\n\t\"es2020\": {\n\t\t\"Array\": false,\n\t\t\"ArrayBuffer\": false,\n\t\t\"Atomics\": false,\n\t\t\"BigInt\": false,\n\t\t\"BigInt64Array\": false,\n\t\t\"BigUint64Array\": false,\n\t\t\"Boolean\": false,\n\t\t\"DataView\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"Float32Array\": false,\n\t\t\"Float64Array\": false,\n\t\t\"Function\": false,\n\t\t\"globalThis\": false,\n\t\t\"Infinity\": false,\n\t\t\"Int16Array\": false,\n\t\t\"Int32Array\": false,\n\t\t\"Int8Array\": false,\n\t\t\"Intl\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"JSON\": false,\n\t\t\"Map\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"Promise\": false,\n\t\t\"Proxy\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"Reflect\": false,\n\t\t\"RegExp\": false,\n\t\t\"Set\": false,\n\t\t\"SharedArrayBuffer\": false,\n\t\t\"String\": false,\n\t\t\"Symbol\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"Uint16Array\": false,\n\t\t\"Uint32Array\": false,\n\t\t\"Uint8Array\": false,\n\t\t\"Uint8ClampedArray\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false,\n\t\t\"WeakMap\": false,\n\t\t\"WeakSet\": false\n\t},\n\t\"es2021\": {\n\t\t\"AggregateError\": false,\n\t\t\"Array\": false,\n\t\t\"ArrayBuffer\": false,\n\t\t\"Atomics\": false,\n\t\t\"BigInt\": false,\n\t\t\"BigInt64Array\": false,\n\t\t\"BigUint64Array\": false,\n\t\t\"Boolean\": false,\n\t\t\"DataView\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"FinalizationRegistry\": false,\n\t\t\"Float32Array\": false,\n\t\t\"Float64Array\": false,\n\t\t\"Function\": false,\n\t\t\"globalThis\": false,\n\t\t\"Infinity\": false,\n\t\t\"Int16Array\": false,\n\t\t\"Int32Array\": false,\n\t\t\"Int8Array\": false,\n\t\t\"Intl\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"JSON\": false,\n\t\t\"Map\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"Promise\": false,\n\t\t\"Proxy\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"Reflect\": false,\n\t\t\"RegExp\": false,\n\t\t\"Set\": false,\n\t\t\"SharedArrayBuffer\": false,\n\t\t\"String\": false,\n\t\t\"Symbol\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"Uint16Array\": false,\n\t\t\"Uint32Array\": false,\n\t\t\"Uint8Array\": false,\n\t\t\"Uint8ClampedArray\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false,\n\t\t\"WeakMap\": false,\n\t\t\"WeakRef\": false,\n\t\t\"WeakSet\": false\n\t},\n\t\"es2022\": {\n\t\t\"AggregateError\": false,\n\t\t\"Array\": false,\n\t\t\"ArrayBuffer\": false,\n\t\t\"Atomics\": false,\n\t\t\"BigInt\": false,\n\t\t\"BigInt64Array\": false,\n\t\t\"BigUint64Array\": false,\n\t\t\"Boolean\": false,\n\t\t\"DataView\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"FinalizationRegistry\": false,\n\t\t\"Float32Array\": false,\n\t\t\"Float64Array\": false,\n\t\t\"Function\": false,\n\t\t\"globalThis\": false,\n\t\t\"Infinity\": false,\n\t\t\"Int16Array\": false,\n\t\t\"Int32Array\": false,\n\t\t\"Int8Array\": false,\n\t\t\"Intl\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"JSON\": false,\n\t\t\"Map\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"Promise\": false,\n\t\t\"Proxy\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"Reflect\": false,\n\t\t\"RegExp\": false,\n\t\t\"Set\": false,\n\t\t\"SharedArrayBuffer\": false,\n\t\t\"String\": false,\n\t\t\"Symbol\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"Uint16Array\": false,\n\t\t\"Uint32Array\": false,\n\t\t\"Uint8Array\": false,\n\t\t\"Uint8ClampedArray\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false,\n\t\t\"WeakMap\": false,\n\t\t\"WeakRef\": false,\n\t\t\"WeakSet\": false\n\t},\n\t\"es2023\": {\n\t\t\"AggregateError\": false,\n\t\t\"Array\": false,\n\t\t\"ArrayBuffer\": false,\n\t\t\"Atomics\": false,\n\t\t\"BigInt\": false,\n\t\t\"BigInt64Array\": false,\n\t\t\"BigUint64Array\": false,\n\t\t\"Boolean\": false,\n\t\t\"DataView\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"FinalizationRegistry\": false,\n\t\t\"Float32Array\": false,\n\t\t\"Float64Array\": false,\n\t\t\"Function\": false,\n\t\t\"globalThis\": false,\n\t\t\"Infinity\": false,\n\t\t\"Int16Array\": false,\n\t\t\"Int32Array\": false,\n\t\t\"Int8Array\": false,\n\t\t\"Intl\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"JSON\": false,\n\t\t\"Map\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"Promise\": false,\n\t\t\"Proxy\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"Reflect\": false,\n\t\t\"RegExp\": false,\n\t\t\"Set\": false,\n\t\t\"SharedArrayBuffer\": false,\n\t\t\"String\": false,\n\t\t\"Symbol\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"Uint16Array\": false,\n\t\t\"Uint32Array\": false,\n\t\t\"Uint8Array\": false,\n\t\t\"Uint8ClampedArray\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false,\n\t\t\"WeakMap\": false,\n\t\t\"WeakRef\": false,\n\t\t\"WeakSet\": false\n\t},\n\t\"es2024\": {\n\t\t\"AggregateError\": false,\n\t\t\"Array\": false,\n\t\t\"ArrayBuffer\": false,\n\t\t\"Atomics\": false,\n\t\t\"BigInt\": false,\n\t\t\"BigInt64Array\": false,\n\t\t\"BigUint64Array\": false,\n\t\t\"Boolean\": false,\n\t\t\"DataView\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"FinalizationRegistry\": false,\n\t\t\"Float32Array\": false,\n\t\t\"Float64Array\": false,\n\t\t\"Function\": false,\n\t\t\"globalThis\": false,\n\t\t\"Infinity\": false,\n\t\t\"Int16Array\": false,\n\t\t\"Int32Array\": false,\n\t\t\"Int8Array\": false,\n\t\t\"Intl\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"JSON\": false,\n\t\t\"Map\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"Promise\": false,\n\t\t\"Proxy\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"Reflect\": false,\n\t\t\"RegExp\": false,\n\t\t\"Set\": false,\n\t\t\"SharedArrayBuffer\": false,\n\t\t\"String\": false,\n\t\t\"Symbol\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"Uint16Array\": false,\n\t\t\"Uint32Array\": false,\n\t\t\"Uint8Array\": false,\n\t\t\"Uint8ClampedArray\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false,\n\t\t\"WeakMap\": false,\n\t\t\"WeakRef\": false,\n\t\t\"WeakSet\": false\n\t},\n\t\"es2025\": {\n\t\t\"AggregateError\": false,\n\t\t\"Array\": false,\n\t\t\"ArrayBuffer\": false,\n\t\t\"Atomics\": false,\n\t\t\"BigInt\": false,\n\t\t\"BigInt64Array\": false,\n\t\t\"BigUint64Array\": false,\n\t\t\"Boolean\": false,\n\t\t\"DataView\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"FinalizationRegistry\": false,\n\t\t\"Float16Array\": false,\n\t\t\"Float32Array\": false,\n\t\t\"Float64Array\": false,\n\t\t\"Function\": false,\n\t\t\"globalThis\": false,\n\t\t\"Infinity\": false,\n\t\t\"Int16Array\": false,\n\t\t\"Int32Array\": false,\n\t\t\"Int8Array\": false,\n\t\t\"Intl\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"Iterator\": false,\n\t\t\"JSON\": false,\n\t\t\"Map\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"Promise\": false,\n\t\t\"Proxy\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"Reflect\": false,\n\t\t\"RegExp\": false,\n\t\t\"Set\": false,\n\t\t\"SharedArrayBuffer\": false,\n\t\t\"String\": false,\n\t\t\"Symbol\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"Uint16Array\": false,\n\t\t\"Uint32Array\": false,\n\t\t\"Uint8Array\": false,\n\t\t\"Uint8ClampedArray\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false,\n\t\t\"WeakMap\": false,\n\t\t\"WeakRef\": false,\n\t\t\"WeakSet\": false\n\t},\n\t\"es3\": {\n\t\t\"Array\": false,\n\t\t\"Boolean\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"Function\": false,\n\t\t\"Infinity\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"RegExp\": false,\n\t\t\"String\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false\n\t},\n\t\"es5\": {\n\t\t\"Array\": false,\n\t\t\"Boolean\": false,\n\t\t\"Date\": false,\n\t\t\"decodeURI\": false,\n\t\t\"decodeURIComponent\": false,\n\t\t\"encodeURI\": false,\n\t\t\"encodeURIComponent\": false,\n\t\t\"Error\": false,\n\t\t\"escape\": false,\n\t\t\"eval\": false,\n\t\t\"EvalError\": false,\n\t\t\"Function\": false,\n\t\t\"Infinity\": false,\n\t\t\"isFinite\": false,\n\t\t\"isNaN\": false,\n\t\t\"JSON\": false,\n\t\t\"Math\": false,\n\t\t\"NaN\": false,\n\t\t\"Number\": false,\n\t\t\"Object\": false,\n\t\t\"parseFloat\": false,\n\t\t\"parseInt\": false,\n\t\t\"RangeError\": false,\n\t\t\"ReferenceError\": false,\n\t\t\"RegExp\": false,\n\t\t\"String\": false,\n\t\t\"SyntaxError\": false,\n\t\t\"TypeError\": false,\n\t\t\"undefined\": false,\n\t\t\"unescape\": false,\n\t\t\"URIError\": false\n\t},\n\t\"greasemonkey\": {\n\t\t\"cloneInto\": false,\n\t\t\"createObjectIn\": false,\n\t\t\"exportFunction\": false,\n\t\t\"GM\": false,\n\t\t\"GM_addElement\": false,\n\t\t\"GM_addStyle\": false,\n\t\t\"GM_addValueChangeListener\": false,\n\t\t\"GM_deleteValue\": false,\n\t\t\"GM_deleteValues\": false,\n\t\t\"GM_download\": false,\n\t\t\"GM_getResourceText\": false,\n\t\t\"GM_getResourceURL\": false,\n\t\t\"GM_getTab\": false,\n\t\t\"GM_getTabs\": false,\n\t\t\"GM_getValue\": false,\n\t\t\"GM_getValues\": false,\n\t\t\"GM_info\": false,\n\t\t\"GM_listValues\": false,\n\t\t\"GM_log\": false,\n\t\t\"GM_notification\": false,\n\t\t\"GM_openInTab\": false,\n\t\t\"GM_registerMenuCommand\": false,\n\t\t\"GM_removeValueChangeListener\": false,\n\t\t\"GM_saveTab\": false,\n\t\t\"GM_setClipboard\": false,\n\t\t\"GM_setValue\": false,\n\t\t\"GM_setValues\": false,\n\t\t\"GM_unregisterMenuCommand\": false,\n\t\t\"GM_xmlhttpRequest\": false,\n\t\t\"unsafeWindow\": false\n\t},\n\t\"jasmine\": {\n\t\t\"afterAll\": false,\n\t\t\"afterEach\": false,\n\t\t\"beforeAll\": false,\n\t\t\"beforeEach\": false,\n\t\t\"describe\": false,\n\t\t\"expect\": false,\n\t\t\"expectAsync\": false,\n\t\t\"fail\": false,\n\t\t\"fdescribe\": false,\n\t\t\"fit\": false,\n\t\t\"it\": false,\n\t\t\"jasmine\": false,\n\t\t\"pending\": false,\n\t\t\"runs\": false,\n\t\t\"spyOn\": false,\n\t\t\"spyOnAllFunctions\": false,\n\t\t\"spyOnProperty\": false,\n\t\t\"waits\": false,\n\t\t\"waitsFor\": false,\n\t\t\"xdescribe\": false,\n\t\t\"xit\": false\n\t},\n\t\"jest\": {\n\t\t\"afterAll\": false,\n\t\t\"afterEach\": false,\n\t\t\"beforeAll\": false,\n\t\t\"beforeEach\": false,\n\t\t\"describe\": false,\n\t\t\"expect\": false,\n\t\t\"fit\": false,\n\t\t\"it\": false,\n\t\t\"jest\": false,\n\t\t\"test\": false,\n\t\t\"xdescribe\": false,\n\t\t\"xit\": false,\n\t\t\"xtest\": false\n\t},\n\t\"jquery\": {\n\t\t\"$\": false,\n\t\t\"jQuery\": false\n\t},\n\t\"meteor\": {\n\t\t\"$\": false,\n\t\t\"Accounts\": false,\n\t\t\"AccountsClient\": false,\n\t\t\"AccountsCommon\": false,\n\t\t\"AccountsServer\": false,\n\t\t\"App\": false,\n\t\t\"Assets\": false,\n\t\t\"Blaze\": false,\n\t\t\"check\": false,\n\t\t\"Cordova\": false,\n\t\t\"DDP\": false,\n\t\t\"DDPRateLimiter\": false,\n\t\t\"DDPServer\": false,\n\t\t\"Deps\": false,\n\t\t\"EJSON\": false,\n\t\t\"Email\": false,\n\t\t\"HTTP\": false,\n\t\t\"Log\": false,\n\t\t\"Match\": false,\n\t\t\"Meteor\": false,\n\t\t\"Mongo\": false,\n\t\t\"MongoInternals\": false,\n\t\t\"Npm\": false,\n\t\t\"Package\": false,\n\t\t\"Plugin\": false,\n\t\t\"process\": false,\n\t\t\"Random\": false,\n\t\t\"ReactiveDict\": false,\n\t\t\"ReactiveVar\": false,\n\t\t\"Router\": false,\n\t\t\"ServiceConfiguration\": false,\n\t\t\"Session\": false,\n\t\t\"share\": false,\n\t\t\"Spacebars\": false,\n\t\t\"Template\": false,\n\t\t\"Tinytest\": false,\n\t\t\"Tracker\": false,\n\t\t\"UI\": false,\n\t\t\"Utils\": false,\n\t\t\"WebApp\": false,\n\t\t\"WebAppInternals\": false\n\t},\n\t\"mocha\": {\n\t\t\"after\": false,\n\t\t\"afterEach\": false,\n\t\t\"before\": false,\n\t\t\"beforeEach\": false,\n\t\t\"context\": false,\n\t\t\"describe\": false,\n\t\t\"it\": false,\n\t\t\"mocha\": false,\n\t\t\"run\": false,\n\t\t\"setup\": false,\n\t\t\"specify\": false,\n\t\t\"suite\": false,\n\t\t\"suiteSetup\": false,\n\t\t\"suiteTeardown\": false,\n\t\t\"teardown\": false,\n\t\t\"test\": false,\n\t\t\"xcontext\": false,\n\t\t\"xdescribe\": false,\n\t\t\"xit\": false,\n\t\t\"xspecify\": false\n\t},\n\t\"mongo\": {\n\t\t\"_isWindows\": false,\n\t\t\"_rand\": false,\n\t\t\"BulkWriteResult\": false,\n\t\t\"cat\": false,\n\t\t\"cd\": false,\n\t\t\"connect\": false,\n\t\t\"db\": false,\n\t\t\"getHostName\": false,\n\t\t\"getMemInfo\": false,\n\t\t\"hostname\": false,\n\t\t\"ISODate\": false,\n\t\t\"listFiles\": false,\n\t\t\"load\": false,\n\t\t\"ls\": false,\n\t\t\"md5sumFile\": false,\n\t\t\"mkdir\": false,\n\t\t\"Mongo\": false,\n\t\t\"NumberInt\": false,\n\t\t\"NumberLong\": false,\n\t\t\"ObjectId\": false,\n\t\t\"PlanCache\": false,\n\t\t\"print\": false,\n\t\t\"printjson\": false,\n\t\t\"pwd\": false,\n\t\t\"quit\": false,\n\t\t\"removeFile\": false,\n\t\t\"rs\": false,\n\t\t\"sh\": false,\n\t\t\"UUID\": false,\n\t\t\"version\": false,\n\t\t\"WriteResult\": false\n\t},\n\t\"nashorn\": {\n\t\t\"__DIR__\": false,\n\t\t\"__FILE__\": false,\n\t\t\"__LINE__\": false,\n\t\t\"com\": false,\n\t\t\"edu\": false,\n\t\t\"exit\": false,\n\t\t\"java\": false,\n\t\t\"Java\": false,\n\t\t\"javafx\": false,\n\t\t\"JavaImporter\": false,\n\t\t\"javax\": false,\n\t\t\"JSAdapter\": false,\n\t\t\"load\": false,\n\t\t\"loadWithNewGlobal\": false,\n\t\t\"org\": false,\n\t\t\"Packages\": false,\n\t\t\"print\": false,\n\t\t\"quit\": false\n\t},\n\t\"node\": {\n\t\t\"__dirname\": false,\n\t\t\"__filename\": false,\n\t\t\"AbortController\": false,\n\t\t\"AbortSignal\": false,\n\t\t\"AsyncDisposableStack\": false,\n\t\t\"atob\": false,\n\t\t\"Blob\": false,\n\t\t\"BroadcastChannel\": false,\n\t\t\"btoa\": false,\n\t\t\"Buffer\": false,\n\t\t\"ByteLengthQueuingStrategy\": false,\n\t\t\"clearImmediate\": false,\n\t\t\"clearInterval\": false,\n\t\t\"clearTimeout\": false,\n\t\t\"CloseEvent\": false,\n\t\t\"CompressionStream\": false,\n\t\t\"console\": false,\n\t\t\"CountQueuingStrategy\": false,\n\t\t\"crypto\": false,\n\t\t\"Crypto\": false,\n\t\t\"CryptoKey\": false,\n\t\t\"CustomEvent\": false,\n\t\t\"DecompressionStream\": false,\n\t\t\"DisposableStack\": false,\n\t\t\"DOMException\": false,\n\t\t\"Event\": false,\n\t\t\"EventTarget\": false,\n\t\t\"exports\": true,\n\t\t\"fetch\": false,\n\t\t\"File\": false,\n\t\t\"FormData\": false,\n\t\t\"global\": false,\n\t\t\"Headers\": false,\n\t\t\"MessageChannel\": false,\n\t\t\"MessageEvent\": false,\n\t\t\"MessagePort\": false,\n\t\t\"module\": false,\n\t\t\"navigator\": false,\n\t\t\"Navigator\": false,\n\t\t\"performance\": false,\n\t\t\"Performance\": false,\n\t\t\"PerformanceEntry\": false,\n\t\t\"PerformanceMark\": false,\n\t\t\"PerformanceMeasure\": false,\n\t\t\"PerformanceObserver\": false,\n\t\t\"PerformanceObserverEntryList\": false,\n\t\t\"PerformanceResourceTiming\": false,\n\t\t\"process\": false,\n\t\t\"queueMicrotask\": false,\n\t\t\"ReadableByteStreamController\": false,\n\t\t\"ReadableStream\": false,\n\t\t\"ReadableStreamBYOBReader\": false,\n\t\t\"ReadableStreamBYOBRequest\": false,\n\t\t\"ReadableStreamDefaultController\": false,\n\t\t\"ReadableStreamDefaultReader\": false,\n\t\t\"Request\": false,\n\t\t\"require\": false,\n\t\t\"Response\": false,\n\t\t\"setImmediate\": false,\n\t\t\"setInterval\": false,\n\t\t\"setTimeout\": false,\n\t\t\"structuredClone\": false,\n\t\t\"SubtleCrypto\": false,\n\t\t\"SuppressedError\": false,\n\t\t\"TextDecoder\": false,\n\t\t\"TextDecoderStream\": false,\n\t\t\"TextEncoder\": false,\n\t\t\"TextEncoderStream\": false,\n\t\t\"TransformStream\": false,\n\t\t\"TransformStreamDefaultController\": false,\n\t\t\"URL\": false,\n\t\t\"URLPattern\": false,\n\t\t\"URLSearchParams\": false,\n\t\t\"WebAssembly\": false,\n\t\t\"WebSocket\": false,\n\t\t\"WritableStream\": false,\n\t\t\"WritableStreamDefaultController\": false,\n\t\t\"WritableStreamDefaultWriter\": false\n\t},\n\t\"nodeBuiltin\": {\n\t\t\"AbortController\": false,\n\t\t\"AbortSignal\": false,\n\t\t\"AsyncDisposableStack\": false,\n\t\t\"atob\": false,\n\t\t\"Blob\": false,\n\t\t\"BroadcastChannel\": false,\n\t\t\"btoa\": false,\n\t\t\"Buffer\": false,\n\t\t\"ByteLengthQueuingStrategy\": false,\n\t\t\"clearImmediate\": false,\n\t\t\"clearInterval\": false,\n\t\t\"clearTimeout\": false,\n\t\t\"CloseEvent\": false,\n\t\t\"CompressionStream\": false,\n\t\t\"console\": false,\n\t\t\"CountQueuingStrategy\": false,\n\t\t\"crypto\": false,\n\t\t\"Crypto\": false,\n\t\t\"CryptoKey\": false,\n\t\t\"CustomEvent\": false,\n\t\t\"DecompressionStream\": false,\n\t\t\"DisposableStack\": false,\n\t\t\"DOMException\": false,\n\t\t\"Event\": false,\n\t\t\"EventTarget\": false,\n\t\t\"fetch\": false,\n\t\t\"File\": false,\n\t\t\"FormData\": false,\n\t\t\"global\": false,\n\t\t\"Headers\": false,\n\t\t\"MessageChannel\": false,\n\t\t\"MessageEvent\": false,\n\t\t\"MessagePort\": false,\n\t\t\"navigator\": false,\n\t\t\"Navigator\": false,\n\t\t\"performance\": false,\n\t\t\"Performance\": false,\n\t\t\"PerformanceEntry\": false,\n\t\t\"PerformanceMark\": false,\n\t\t\"PerformanceMeasure\": false,\n\t\t\"PerformanceObserver\": false,\n\t\t\"PerformanceObserverEntryList\": false,\n\t\t\"PerformanceResourceTiming\": false,\n\t\t\"process\": false,\n\t\t\"queueMicrotask\": false,\n\t\t\"ReadableByteStreamController\": false,\n\t\t\"ReadableStream\": false,\n\t\t\"ReadableStreamBYOBReader\": false,\n\t\t\"ReadableStreamBYOBRequest\": false,\n\t\t\"ReadableStreamDefaultController\": false,\n\t\t\"ReadableStreamDefaultReader\": false,\n\t\t\"Request\": false,\n\t\t\"Response\": false,\n\t\t\"setImmediate\": false,\n\t\t\"setInterval\": false,\n\t\t\"setTimeout\": false,\n\t\t\"structuredClone\": false,\n\t\t\"SubtleCrypto\": false,\n\t\t\"SuppressedError\": false,\n\t\t\"TextDecoder\": false,\n\t\t\"TextDecoderStream\": false,\n\t\t\"TextEncoder\": false,\n\t\t\"TextEncoderStream\": false,\n\t\t\"TransformStream\": false,\n\t\t\"TransformStreamDefaultController\": false,\n\t\t\"URL\": false,\n\t\t\"URLPattern\": false,\n\t\t\"URLSearchParams\": false,\n\t\t\"WebAssembly\": false,\n\t\t\"WebSocket\": false,\n\t\t\"WritableStream\": false,\n\t\t\"WritableStreamDefaultController\": false,\n\t\t\"WritableStreamDefaultWriter\": false\n\t},\n\t\"phantomjs\": {\n\t\t\"console\": true,\n\t\t\"exports\": true,\n\t\t\"phantom\": true,\n\t\t\"require\": true,\n\t\t\"WebPage\": true\n\t},\n\t\"prototypejs\": {\n\t\t\"$\": false,\n\t\t\"$$\": false,\n\t\t\"$A\": false,\n\t\t\"$break\": false,\n\t\t\"$continue\": false,\n\t\t\"$F\": false,\n\t\t\"$H\": false,\n\t\t\"$R\": false,\n\t\t\"$w\": false,\n\t\t\"Abstract\": false,\n\t\t\"Ajax\": false,\n\t\t\"Autocompleter\": false,\n\t\t\"Builder\": false,\n\t\t\"Class\": false,\n\t\t\"Control\": false,\n\t\t\"Draggable\": false,\n\t\t\"Draggables\": false,\n\t\t\"Droppables\": false,\n\t\t\"Effect\": false,\n\t\t\"Element\": false,\n\t\t\"Enumerable\": false,\n\t\t\"Event\": false,\n\t\t\"Field\": false,\n\t\t\"Form\": false,\n\t\t\"Hash\": false,\n\t\t\"Insertion\": false,\n\t\t\"ObjectRange\": false,\n\t\t\"PeriodicalExecuter\": false,\n\t\t\"Position\": false,\n\t\t\"Prototype\": false,\n\t\t\"Scriptaculous\": false,\n\t\t\"Selector\": false,\n\t\t\"Sortable\": false,\n\t\t\"SortableObserver\": false,\n\t\t\"Sound\": false,\n\t\t\"Template\": false,\n\t\t\"Toggle\": false,\n\t\t\"Try\": false\n\t},\n\t\"protractor\": {\n\t\t\"$\": false,\n\t\t\"$$\": false,\n\t\t\"browser\": false,\n\t\t\"by\": false,\n\t\t\"By\": false,\n\t\t\"DartObject\": false,\n\t\t\"element\": false,\n\t\t\"protractor\": false\n\t},\n\t\"qunit\": {\n\t\t\"asyncTest\": false,\n\t\t\"deepEqual\": false,\n\t\t\"equal\": false,\n\t\t\"expect\": false,\n\t\t\"module\": false,\n\t\t\"notDeepEqual\": false,\n\t\t\"notEqual\": false,\n\t\t\"notOk\": false,\n\t\t\"notPropEqual\": false,\n\t\t\"notStrictEqual\": false,\n\t\t\"ok\": false,\n\t\t\"propEqual\": false,\n\t\t\"QUnit\": false,\n\t\t\"raises\": false,\n\t\t\"start\": false,\n\t\t\"stop\": false,\n\t\t\"strictEqual\": false,\n\t\t\"test\": false,\n\t\t\"throws\": false\n\t},\n\t\"rhino\": {\n\t\t\"defineClass\": false,\n\t\t\"deserialize\": false,\n\t\t\"gc\": false,\n\t\t\"help\": false,\n\t\t\"importClass\": false,\n\t\t\"importPackage\": false,\n\t\t\"java\": false,\n\t\t\"load\": false,\n\t\t\"loadClass\": false,\n\t\t\"Packages\": false,\n\t\t\"print\": false,\n\t\t\"quit\": false,\n\t\t\"readFile\": false,\n\t\t\"readUrl\": false,\n\t\t\"runCommand\": false,\n\t\t\"seal\": false,\n\t\t\"serialize\": false,\n\t\t\"spawn\": false,\n\t\t\"sync\": false,\n\t\t\"toint32\": false,\n\t\t\"version\": false\n\t},\n\t\"serviceworker\": {\n\t\t\"AbortController\": false,\n\t\t\"AbortPaymentEvent\": false,\n\t\t\"AbortSignal\": false,\n\t\t\"addEventListener\": false,\n\t\t\"ai\": false,\n\t\t\"AI\": false,\n\t\t\"AICreateMonitor\": false,\n\t\t\"AsyncDisposableStack\": false,\n\t\t\"atob\": false,\n\t\t\"BackgroundFetchEvent\": false,\n\t\t\"BackgroundFetchManager\": false,\n\t\t\"BackgroundFetchRecord\": false,\n\t\t\"BackgroundFetchRegistration\": false,\n\t\t\"BackgroundFetchUpdateUIEvent\": false,\n\t\t\"BarcodeDetector\": false,\n\t\t\"Blob\": false,\n\t\t\"BroadcastChannel\": false,\n\t\t\"btoa\": false,\n\t\t\"ByteLengthQueuingStrategy\": false,\n\t\t\"Cache\": false,\n\t\t\"caches\": false,\n\t\t\"CacheStorage\": false,\n\t\t\"CanMakePaymentEvent\": false,\n\t\t\"CanvasGradient\": false,\n\t\t\"CanvasPattern\": false,\n\t\t\"clearInterval\": false,\n\t\t\"clearTimeout\": false,\n\t\t\"Client\": false,\n\t\t\"clients\": false,\n\t\t\"Clients\": false,\n\t\t\"CloseEvent\": false,\n\t\t\"CompressionStream\": false,\n\t\t\"console\": false,\n\t\t\"cookieStore\": false,\n\t\t\"CookieStore\": false,\n\t\t\"CookieStoreManager\": false,\n\t\t\"CountQueuingStrategy\": false,\n\t\t\"createImageBitmap\": false,\n\t\t\"CropTarget\": false,\n\t\t\"crossOriginIsolated\": false,\n\t\t\"crypto\": false,\n\t\t\"Crypto\": false,\n\t\t\"CryptoKey\": false,\n\t\t\"CSSSkewX\": false,\n\t\t\"CSSSkewY\": false,\n\t\t\"CustomEvent\": false,\n\t\t\"DecompressionStream\": false,\n\t\t\"dispatchEvent\": false,\n\t\t\"DisposableStack\": false,\n\t\t\"DOMException\": false,\n\t\t\"DOMMatrix\": false,\n\t\t\"DOMMatrixReadOnly\": false,\n\t\t\"DOMPoint\": false,\n\t\t\"DOMPointReadOnly\": false,\n\t\t\"DOMQuad\": false,\n\t\t\"DOMRect\": false,\n\t\t\"DOMRectReadOnly\": false,\n\t\t\"DOMStringList\": false,\n\t\t\"ErrorEvent\": false,\n\t\t\"Event\": false,\n\t\t\"EventSource\": false,\n\t\t\"EventTarget\": false,\n\t\t\"ExtendableCookieChangeEvent\": false,\n\t\t\"ExtendableEvent\": false,\n\t\t\"ExtendableMessageEvent\": false,\n\t\t\"fetch\": false,\n\t\t\"FetchEvent\": false,\n\t\t\"File\": false,\n\t\t\"FileList\": false,\n\t\t\"FileReader\": false,\n\t\t\"FileSystemDirectoryHandle\": false,\n\t\t\"FileSystemFileHandle\": false,\n\t\t\"FileSystemHandle\": false,\n\t\t\"FileSystemWritableFileStream\": false,\n\t\t\"FontFace\": false,\n\t\t\"fonts\": false,\n\t\t\"FormData\": false,\n\t\t\"GPU\": false,\n\t\t\"GPUAdapter\": false,\n\t\t\"GPUAdapterInfo\": false,\n\t\t\"GPUBindGroup\": false,\n\t\t\"GPUBindGroupLayout\": false,\n\t\t\"GPUBuffer\": false,\n\t\t\"GPUBufferUsage\": false,\n\t\t\"GPUCanvasContext\": false,\n\t\t\"GPUColorWrite\": false,\n\t\t\"GPUCommandBuffer\": false,\n\t\t\"GPUCommandEncoder\": false,\n\t\t\"GPUCompilationInfo\": false,\n\t\t\"GPUCompilationMessage\": false,\n\t\t\"GPUComputePassEncoder\": false,\n\t\t\"GPUComputePipeline\": false,\n\t\t\"GPUDevice\": false,\n\t\t\"GPUDeviceLostInfo\": false,\n\t\t\"GPUError\": false,\n\t\t\"GPUExternalTexture\": false,\n\t\t\"GPUInternalError\": false,\n\t\t\"GPUMapMode\": false,\n\t\t\"GPUOutOfMemoryError\": false,\n\t\t\"GPUPipelineError\": false,\n\t\t\"GPUPipelineLayout\": false,\n\t\t\"GPUQuerySet\": false,\n\t\t\"GPUQueue\": false,\n\t\t\"GPURenderBundle\": false,\n\t\t\"GPURenderBundleEncoder\": false,\n\t\t\"GPURenderPassEncoder\": false,\n\t\t\"GPURenderPipeline\": false,\n\t\t\"GPUSampler\": false,\n\t\t\"GPUShaderModule\": false,\n\t\t\"GPUShaderStage\": false,\n\t\t\"GPUSupportedFeatures\": false,\n\t\t\"GPUSupportedLimits\": false,\n\t\t\"GPUTexture\": false,\n\t\t\"GPUTextureUsage\": false,\n\t\t\"GPUTextureView\": false,\n\t\t\"GPUUncapturedErrorEvent\": false,\n\t\t\"GPUValidationError\": false,\n\t\t\"Headers\": false,\n\t\t\"IDBCursor\": false,\n\t\t\"IDBCursorWithValue\": false,\n\t\t\"IDBDatabase\": false,\n\t\t\"IDBFactory\": false,\n\t\t\"IDBIndex\": false,\n\t\t\"IDBKeyRange\": false,\n\t\t\"IDBObjectStore\": false,\n\t\t\"IDBOpenDBRequest\": false,\n\t\t\"IDBRequest\": false,\n\t\t\"IDBTransaction\": false,\n\t\t\"IDBVersionChangeEvent\": false,\n\t\t\"ImageBitmap\": false,\n\t\t\"ImageBitmapRenderingContext\": false,\n\t\t\"ImageData\": false,\n\t\t\"importScripts\": false,\n\t\t\"indexedDB\": false,\n\t\t\"InstallEvent\": false,\n\t\t\"isSecureContext\": false,\n\t\t\"LanguageDetector\": false,\n\t\t\"location\": false,\n\t\t\"Lock\": false,\n\t\t\"LockManager\": false,\n\t\t\"MediaCapabilities\": false,\n\t\t\"MessageChannel\": false,\n\t\t\"MessageEvent\": false,\n\t\t\"MessagePort\": false,\n\t\t\"NavigationPreloadManager\": false,\n\t\t\"navigator\": false,\n\t\t\"NavigatorUAData\": false,\n\t\t\"NetworkInformation\": false,\n\t\t\"Notification\": false,\n\t\t\"NotificationEvent\": false,\n\t\t\"Observable\": false,\n\t\t\"OffscreenCanvas\": false,\n\t\t\"OffscreenCanvasRenderingContext2D\": false,\n\t\t\"onabortpayment\": true,\n\t\t\"onactivate\": true,\n\t\t\"onbackgroundfetchabort\": true,\n\t\t\"onbackgroundfetchclick\": true,\n\t\t\"onbackgroundfetchfail\": true,\n\t\t\"onbackgroundfetchsuccess\": true,\n\t\t\"oncanmakepayment\": true,\n\t\t\"oncookiechange\": true,\n\t\t\"onerror\": true,\n\t\t\"onfetch\": true,\n\t\t\"oninstall\": true,\n\t\t\"onlanguagechange\": true,\n\t\t\"onmessage\": true,\n\t\t\"onmessageerror\": true,\n\t\t\"onnotificationclick\": true,\n\t\t\"onnotificationclose\": true,\n\t\t\"onpaymentrequest\": true,\n\t\t\"onperiodicsync\": true,\n\t\t\"onpush\": true,\n\t\t\"onpushsubscriptionchange\": true,\n\t\t\"onrejectionhandled\": true,\n\t\t\"onsync\": true,\n\t\t\"onunhandledrejection\": true,\n\t\t\"origin\": false,\n\t\t\"Path2D\": false,\n\t\t\"PaymentRequestEvent\": false,\n\t\t\"performance\": false,\n\t\t\"Performance\": false,\n\t\t\"PerformanceEntry\": false,\n\t\t\"PerformanceMark\": false,\n\t\t\"PerformanceMeasure\": false,\n\t\t\"PerformanceObserver\": false,\n\t\t\"PerformanceObserverEntryList\": false,\n\t\t\"PerformanceResourceTiming\": false,\n\t\t\"PerformanceServerTiming\": false,\n\t\t\"PeriodicSyncEvent\": false,\n\t\t\"PeriodicSyncManager\": false,\n\t\t\"Permissions\": false,\n\t\t\"PermissionStatus\": false,\n\t\t\"PromiseRejectionEvent\": false,\n\t\t\"PushEvent\": false,\n\t\t\"PushManager\": false,\n\t\t\"PushMessageData\": false,\n\t\t\"PushSubscription\": false,\n\t\t\"PushSubscriptionOptions\": false,\n\t\t\"queueMicrotask\": false,\n\t\t\"ReadableByteStreamController\": false,\n\t\t\"ReadableStream\": false,\n\t\t\"ReadableStreamBYOBReader\": false,\n\t\t\"ReadableStreamBYOBRequest\": false,\n\t\t\"ReadableStreamDefaultController\": false,\n\t\t\"ReadableStreamDefaultReader\": false,\n\t\t\"registration\": false,\n\t\t\"removeEventListener\": false,\n\t\t\"ReportBody\": false,\n\t\t\"reportError\": false,\n\t\t\"ReportingObserver\": false,\n\t\t\"Request\": false,\n\t\t\"Response\": false,\n\t\t\"RestrictionTarget\": false,\n\t\t\"scheduler\": false,\n\t\t\"Scheduler\": false,\n\t\t\"SecurityPolicyViolationEvent\": false,\n\t\t\"self\": false,\n\t\t\"serviceWorker\": false,\n\t\t\"ServiceWorker\": false,\n\t\t\"ServiceWorkerGlobalScope\": false,\n\t\t\"ServiceWorkerRegistration\": false,\n\t\t\"setInterval\": false,\n\t\t\"setTimeout\": false,\n\t\t\"skipWaiting\": false,\n\t\t\"StorageBucket\": false,\n\t\t\"StorageBucketManager\": false,\n\t\t\"StorageManager\": false,\n\t\t\"structuredClone\": false,\n\t\t\"Subscriber\": false,\n\t\t\"SubtleCrypto\": false,\n\t\t\"SuppressedError\": false,\n\t\t\"SyncEvent\": false,\n\t\t\"SyncManager\": false,\n\t\t\"TaskController\": false,\n\t\t\"TaskPriorityChangeEvent\": false,\n\t\t\"TaskSignal\": false,\n\t\t\"TextDecoder\": false,\n\t\t\"TextDecoderStream\": false,\n\t\t\"TextEncoder\": false,\n\t\t\"TextEncoderStream\": false,\n\t\t\"TextMetrics\": false,\n\t\t\"TransformStream\": false,\n\t\t\"TransformStreamDefaultController\": false,\n\t\t\"TrustedHTML\": false,\n\t\t\"TrustedScript\": false,\n\t\t\"TrustedScriptURL\": false,\n\t\t\"TrustedTypePolicy\": false,\n\t\t\"TrustedTypePolicyFactory\": false,\n\t\t\"trustedTypes\": false,\n\t\t\"URL\": false,\n\t\t\"URLPattern\": false,\n\t\t\"URLSearchParams\": false,\n\t\t\"UserActivation\": false,\n\t\t\"WebAssembly\": false,\n\t\t\"WebGL2RenderingContext\": false,\n\t\t\"WebGLActiveInfo\": false,\n\t\t\"WebGLBuffer\": false,\n\t\t\"WebGLContextEvent\": false,\n\t\t\"WebGLFramebuffer\": false,\n\t\t\"WebGLObject\": false,\n\t\t\"WebGLProgram\": false,\n\t\t\"WebGLQuery\": false,\n\t\t\"WebGLRenderbuffer\": false,\n\t\t\"WebGLRenderingContext\": false,\n\t\t\"WebGLSampler\": false,\n\t\t\"WebGLShader\": false,\n\t\t\"WebGLShaderPrecisionFormat\": false,\n\t\t\"WebGLSync\": false,\n\t\t\"WebGLTexture\": false,\n\t\t\"WebGLTransformFeedback\": false,\n\t\t\"WebGLUniformLocation\": false,\n\t\t\"WebGLVertexArrayObject\": false,\n\t\t\"WebSocket\": false,\n\t\t\"WebSocketError\": false,\n\t\t\"WebSocketStream\": false,\n\t\t\"WebTransport\": false,\n\t\t\"WebTransportBidirectionalStream\": false,\n\t\t\"WebTransportDatagramDuplexStream\": false,\n\t\t\"WebTransportError\": false,\n\t\t\"WGSLLanguageFeatures\": false,\n\t\t\"when\": false,\n\t\t\"WindowClient\": false,\n\t\t\"WorkerGlobalScope\": false,\n\t\t\"WorkerLocation\": false,\n\t\t\"WorkerNavigator\": false,\n\t\t\"WritableStream\": false,\n\t\t\"WritableStreamDefaultController\": false,\n\t\t\"WritableStreamDefaultWriter\": false\n\t},\n\t\"shared-node-browser\": {\n\t\t\"AbortController\": false,\n\t\t\"AbortSignal\": false,\n\t\t\"AsyncDisposableStack\": false,\n\t\t\"atob\": false,\n\t\t\"Blob\": false,\n\t\t\"BroadcastChannel\": false,\n\t\t\"btoa\": false,\n\t\t\"ByteLengthQueuingStrategy\": false,\n\t\t\"clearInterval\": false,\n\t\t\"clearTimeout\": false,\n\t\t\"CloseEvent\": false,\n\t\t\"CompressionStream\": false,\n\t\t\"console\": false,\n\t\t\"CountQueuingStrategy\": false,\n\t\t\"crypto\": false,\n\t\t\"Crypto\": false,\n\t\t\"CryptoKey\": false,\n\t\t\"CustomEvent\": false,\n\t\t\"DecompressionStream\": false,\n\t\t\"DisposableStack\": false,\n\t\t\"DOMException\": false,\n\t\t\"Event\": false,\n\t\t\"EventTarget\": false,\n\t\t\"fetch\": false,\n\t\t\"File\": false,\n\t\t\"FormData\": false,\n\t\t\"Headers\": false,\n\t\t\"MessageChannel\": false,\n\t\t\"MessageEvent\": false,\n\t\t\"MessagePort\": false,\n\t\t\"navigator\": false,\n\t\t\"Navigator\": false,\n\t\t\"performance\": false,\n\t\t\"Performance\": false,\n\t\t\"PerformanceEntry\": false,\n\t\t\"PerformanceMark\": false,\n\t\t\"PerformanceMeasure\": false,\n\t\t\"PerformanceObserver\": false,\n\t\t\"PerformanceObserverEntryList\": false,\n\t\t\"PerformanceResourceTiming\": false,\n\t\t\"queueMicrotask\": false,\n\t\t\"ReadableByteStreamController\": false,\n\t\t\"ReadableStream\": false,\n\t\t\"ReadableStreamBYOBReader\": false,\n\t\t\"ReadableStreamBYOBRequest\": false,\n\t\t\"ReadableStreamDefaultController\": false,\n\t\t\"ReadableStreamDefaultReader\": false,\n\t\t\"Request\": false,\n\t\t\"Response\": false,\n\t\t\"setInterval\": false,\n\t\t\"setTimeout\": false,\n\t\t\"structuredClone\": false,\n\t\t\"SubtleCrypto\": false,\n\t\t\"SuppressedError\": false,\n\t\t\"TextDecoder\": false,\n\t\t\"TextDecoderStream\": false,\n\t\t\"TextEncoder\": false,\n\t\t\"TextEncoderStream\": false,\n\t\t\"TransformStream\": false,\n\t\t\"TransformStreamDefaultController\": false,\n\t\t\"URL\": false,\n\t\t\"URLPattern\": false,\n\t\t\"URLSearchParams\": false,\n\t\t\"WebAssembly\": false,\n\t\t\"WebSocket\": false,\n\t\t\"WritableStream\": false,\n\t\t\"WritableStreamDefaultController\": false,\n\t\t\"WritableStreamDefaultWriter\": false\n\t},\n\t\"shelljs\": {\n\t\t\"cat\": false,\n\t\t\"cd\": false,\n\t\t\"chmod\": false,\n\t\t\"cmd\": false,\n\t\t\"config\": false,\n\t\t\"cp\": false,\n\t\t\"dirs\": false,\n\t\t\"echo\": false,\n\t\t\"env\": false,\n\t\t\"error\": false,\n\t\t\"errorCode\": false,\n\t\t\"exec\": false,\n\t\t\"exit\": false,\n\t\t\"find\": false,\n\t\t\"grep\": false,\n\t\t\"head\": false,\n\t\t\"ln\": false,\n\t\t\"ls\": false,\n\t\t\"mkdir\": false,\n\t\t\"mv\": false,\n\t\t\"popd\": false,\n\t\t\"pushd\": false,\n\t\t\"pwd\": false,\n\t\t\"rm\": false,\n\t\t\"sed\": false,\n\t\t\"set\": false,\n\t\t\"ShellString\": false,\n\t\t\"sort\": false,\n\t\t\"tail\": false,\n\t\t\"tempdir\": false,\n\t\t\"test\": false,\n\t\t\"touch\": false,\n\t\t\"uniq\": false,\n\t\t\"which\": false\n\t},\n\t\"vitest\": {\n\t\t\"afterAll\": false,\n\t\t\"afterEach\": false,\n\t\t\"assert\": false,\n\t\t\"assertType\": false,\n\t\t\"beforeAll\": false,\n\t\t\"beforeEach\": false,\n\t\t\"chai\": false,\n\t\t\"describe\": false,\n\t\t\"expect\": false,\n\t\t\"expectTypeOf\": false,\n\t\t\"it\": false,\n\t\t\"onTestFailed\": false,\n\t\t\"onTestFinished\": false,\n\t\t\"suite\": false,\n\t\t\"test\": false,\n\t\t\"vi\": false,\n\t\t\"vitest\": false\n\t},\n\t\"webextensions\": {\n\t\t\"browser\": false,\n\t\t\"chrome\": false,\n\t\t\"opr\": false\n\t},\n\t\"worker\": {\n\t\t\"AbortController\": false,\n\t\t\"AbortSignal\": false,\n\t\t\"addEventListener\": false,\n\t\t\"ai\": false,\n\t\t\"AI\": false,\n\t\t\"AICreateMonitor\": false,\n\t\t\"AsyncDisposableStack\": false,\n\t\t\"atob\": false,\n\t\t\"AudioData\": false,\n\t\t\"AudioDecoder\": false,\n\t\t\"AudioEncoder\": false,\n\t\t\"BackgroundFetchManager\": false,\n\t\t\"BackgroundFetchRecord\": false,\n\t\t\"BackgroundFetchRegistration\": false,\n\t\t\"BarcodeDetector\": false,\n\t\t\"Blob\": false,\n\t\t\"BroadcastChannel\": false,\n\t\t\"btoa\": false,\n\t\t\"ByteLengthQueuingStrategy\": false,\n\t\t\"Cache\": false,\n\t\t\"caches\": false,\n\t\t\"CacheStorage\": false,\n\t\t\"cancelAnimationFrame\": false,\n\t\t\"CanvasGradient\": false,\n\t\t\"CanvasPattern\": false,\n\t\t\"clearInterval\": false,\n\t\t\"clearTimeout\": false,\n\t\t\"close\": false,\n\t\t\"CloseEvent\": false,\n\t\t\"CompressionStream\": false,\n\t\t\"console\": false,\n\t\t\"CountQueuingStrategy\": false,\n\t\t\"createImageBitmap\": false,\n\t\t\"CropTarget\": false,\n\t\t\"crossOriginIsolated\": false,\n\t\t\"crypto\": false,\n\t\t\"Crypto\": false,\n\t\t\"CryptoKey\": false,\n\t\t\"CSSSkewX\": false,\n\t\t\"CSSSkewY\": false,\n\t\t\"CustomEvent\": false,\n\t\t\"DecompressionStream\": false,\n\t\t\"DedicatedWorkerGlobalScope\": false,\n\t\t\"dispatchEvent\": false,\n\t\t\"DisposableStack\": false,\n\t\t\"DOMException\": false,\n\t\t\"DOMMatrix\": false,\n\t\t\"DOMMatrixReadOnly\": false,\n\t\t\"DOMPoint\": false,\n\t\t\"DOMPointReadOnly\": false,\n\t\t\"DOMQuad\": false,\n\t\t\"DOMRect\": false,\n\t\t\"DOMRectReadOnly\": false,\n\t\t\"DOMStringList\": false,\n\t\t\"EncodedAudioChunk\": false,\n\t\t\"EncodedVideoChunk\": false,\n\t\t\"ErrorEvent\": false,\n\t\t\"Event\": false,\n\t\t\"EventSource\": false,\n\t\t\"EventTarget\": false,\n\t\t\"fetch\": false,\n\t\t\"File\": false,\n\t\t\"FileList\": false,\n\t\t\"FileReader\": false,\n\t\t\"FileReaderSync\": false,\n\t\t\"FileSystemDirectoryHandle\": false,\n\t\t\"FileSystemFileHandle\": false,\n\t\t\"FileSystemHandle\": false,\n\t\t\"FileSystemObserver\": false,\n\t\t\"FileSystemSyncAccessHandle\": false,\n\t\t\"FileSystemWritableFileStream\": false,\n\t\t\"FontFace\": false,\n\t\t\"fonts\": false,\n\t\t\"FormData\": false,\n\t\t\"GPU\": false,\n\t\t\"GPUAdapter\": false,\n\t\t\"GPUAdapterInfo\": false,\n\t\t\"GPUBindGroup\": false,\n\t\t\"GPUBindGroupLayout\": false,\n\t\t\"GPUBuffer\": false,\n\t\t\"GPUBufferUsage\": false,\n\t\t\"GPUCanvasContext\": false,\n\t\t\"GPUColorWrite\": false,\n\t\t\"GPUCommandBuffer\": false,\n\t\t\"GPUCommandEncoder\": false,\n\t\t\"GPUCompilationInfo\": false,\n\t\t\"GPUCompilationMessage\": false,\n\t\t\"GPUComputePassEncoder\": false,\n\t\t\"GPUComputePipeline\": false,\n\t\t\"GPUDevice\": false,\n\t\t\"GPUDeviceLostInfo\": false,\n\t\t\"GPUError\": false,\n\t\t\"GPUExternalTexture\": false,\n\t\t\"GPUInternalError\": false,\n\t\t\"GPUMapMode\": false,\n\t\t\"GPUOutOfMemoryError\": false,\n\t\t\"GPUPipelineError\": false,\n\t\t\"GPUPipelineLayout\": false,\n\t\t\"GPUQuerySet\": false,\n\t\t\"GPUQueue\": false,\n\t\t\"GPURenderBundle\": false,\n\t\t\"GPURenderBundleEncoder\": false,\n\t\t\"GPURenderPassEncoder\": false,\n\t\t\"GPURenderPipeline\": false,\n\t\t\"GPUSampler\": false,\n\t\t\"GPUShaderModule\": false,\n\t\t\"GPUShaderStage\": false,\n\t\t\"GPUSupportedFeatures\": false,\n\t\t\"GPUSupportedLimits\": false,\n\t\t\"GPUTexture\": false,\n\t\t\"GPUTextureUsage\": false,\n\t\t\"GPUTextureView\": false,\n\t\t\"GPUUncapturedErrorEvent\": false,\n\t\t\"GPUValidationError\": false,\n\t\t\"Headers\": false,\n\t\t\"HID\": false,\n\t\t\"HIDConnectionEvent\": false,\n\t\t\"HIDDevice\": false,\n\t\t\"HIDInputReportEvent\": false,\n\t\t\"IDBCursor\": false,\n\t\t\"IDBCursorWithValue\": false,\n\t\t\"IDBDatabase\": false,\n\t\t\"IDBFactory\": false,\n\t\t\"IDBIndex\": false,\n\t\t\"IDBKeyRange\": false,\n\t\t\"IDBObjectStore\": false,\n\t\t\"IDBOpenDBRequest\": false,\n\t\t\"IDBRequest\": false,\n\t\t\"IDBTransaction\": false,\n\t\t\"IDBVersionChangeEvent\": false,\n\t\t\"IdleDetector\": false,\n\t\t\"ImageBitmap\": false,\n\t\t\"ImageBitmapRenderingContext\": false,\n\t\t\"ImageData\": false,\n\t\t\"ImageDecoder\": false,\n\t\t\"ImageTrack\": false,\n\t\t\"ImageTrackList\": false,\n\t\t\"importScripts\": false,\n\t\t\"indexedDB\": false,\n\t\t\"isSecureContext\": false,\n\t\t\"LanguageDetector\": false,\n\t\t\"location\": false,\n\t\t\"Lock\": false,\n\t\t\"LockManager\": false,\n\t\t\"MediaCapabilities\": false,\n\t\t\"MediaSource\": false,\n\t\t\"MediaSourceHandle\": false,\n\t\t\"MessageChannel\": false,\n\t\t\"MessageEvent\": false,\n\t\t\"MessagePort\": false,\n\t\t\"name\": false,\n\t\t\"NavigationPreloadManager\": false,\n\t\t\"navigator\": false,\n\t\t\"NavigatorUAData\": false,\n\t\t\"NetworkInformation\": false,\n\t\t\"Notification\": false,\n\t\t\"Observable\": false,\n\t\t\"OffscreenCanvas\": false,\n\t\t\"OffscreenCanvasRenderingContext2D\": false,\n\t\t\"onerror\": true,\n\t\t\"onlanguagechange\": true,\n\t\t\"onmessage\": true,\n\t\t\"onmessageerror\": true,\n\t\t\"onrejectionhandled\": true,\n\t\t\"onunhandledrejection\": true,\n\t\t\"origin\": false,\n\t\t\"Path2D\": false,\n\t\t\"performance\": false,\n\t\t\"Performance\": false,\n\t\t\"PerformanceEntry\": false,\n\t\t\"PerformanceMark\": false,\n\t\t\"PerformanceMeasure\": false,\n\t\t\"PerformanceObserver\": false,\n\t\t\"PerformanceObserverEntryList\": false,\n\t\t\"PerformanceResourceTiming\": false,\n\t\t\"PerformanceServerTiming\": false,\n\t\t\"PeriodicSyncManager\": false,\n\t\t\"Permissions\": false,\n\t\t\"PermissionStatus\": false,\n\t\t\"PERSISTENT\": false,\n\t\t\"postMessage\": false,\n\t\t\"PressureObserver\": false,\n\t\t\"PressureRecord\": false,\n\t\t\"ProgressEvent\": false,\n\t\t\"PromiseRejectionEvent\": false,\n\t\t\"PushManager\": false,\n\t\t\"PushSubscription\": false,\n\t\t\"PushSubscriptionOptions\": false,\n\t\t\"queueMicrotask\": false,\n\t\t\"ReadableByteStreamController\": false,\n\t\t\"ReadableStream\": false,\n\t\t\"ReadableStreamBYOBReader\": false,\n\t\t\"ReadableStreamBYOBRequest\": false,\n\t\t\"ReadableStreamDefaultController\": false,\n\t\t\"ReadableStreamDefaultReader\": false,\n\t\t\"removeEventListener\": false,\n\t\t\"ReportBody\": false,\n\t\t\"reportError\": false,\n\t\t\"ReportingObserver\": false,\n\t\t\"Request\": false,\n\t\t\"requestAnimationFrame\": false,\n\t\t\"Response\": false,\n\t\t\"RestrictionTarget\": false,\n\t\t\"RTCDataChannel\": false,\n\t\t\"RTCEncodedAudioFrame\": false,\n\t\t\"RTCEncodedVideoFrame\": false,\n\t\t\"scheduler\": false,\n\t\t\"Scheduler\": false,\n\t\t\"SecurityPolicyViolationEvent\": false,\n\t\t\"self\": false,\n\t\t\"Serial\": false,\n\t\t\"SerialPort\": false,\n\t\t\"ServiceWorkerRegistration\": false,\n\t\t\"setInterval\": false,\n\t\t\"setTimeout\": false,\n\t\t\"SourceBuffer\": false,\n\t\t\"SourceBufferList\": false,\n\t\t\"StorageBucket\": false,\n\t\t\"StorageBucketManager\": false,\n\t\t\"StorageManager\": false,\n\t\t\"structuredClone\": false,\n\t\t\"Subscriber\": false,\n\t\t\"SubtleCrypto\": false,\n\t\t\"SuppressedError\": false,\n\t\t\"SyncManager\": false,\n\t\t\"TaskController\": false,\n\t\t\"TaskPriorityChangeEvent\": false,\n\t\t\"TaskSignal\": false,\n\t\t\"TEMPORARY\": false,\n\t\t\"TextDecoder\": false,\n\t\t\"TextDecoderStream\": false,\n\t\t\"TextEncoder\": false,\n\t\t\"TextEncoderStream\": false,\n\t\t\"TextMetrics\": false,\n\t\t\"TransformStream\": false,\n\t\t\"TransformStreamDefaultController\": false,\n\t\t\"TrustedHTML\": false,\n\t\t\"TrustedScript\": false,\n\t\t\"TrustedScriptURL\": false,\n\t\t\"TrustedTypePolicy\": false,\n\t\t\"TrustedTypePolicyFactory\": false,\n\t\t\"trustedTypes\": false,\n\t\t\"URL\": false,\n\t\t\"URLPattern\": false,\n\t\t\"URLSearchParams\": false,\n\t\t\"USB\": false,\n\t\t\"USBAlternateInterface\": false,\n\t\t\"USBConfiguration\": false,\n\t\t\"USBConnectionEvent\": false,\n\t\t\"USBDevice\": false,\n\t\t\"USBEndpoint\": false,\n\t\t\"USBInterface\": false,\n\t\t\"USBInTransferResult\": false,\n\t\t\"USBIsochronousInTransferPacket\": false,\n\t\t\"USBIsochronousInTransferResult\": false,\n\t\t\"USBIsochronousOutTransferPacket\": false,\n\t\t\"USBIsochronousOutTransferResult\": false,\n\t\t\"USBOutTransferResult\": false,\n\t\t\"UserActivation\": false,\n\t\t\"VideoColorSpace\": false,\n\t\t\"VideoDecoder\": false,\n\t\t\"VideoEncoder\": false,\n\t\t\"VideoFrame\": false,\n\t\t\"WebAssembly\": false,\n\t\t\"WebGL2RenderingContext\": false,\n\t\t\"WebGLActiveInfo\": false,\n\t\t\"WebGLBuffer\": false,\n\t\t\"WebGLContextEvent\": false,\n\t\t\"WebGLFramebuffer\": false,\n\t\t\"WebGLObject\": false,\n\t\t\"WebGLProgram\": false,\n\t\t\"WebGLQuery\": false,\n\t\t\"WebGLRenderbuffer\": false,\n\t\t\"WebGLRenderingContext\": false,\n\t\t\"WebGLSampler\": false,\n\t\t\"WebGLShader\": false,\n\t\t\"WebGLShaderPrecisionFormat\": false,\n\t\t\"WebGLSync\": false,\n\t\t\"WebGLTexture\": false,\n\t\t\"WebGLTransformFeedback\": false,\n\t\t\"WebGLUniformLocation\": false,\n\t\t\"WebGLVertexArrayObject\": false,\n\t\t\"webkitRequestFileSystem\": false,\n\t\t\"webkitRequestFileSystemSync\": false,\n\t\t\"webkitResolveLocalFileSystemSyncURL\": false,\n\t\t\"webkitResolveLocalFileSystemURL\": false,\n\t\t\"WebSocket\": false,\n\t\t\"WebSocketError\": false,\n\t\t\"WebSocketStream\": false,\n\t\t\"WebTransport\": false,\n\t\t\"WebTransportBidirectionalStream\": false,\n\t\t\"WebTransportDatagramDuplexStream\": false,\n\t\t\"WebTransportError\": false,\n\t\t\"WGSLLanguageFeatures\": false,\n\t\t\"when\": false,\n\t\t\"Worker\": false,\n\t\t\"WorkerGlobalScope\": false,\n\t\t\"WorkerLocation\": false,\n\t\t\"WorkerNavigator\": false,\n\t\t\"WritableStream\": false,\n\t\t\"WritableStreamDefaultController\": false,\n\t\t\"WritableStreamDefaultWriter\": false,\n\t\t\"XMLHttpRequest\": false,\n\t\t\"XMLHttpRequestEventTarget\": false,\n\t\t\"XMLHttpRequestUpload\": false\n\t},\n\t\"wsh\": {\n\t\t\"ActiveXObject\": false,\n\t\t\"CollectGarbage\": false,\n\t\t\"Debug\": false,\n\t\t\"Enumerator\": false,\n\t\t\"GetObject\": false,\n\t\t\"RuntimeObject\": false,\n\t\t\"ScriptEngine\": false,\n\t\t\"ScriptEngineBuildVersion\": false,\n\t\t\"ScriptEngineMajorVersion\": false,\n\t\t\"ScriptEngineMinorVersion\": false,\n\t\t\"VBArray\": false,\n\t\t\"WScript\": false,\n\t\t\"WSH\": false\n\t},\n\t\"yui\": {\n\t\t\"YAHOO\": false,\n\t\t\"YAHOO_config\": false,\n\t\t\"YUI\": false,\n\t\t\"YUI_config\": false\n\t}\n}\n", "'use strict';\nmodule.exports = require('./globals.json');\n", "import { name as ruleName, rule } from \"./rule.js\";\nimport globals from \"globals\";\n\nconst pluginName = \"react-you-might-not-need-an-effect\";\n\nconst plugin = {\n  meta: {\n    name: pluginName,\n  },\n  configs: {},\n  rules: {\n    [ruleName]: rule,\n  },\n};\n\nObject.assign(plugin.configs, {\n  // flat config format\n  recommended: {\n    files: [\"**/*.{js,jsx,mjs,cjs,ts,tsx,mts,cts}\"],\n    plugins: {\n      // Object.assign above so we can reference `plugin` here\n      [pluginName]: plugin,\n    },\n    rules: {\n      [pluginName + \"/\" + ruleName]: \"warn\",\n    },\n    languageOptions: {\n      globals: {\n        // NOTE: Required so we can resolve global references to their upstream global variables\n        ...globals.browser,\n      },\n      parserOptions: {\n        ecmaFeatures: {\n          jsx: true,\n        },\n      },\n    },\n  },\n  // eslintrc format\n  \"legacy-recommended\": {\n    plugins: [pluginName],\n    rules: {\n      [pluginName + \"/\" + ruleName]: \"warn\",\n    },\n    globals: {\n      // NOTE: Required so we can resolve global references to their upstream global variables\n      ...globals.browser,\n    },\n    parserOptions: {\n      ecmaFeatures: {\n        jsx: true,\n      },\n    },\n  },\n});\n\nexport default plugin;\n", "// `build.js` will bundle everything into CJS.\n// Would be nice to have it use `index.js` directly, but then `esbuild`\n// seems unable to structure the CJS export the way ESLint expects.\n// Seems we have to unwrap the default export ourselves for that.\nmodule.exports = require(\"./index.js\").default;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAa,YAgBA;AAhBb;AAAA;AAAO,IAAM,aAAa;AAAA,MACxB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,wBAAwB;AAAA,MACxB,oBAAoB;AAAA,MACpB,0BAA0B;AAAA,MAC1B,8BAA8B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOhC;AAGO,IAAM,WAAW;AAAA,MACtB,CAAC,WAAW,gBAAgB,GAAG;AAAA,MAC/B,CAAC,WAAW,iBAAiB,GAC3B;AAAA,MACF,CAAC,WAAW,sBAAsB,GAChC;AAAA,MACF,CAAC,WAAW,kBAAkB,GAC5B;AAAA,MACF,CAAC,WAAW,wBAAwB,GAClC;AAAA,MACF,CAAC,WAAW,4BAA4B,GACtC;AAAA;AAAA;AAAA,IAGJ;AAAA;AAAA;;;AC9BA;AAAA,qFAAAA,UAAAC,SAAA;AAAA,IAAAA,QAAA;AAAA,MACI,sBAAwB;AAAA,QACpB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,mBAAqB;AAAA,QACjB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,iBAAmB;AAAA,QACf;AAAA,MACJ;AAAA,MACA,cAAgB;AAAA,QACZ;AAAA,MACJ;AAAA,MACA,yBAA2B;AAAA,QACvB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,iBAAmB;AAAA,QACf;AAAA,MACJ;AAAA,MACA,gBAAkB;AAAA,QACd;AAAA,MACJ;AAAA,MACA,kBAAoB;AAAA,QAChB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,gBAAkB;AAAA,QACd;AAAA,MACJ;AAAA,MACA,gBAAkB;AAAA,QACd;AAAA,QACA;AAAA,MACJ;AAAA,MACA,aAAe;AAAA,QACX;AAAA,QACA;AAAA,MACJ;AAAA,MACA,iBAAmB;AAAA,QACf;AAAA,MACJ;AAAA,MACA,WAAa;AAAA,QACT;AAAA,MACJ;AAAA,MACA,kBAAoB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,iBAAmB;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,uBAAyB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,mBAAqB;AAAA,QACjB;AAAA,MACJ;AAAA,MACA,mBAAqB,CAAC;AAAA,MACtB,kBAAoB;AAAA,QAChB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,gBAAkB,CAAC;AAAA,MACnB,sBAAwB;AAAA,QACpB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,0BAA4B;AAAA,QACxB;AAAA,MACJ;AAAA,MACA,wBAA0B;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,iBAAmB;AAAA,QACf;AAAA,QACA;AAAA,MACJ;AAAA,MACA,qBAAuB;AAAA,QACnB;AAAA,MACJ;AAAA,MACA,0BAA4B;AAAA,QACxB;AAAA,MACJ;AAAA,MACA,4BAA8B;AAAA,QAC1B;AAAA,MACJ;AAAA,MACA,cAAgB;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,gBAAkB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,gBAAkB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,qBAAuB;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,oBAAsB;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,YAAc,CAAC;AAAA,MACf,aAAe;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,mBAAqB;AAAA,QACjB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,wBAA0B;AAAA,QACtB;AAAA,MACJ;AAAA,MACA,kBAAoB;AAAA,QAChB;AAAA,MACJ;AAAA,MACA,0BAA4B;AAAA,QACxB;AAAA,MACJ;AAAA,MACA,iBAAmB;AAAA,QACf;AAAA,QACA;AAAA,MACJ;AAAA,MACA,cAAgB;AAAA,QACZ;AAAA,QACA;AAAA,MACJ;AAAA,MACA,mBAAqB;AAAA,QACjB;AAAA,MACJ;AAAA,MACA,YAAc;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,oBAAsB,CAAC;AAAA,MACvB,wBAA0B;AAAA,QACtB;AAAA,MACJ;AAAA,MACA,eAAiB,CAAC;AAAA,MAClB,qBAAuB;AAAA,QACnB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,mBAAqB;AAAA,QACjB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,mBAAqB;AAAA,QACjB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,oBAAsB;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,SAAW,CAAC;AAAA,MACZ,aAAe;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAW,CAAC;AAAA,MACZ,kBAAoB;AAAA,QAChB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,mBAAqB;AAAA,QACjB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,kBAAoB;AAAA,QAChB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,cAAgB;AAAA,QACZ;AAAA,QACA;AAAA,MACJ;AAAA,MACA,kBAAoB;AAAA,QAChB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,eAAiB;AAAA,QACb;AAAA,QACA;AAAA,MACJ;AAAA,MACA,kBAAoB;AAAA,QAChB;AAAA,MACJ;AAAA,MACA,eAAiB;AAAA,QACb;AAAA,MACJ;AAAA,MACA,mBAAqB,CAAC;AAAA,MACtB,SAAW;AAAA,QACP;AAAA,MACJ;AAAA,MACA,UAAY;AAAA,QACR;AAAA,QACA;AAAA,MACJ;AAAA,MACA,oBAAsB;AAAA,QAClB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,aAAe;AAAA,QACX;AAAA,MACJ;AAAA,MACA,iBAAmB;AAAA,QACf;AAAA,MACJ;AAAA,MACA,oBAAsB;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,eAAiB;AAAA,QACb;AAAA,MACJ;AAAA,MACA,OAAS,CAAC;AAAA,MACV,iBAAmB;AAAA,QACf;AAAA,QACA;AAAA,MACJ;AAAA,MACA,YAAc;AAAA,QACV;AAAA,QACA;AAAA,MACJ;AAAA,MACA,0BAA4B;AAAA,QACxB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,iBAAmB,CAAC;AAAA,MACpB,iBAAmB;AAAA,QACf;AAAA,QACA;AAAA,MACJ;AAAA,MACA,gBAAkB,CAAC;AAAA,MACnB,gBAAkB;AAAA,QACd;AAAA,MACJ;AAAA,MACA,cAAgB;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,iBAAmB;AAAA,QACf;AAAA,MACJ;AAAA,MACA,kBAAoB;AAAA,QAChB;AAAA,MACJ;AAAA,MACA,qBAAuB;AAAA,QACnB;AAAA,MACJ;AAAA,MACA,oBAAsB;AAAA,QAClB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,gBAAkB;AAAA,QACd;AAAA,QACA;AAAA,MACJ;AAAA,MACA,eAAiB;AAAA,QACb;AAAA,QACA;AAAA,MACJ;AAAA,MACA,iBAAmB;AAAA,QACf;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;AChSA;AAAA,4EAAAC,UAAAC,SAAA;AAAA;AAMA,QAAM,OAAO;AAGb,QAAM,aAAa,OAAO,OAAO,OAAO,KAAK,IAAI,CAAC;AAGlD,eAAW,QAAQ,YAAY;AAC3B,aAAO,OAAO,KAAK,IAAI,CAAC;AAAA,IAC5B;AACA,WAAO,OAAO,IAAI;AAGlB,QAAM,gBAAgB,oBAAI,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAOD,aAAS,UAAU,KAAK;AACpB,aAAO,CAAC,cAAc,IAAI,GAAG,KAAK,IAAI,CAAC,MAAM;AAAA,IACjD;AAMA,IAAAA,QAAO,UAAU,OAAO,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,MAM3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,QAAQ,MAAM;AACV,eAAO,OAAO,KAAK,IAAI,EAAE,OAAO,SAAS;AAAA,MAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,UAAU,gBAAgB;AACtB,cAAM,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI;AAEnC,mBAAW,QAAQ,OAAO,KAAK,cAAc,GAAG;AAC5C,cAAI,KAAK,eAAe,IAAI,GAAG;AAC3B,kBAAM,OAAO,IAAI,IAAI,eAAe,IAAI,CAAC;AAEzC,uBAAW,OAAO,KAAK,IAAI,GAAG;AAC1B,mBAAK,IAAI,GAAG;AAAA,YAChB;AAEA,iBAAK,IAAI,IAAI,OAAO,OAAO,MAAM,KAAK,IAAI,CAAC;AAAA,UAC/C,OAAO;AACH,iBAAK,IAAI,IAAI,OAAO,OAAO,MAAM,KAAK,eAAe,IAAI,CAAC,CAAC;AAAA,UAC/D;AAAA,QACJ;AAEA,eAAO,OAAO,OAAO,IAAI;AAAA,MAC7B;AAAA,IACJ,CAAC;AAAA;AAAA;;;AC1EM,SAAS,kBAAkB,cAAc,MAAM;AAClD,QAAM,WAAW,KAAK,MAAM,CAAC;AAE7B,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,KAAG;AACC,YAAQ;AACR,eAAW,cAAc,MAAM,aAAa;AACxC,YAAM,QAAQ,WAAW,MAAM;AAE/B,UAAI,MAAM,CAAC,KAAK,YAAY,WAAW,MAAM,CAAC,GAAG;AAC7C,gBAAQ;AACR,gBAAQ;AACR;MAChB;IACA;EACA,SAAa;AAET,SAAO;AACX;ACjBO,SAAS,aAAa,cAAc,YAAY;AACnD,MAAIC,QAAO;AACX,MAAI,QAAQ;AAEZ,MAAI,OAAO,eAAe,UAAU;AAChC,IAAAA,QAAO;EACf,OAAW;AACH,IAAAA,QAAO,WAAW;AAClB,YAAQ,kBAAkB,OAAO,UAAU;EACnD;AAEI,SAAO,SAAS,MAAM;AAClB,UAAM,WAAW,MAAM,IAAI,IAAIA,KAAI;AACnC,QAAI,YAAY,MAAM;AAClB,aAAO;IACnB;AACQ,YAAQ,MAAM;EACtB;AAEI,SAAO;AACX;ACvBA,SAAS,QAAQ,OAAO;AACpB,SAAO,CAAC,KAAK,KAAK;AACtB;AAOA,SAAS,OAAO,GAAG;AACf,SAAO,QAAQ,KAAK,CAAC;AACzB;AAQA,SAAS,2BAA2B,OAAO,OAAO;AAC9C,SAAO,MAAM,SAAS,gBAAgB,MAAM,UAAU;AAC1D;AAOO,SAAS,aAAa,OAAO;AAChC,SAAO,2BAA2B,OAAO,IAAI;AACjD;AAOO,SAAS,aAAa,OAAO;AAChC,SAAO,2BAA2B,OAAO,GAAG;AAChD;AAOO,SAAS,iBAAiB,OAAO;AACpC,SAAO,2BAA2B,OAAO,GAAG;AAChD;AAOO,SAAS,aAAa,OAAO;AAChC,SAAO,2BAA2B,OAAO,GAAG;AAChD;AAOO,SAAS,oBAAoB,OAAO;AACvC,SAAO,2BAA2B,OAAO,GAAG;AAChD;AAOO,SAAS,oBAAoB,OAAO;AACvC,SAAO,2BAA2B,OAAO,GAAG;AAChD;AAOO,SAAS,sBAAsB,OAAO;AACzC,SAAO,2BAA2B,OAAO,GAAG;AAChD;AAOO,SAAS,sBAAsB,OAAO;AACzC,SAAO,2BAA2B,OAAO,GAAG;AAChD;AAOO,SAAS,oBAAoB,OAAO;AACvC,SAAO,2BAA2B,OAAO,GAAG;AAChD;AAOO,SAAS,oBAAoB,OAAO;AACvC,SAAO,2BAA2B,OAAO,GAAG;AAChD;AAOO,SAAS,eAAe,OAAO;AAClC,SAAO,CAAC,SAAS,QAAQ,SAAS,EAAE,SAAS,MAAM,IAAI;AAC3D;AENA,SAAS,sBAAsB,QAAQA,OAAM;AACzC,MAAI,IAAI;AACR,UAAQ,OAAO,MAAM,YAAY,OAAO,MAAM,eAAe,MAAM,MAAM;AACrE,UAAM,IAAI,OAAO,yBAAyB,GAAGA,KAAI;AACjD,QAAI,GAAG;AACH,aAAO;IACnB;AACQ,QAAI,OAAO,eAAe,CAAC;EACnC;AACI,SAAO;AACX;AAOA,SAAS,SAAS,QAAQA,OAAM;AAC5B,QAAM,IAAI,sBAAsB,QAAQA,KAAI;AAC5C,SAAO,KAAK,QAAQ,EAAE,OAAO;AACjC;AAQA,SAAS,iBAAiB,UAAU,cAAc;AAC9C,QAAM,YAAY,CAAA;AAElB,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACtC,UAAM,cAAc,SAAS,CAAC;AAE9B,QAAI,eAAe,MAAM;AACrB,gBAAU,SAAS,IAAI;IACnC,WAAmB,YAAY,SAAS,iBAAiB;AAC7C,YAAM,WAAW,gBAAgB,YAAY,UAAU,YAAY;AACnE,UAAI,YAAY,MAAM;AAClB,eAAO;MACvB;AACY,gBAAU,KAAK,GAAG,SAAS,KAAK;IAC5C,OAAe;AACH,YAAM,UAAU,gBAAgB,aAAa,YAAY;AACzD,UAAI,WAAW,MAAM;AACjB,eAAO;MACvB;AACY,gBAAU,KAAK,QAAQ,KAAK;IACxC;EACA;AAEI,SAAO;AACX;AAgWA,SAAS,gBAAgB,MAAM,cAAc;AACzC,MAAI,QAAQ,QAAQ,OAAO,eAAe,KAAK,YAAY,KAAK,IAAI,GAAG;AACnE,WAAO,WAAW,KAAK,IAAI,EAAE,MAAM,YAAY;EACvD;AACI,SAAO;AACX;AAQA,SAAS,2BAA2B,MAAM,cAAc;AACpD,QAAM,WAAW,KAAK,SAAS,aAAa,KAAK,MAAM,KAAK;AAE5D,MAAI,KAAK,UAAU;AACf,WAAO,gBAAgB,UAAU,YAAY;EACrD;AAEI,MAAI,SAAS,SAAS,cAAc;AAChC,WAAO,EAAE,OAAO,SAAS,KAAI;EACrC;AAEI,MAAI,SAAS,SAAS,WAAW;AAC7B,QAAI,SAAS,QAAQ;AACjB,aAAO,EAAE,OAAO,SAAS,OAAM;IAC3C;AACQ,WAAO,EAAE,OAAO,OAAO,SAAS,KAAK,EAAC;EAC9C;AAEI,SAAO;AACX;AAQO,SAAS,eAAe,MAAM,eAAe,MAAM;AACtD,MAAI;AACA,WAAO,gBAAgB,MAAM,YAAY;EACjD,SAAa,QAAQ;AACb,WAAO;EACf;AACA;ACjjBO,SAAS,oBAAoB,MAAM,eAAe,MAAM;AAE3D,MAAI,QAAQ,KAAK,SAAS,aAAa,KAAK,UAAU,MAAM;AACxD,QAAI,KAAK,OAAO;AACZ,aAAO,IAAI,KAAK,MAAM,OAAO,IAAI,KAAK,MAAM,KAAK;IAC7D;AACQ,QAAI,KAAK,QAAQ;AACb,aAAO,KAAK;IACxB;EACA;AAEI,QAAM,YAAY,eAAe,MAAM,YAAY;AACnD,SAAO,aAAa,OAAO,UAAU,KAAK;AAC9C;ACbO,SAAS,gBAAgB,MAAM,cAAc;AAChD,UAAQ,KAAK,MAAI;IACb,KAAK;AACD,UAAI,KAAK,UAAU;AACf,eAAO,oBAAoB,KAAK,UAAU,YAAY;MACtE;AACY,UAAI,KAAK,SAAS,SAAS,qBAAqB;AAC5C,eAAO;MACvB;AACY,aAAO,KAAK,SAAS;IAEzB,KAAK;IACL,KAAK;IACL,KAAK;AACD,UAAI,KAAK,UAAU;AACf,eAAO,oBAAoB,KAAK,KAAK,YAAY;MACjE;AACY,UAAI,KAAK,IAAI,SAAS,WAAW;AAC7B,eAAO,OAAO,KAAK,IAAI,KAAK;MAC5C;AACY,UAAI,KAAK,IAAI,SAAS,qBAAqB;AACvC,eAAO;MACvB;AACY,aAAO,KAAK,IAAI;EAG5B;AAEI,SAAO;AACX;AENA,SAAS,OAAO,GAAG;AACf,SAAO,MAAM,QAAQ,OAAO,MAAM,YAAY,OAAO,EAAE,SAAS;AACpE;AGdA,SAAS,iBAAiB,UAAU;AAChC,SACI,YAAY,QACZ,SAAS,KAAK,WAAW,KACzB,SAAS,WAAW,KAAK,CAAC,MAAM,EAAE,QAAO,CAAE;AAEnD;AAQA,SAAS,cAAc,MAAM;AACzB,QAAM,SAAS,KAAK;AAEpB,UAAQ,UAAU,OAAO,MAAI;IACzB,KAAK;AACD,aAAO,OAAO,eAAe,QAAQ,OAAO,cAAc;IAC9D,KAAK;AACD,aAAO;IACX,KAAK;AACD,aAAO,OAAO,YAAY,OAAO,YAAY,SAAS,CAAC,MAAM;IACjE,KAAK;AACD,aAAO;IAEX;AACI,aAAO;EACnB;AACA;AA8YA,SAAS,cAAcA,OAAM,OAAO;AAChC,SAAO,EAAE,UAAU,KAAKA,UAAS;AACrC;gCTlUa,iBACA,iBACA,qBACA,iBACA,wBACA,wBACA,0BACA,0BACA,wBACA,wBACA,mBErIP,cAWA,cAkDA,aA2CA,iBAiEA,YI3KA,yBAsBA,wBAWA,SG/BA,aACA,KAEO,MACA,MACA,WACA,KAEP,aA0CO;;;;ATyED,IAAC,kBAAkB,OAAO,YAAY;AACtC,IAAC,kBAAkB,OAAO,YAAY;AACtC,IAAC,sBAAsB,OAAO,gBAAgB;AAC9C,IAAC,kBAAkB,OAAO,YAAY;AACtC,IAAC,yBAAyB,OAAO,mBAAmB;AACpD,IAAC,yBAAyB,OAAO,mBAAmB;AACpD,IAAC,2BAA2B,OAAO,qBAAqB;AACxD,IAAC,2BAA2B,OAAO,qBAAqB;AACxD,IAAC,yBAAyB,OAAO,mBAAmB;AACpD,IAAC,yBAAyB,OAAO,mBAAmB;AACpD,IAAC,oBAAoB,OAAO,cAAc;AErItD,IAAM,eACF,OAAO,eAAe,cAChB,aACA,OAAO,SAAS,cAChB,OACA,OAAO,WAAW,cAClB,SACA,OAAO,WAAW,cAClB,SACA,CAAA;AAEV,IAAM,eAAe,OAAO;MACxB,oBAAI,IAAI;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACR,CAAK;IACL;AACA,IAAM,cAAc,IAAI;MACpB;QACI,MAAM;QACN,OAAO,WAAW,aAAa,SAAS;QACxC;QACA;QACA,KAAK;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,GAAG,OAAO,oBAAoB,IAAI,EAC7B,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,EAClB,OAAO,CAAC,MAAM,OAAO,MAAM,UAAU;QAC1C;QACA,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP;QACA,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP;QACA;QACA;QACA;QACA,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP;MACR,EAAM,OAAO,CAAC,MAAM,OAAO,MAAM,UAAU;IAC3C;AACA,IAAM,kBAAkB,oBAAI,IAAI;MAC5B,OAAO;MACP,OAAO;MACP,OAAO;IACX,CAAC;AA6DD,IAAM,aAAa,OAAO,OAAO;MAC7B,gBAAgB,MAAM,cAAc;AAChC,cAAM,WAAW,iBAAiB,KAAK,UAAU,YAAY;AAC7D,eAAO,YAAY,OAAO,EAAE,OAAO,SAAQ,IAAK;MACxD;MAEI,qBAAqB,MAAM,cAAc;AACrC,YAAI,KAAK,aAAa,KAAK;AACvB,iBAAO,gBAAgB,KAAK,OAAO,YAAY;QAC3D;AACQ,eAAO;MACf;;MAGI,iBAAiB,MAAM,cAAc;AACjC,YAAI,KAAK,aAAa,QAAQ,KAAK,aAAa,cAAc;AAE1D,iBAAO;QACnB;AAEQ,cAAM,OAAO,gBAAgB,KAAK,MAAM,YAAY;AACpD,cAAM,QAAQ,gBAAgB,KAAK,OAAO,YAAY;AACtD,YAAI,QAAQ,QAAQ,SAAS,MAAM;AAC/B,kBAAQ,KAAK,UAAQ;YACjB,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,SAAS,MAAM,MAAK;;YAC7C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,SAAS,MAAM,MAAK;;YAC7C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,UAAU,MAAM,MAAK;YAC9C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,UAAU,MAAM,MAAK;YAC9C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,QAAQ,MAAM,MAAK;YAC5C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,SAAS,MAAM,MAAK;YAC7C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,QAAQ,MAAM,MAAK;YAC5C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,SAAS,MAAM,MAAK;YAC7C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,SAAS,MAAM,MAAK;YAC7C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,SAAS,MAAM,MAAK;YAC7C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,UAAU,MAAM,MAAK;YAC9C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,QAAQ,MAAM,MAAK;YAC5C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,QAAQ,MAAM,MAAK;YAC5C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,QAAQ,MAAM,MAAK;YAC5C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,QAAQ,MAAM,MAAK;YAC5C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,QAAQ,MAAM,MAAK;YAC5C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,IAAI,KAAK,OAAO,MAAM,KAAK,EAAC;YACrD,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,QAAQ,MAAM,MAAK;YAC5C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,QAAQ,MAAM,MAAK;YAC5C,KAAK;AACD,qBAAO,EAAE,OAAO,KAAK,QAAQ,MAAM,MAAK;UAG5D;QACA;AAEQ,eAAO;MACf;MAEI,eAAe,MAAM,cAAc;AAC/B,cAAM,aAAa,KAAK;AACxB,cAAM,OAAO,iBAAiB,KAAK,WAAW,YAAY;AAE1D,YAAI,QAAQ,MAAM;AACd,cAAI,WAAW,SAAS,oBAAoB;AACxC,gBAAI,WAAW,SAAS,SAAS,qBAAqB;AAClD,qBAAO;YAC3B;AACgB,kBAAM,SAAS,gBAAgB,WAAW,QAAQ,YAAY;AAC9D,gBAAI,UAAU,MAAM;AAChB,kBACI,OAAO,SAAS,SACf,OAAO,YAAY,KAAK,WAC3B;AACE,uBAAO,EAAE,OAAO,QAAW,UAAU,KAAI;cACjE;AACoB,oBAAM,WAAW;gBACb;gBACA;cACxB;AAEoB,kBAAI,YAAY,MAAM;AAClB,sBAAM,WAAW,OAAO;AACxB,sBAAM,aAAa,SAAS;AAC5B,oBAAI,YAAY,IAAI,SAAS,UAAU,CAAC,GAAG;AACvC,yBAAO,EAAE,OAAO,SAAS,UAAU,EAAE,GAAG,IAAI,EAAC;gBACzE;AACwB,oBAAI,gBAAgB,IAAI,SAAS,UAAU,CAAC,GAAG;AAC3C,yBAAO,EAAE,OAAO,KAAK,CAAC,EAAC;gBACnD;cACA;YACA;UACA,OAAmB;AACH,kBAAM,SAAS,gBAAgB,YAAY,YAAY;AACvD,gBAAI,UAAU,MAAM;AAChB,kBAAI,OAAO,SAAS,QAAQ,KAAK,UAAU;AACvC,uBAAO,EAAE,OAAO,QAAW,UAAU,KAAI;cACjE;AACoB,oBAAM,OAAO,OAAO;AACpB,kBAAI,YAAY,IAAI,IAAI,GAAG;AACvB,uBAAO,EAAE,OAAO,KAAK,GAAG,IAAI,EAAC;cACrD;AACoB,kBAAI,gBAAgB,IAAI,IAAI,GAAG;AAC3B,uBAAO,EAAE,OAAO,KAAK,CAAC,EAAC;cAC/C;YACA;UACA;QACA;AAEQ,eAAO;MACf;MAEI,sBAAsB,MAAM,cAAc;AACtC,cAAM,OAAO,gBAAgB,KAAK,MAAM,YAAY;AACpD,YAAI,QAAQ,MAAM;AACd,iBAAO,KAAK,QACN,gBAAgB,KAAK,YAAY,YAAY,IAC7C,gBAAgB,KAAK,WAAW,YAAY;QAC9D;AACQ,eAAO;MACf;MAEI,oBAAoB,MAAM,cAAc;AACpC,eAAO,gBAAgB,KAAK,YAAY,YAAY;MAC5D;MAEI,WAAW,MAAM,cAAc;AAC3B,YAAI,gBAAgB,MAAM;AACtB,gBAAM,WAAW,aAAa,cAAc,IAAI;AAGhD,cACI,YAAY,QACZ,SAAS,KAAK,WAAW,KACzB,aAAa,IAAI,SAAS,IAAI,KAC9B,SAAS,QAAQ,cACnB;AACE,mBAAO,EAAE,OAAO,aAAa,SAAS,IAAI,EAAC;UAC3D;AAGY,cAAI,YAAY,QAAQ,SAAS,KAAK,WAAW,GAAG;AAChD,kBAAM,MAAM,SAAS,KAAK,CAAC;AAC3B,gBACI,IAAI,UACJ,IAAI,OAAO,SAAS;YAEpB,IAAI,KAAK,GAAG,SAAS,cACvB;AACE,qBAAO,gBAAgB,IAAI,KAAK,MAAM,YAAY;YACtE;UACA;QACA;AACQ,eAAO;MACf;MAEI,QAAQ,MAAM;AAEV,aAAK,KAAK,SAAS,QAAQ,KAAK,UAAU,SAAS,KAAK,SAAS,MAAM;AAEnE,iBAAO;QACnB;AACQ,eAAO,EAAE,OAAO,KAAK,MAAK;MAClC;MAEI,kBAAkB,MAAM,cAAc;AAClC,cAAM,OAAO,gBAAgB,KAAK,MAAM,YAAY;AACpD,YAAI,QAAQ,MAAM;AACd,cACK,KAAK,aAAa,QAAQ,QAAQ,KAAK,KAAK,MAAM,QAClD,KAAK,aAAa,QAAQ,QAAQ,KAAK,KAAK,MAAM,SAClD,KAAK,aAAa,QAAQ,KAAK,SAAS,MAC3C;AACE,mBAAO;UACvB;AAEY,gBAAM,QAAQ,gBAAgB,KAAK,OAAO,YAAY;AACtD,cAAI,SAAS,MAAM;AACf,mBAAO;UACvB;QACA;AAEQ,eAAO;MACf;MAEI,iBAAiB,MAAM,cAAc;AACjC,YAAI,KAAK,SAAS,SAAS,qBAAqB;AAC5C,iBAAO;QACnB;AACQ,cAAM,SAAS,gBAAgB,KAAK,QAAQ,YAAY;AACxD,YAAI,UAAU,MAAM;AAChB,cAAI,OAAO,SAAS,SAAS,OAAO,YAAY,KAAK,WAAW;AAC5D,mBAAO,EAAE,OAAO,QAAW,UAAU,KAAI;UACzD;AACY,gBAAM,WAAW,2BAA2B,MAAM,YAAY;AAE9D,cAAI,YAAY,QAAQ,CAAC,SAAS,OAAO,OAAO,SAAS,KAAK,GAAG;AAC7D,mBAAO,EAAE,OAAO,OAAO,MAAM,SAAS,KAAK,EAAC;UAC5D;QACA;AACQ,eAAO;MACf;MAEI,gBAAgB,MAAM,cAAc;AAChC,cAAM,aAAa,gBAAgB,KAAK,YAAY,YAAY;AAChE,YAAI,cAAc,MAAM;AACpB,iBAAO,EAAE,OAAO,WAAW,MAAK;QAC5C;AACQ,eAAO;MACf;MAEI,cAAc,MAAM,cAAc;AAC9B,cAAM,SAAS,gBAAgB,KAAK,QAAQ,YAAY;AACxD,cAAM,OAAO,iBAAiB,KAAK,WAAW,YAAY;AAE1D,YAAI,UAAU,QAAQ,QAAQ,MAAM;AAChC,gBAAM,OAAO,OAAO;AACpB,cAAI,YAAY,IAAI,IAAI,GAAG;AACvB,mBAAO,EAAE,OAAO,IAAI,KAAK,GAAG,IAAI,EAAC;UACjD;QACA;AAEQ,eAAO;MACf;MAEI,iBAAiB,MAAM,cAAc;AACjC,cAAM,SAAS,CAAA;AAEf,mBAAW,gBAAgB,KAAK,YAAY;AACxC,cAAI,aAAa,SAAS,YAAY;AAClC,gBAAI,aAAa,SAAS,QAAQ;AAC9B,qBAAO;YAC3B;AACgB,kBAAM,MAAM;cACR;cACA;YACpB;AACgB,kBAAM,QAAQ,gBAAgB,aAAa,OAAO,YAAY;AAC9D,gBAAI,OAAO,QAAQ,SAAS,MAAM;AAC9B,qBAAO;YAC3B;AACgB,mBAAO,IAAI,KAAK,IAAI,MAAM;UAC1C,WACgB,aAAa,SAAS,mBACtB,aAAa,SAAS,8BACxB;AACE,kBAAM,WAAW;cACb,aAAa;cACb;YACpB;AACgB,gBAAI,YAAY,MAAM;AAClB,qBAAO;YAC3B;AACgB,mBAAO,OAAO,QAAQ,SAAS,KAAK;UACpD,OAAmB;AACH,mBAAO;UACvB;QACA;AAEQ,eAAO,EAAE,OAAO,OAAM;MAC9B;MAEI,mBAAmB,MAAM,cAAc;AACnC,cAAM,OAAO,KAAK,YAAY,KAAK,YAAY,SAAS,CAAC;AACzD,eAAO,gBAAgB,MAAM,YAAY;MACjD;MAEI,yBAAyB,MAAM,cAAc;AACzC,cAAM,MAAM,gBAAgB,KAAK,KAAK,YAAY;AAClD,cAAM,cAAc;UAChB,KAAK,MAAM;UACX;QACZ;AAEQ,YAAI,OAAO,QAAQ,eAAe,MAAM;AACpC,gBAAM,OAAO,IAAI;AACjB,gBAAM,UAAU,KAAK,MAAM,OAAO,IAAI,CAAC,MAAM,EAAE,MAAM,MAAM;AAC3D,kBAAQ,MAAM,KAAK,MAAM,OAAO,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG;AAEtD,cAAI,SAAS,OAAO,KAAK;AACrB,mBAAO,EAAE,OAAO,KAAK,SAAS,GAAG,WAAW,EAAC;UAC7D;QACA;AAEQ,eAAO;MACf;MAEI,gBAAgB,MAAM,cAAc;AAChC,cAAM,cAAc,iBAAiB,KAAK,aAAa,YAAY;AACnE,YAAI,eAAe,MAAM;AACrB,cAAI,QAAQ,KAAK,OAAO,CAAC,EAAE,MAAM;AACjC,mBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,EAAE,GAAG;AACzC,qBAAS,YAAY,CAAC;AACtB,qBAAS,KAAK,OAAO,IAAI,CAAC,EAAE,MAAM;UAClD;AACY,iBAAO,EAAE,MAAK;QAC1B;AACQ,eAAO;MACf;MAEI,gBAAgB,MAAM,cAAc;AAChC,YAAI,KAAK,aAAa,UAAU;AAE5B,iBAAO;QACnB;AACQ,YAAI,KAAK,aAAa,QAAQ;AAC1B,iBAAO,EAAE,OAAO,OAAS;QACrC;AAEQ,cAAM,MAAM,gBAAgB,KAAK,UAAU,YAAY;AACvD,YAAI,OAAO,MAAM;AACb,kBAAQ,KAAK,UAAQ;YACjB,KAAK;AACD,qBAAO,EAAE,OAAO,CAAC,IAAI,MAAK;YAC9B,KAAK;AACD,qBAAO,EAAE,OAAO,CAAC,IAAI,MAAK;;YAC9B,KAAK;AACD,qBAAO,EAAE,OAAO,CAAC,IAAI,MAAK;YAC9B,KAAK;AACD,qBAAO,EAAE,OAAO,CAAC,IAAI,MAAK;YAC9B,KAAK;AACD,qBAAO,EAAE,OAAO,OAAO,IAAI,MAAK;UAGpD;QACA;AAEQ,eAAO;MACf;IACA,CAAC;AIjgBD,IAAM,0BAA0B,OAAO;MACnC,oBAAI,IAAI;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACR,CAAK;IACL;AACA,IAAM,yBAAyB,OAAO,OAAO,oBAAI,IAAI,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC,CAAC;AAW1E,IAAM,UAAU,OAAO;MACnB,OAAO,OAAO,uBAAO,OAAO,IAAI,GAAG;QAC/B,OAAO,MAAM,SAAS,aAAa;AAC/B,gBAAM,EAAE,KAAI,IAAK;AAEjB,cAAI,OAAO,KAAK,IAAI,MAAM,YAAY;AAClC,mBAAO,KAAK,IAAI,EAAE,MAAM,SAAS,WAAW;UAC5D;AAEY,iBAAO,KAAK,eAAe,MAAM,SAAS,WAAW;QACjE;QAEQ,eAAe,MAAM,SAAS,aAAa;AACvC,gBAAM,EAAE,KAAI,IAAK;AAEjB,qBAAW,OAAO,YAAY,IAAI,KAAK,2BAAAC,QAAI,QAAQ,IAAI,GAAG;AACtD,kBAAM,QAAQ,KAAK,GAAG;AAEtB,gBAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,yBAAW,WAAW,OAAO;AACzB,oBACI,OAAO,OAAO,KACd,KAAK,OAAO,SAAS,SAAS,WAAW,GAC3C;AACE,yBAAO;gBACnC;cACA;YACA,WACoB,OAAO,KAAK,KACZ,KAAK,OAAO,OAAO,SAAS,WAAW,GACzC;AACE,qBAAO;YAC3B;UACA;AAEY,iBAAO;QACnB;QAEQ,0BAA0B;AACtB,iBAAO;QACnB;QACQ,uBAAuB;AACnB,iBAAO;QACnB;QACQ,kBAAkB;AACd,iBAAO;QACnB;QACQ,iBAAiB,MAAM,SAAS,aAAa;AACzC,cACI,QAAQ,kCACR,wBAAwB,IAAI,KAAK,QAAQ,MACxC,KAAK,KAAK,SAAS,aAAa,KAAK,MAAM,SAAS,YACvD;AACE,mBAAO;UACvB;AACY,iBAAO,KAAK,eAAe,MAAM,SAAS,WAAW;QACjE;QACQ,iBAAiB;AACb,iBAAO;QACnB;QACQ,qBAAqB;AACjB,iBAAO;QACnB;QACQ,mBAAmB;AACf,iBAAO;QACnB;QACQ,iBAAiB,MAAM,SAAS,aAAa;AACzC,cAAI,QAAQ,iBAAiB;AACzB,mBAAO;UACvB;AACY,cACI,QAAQ,kCACR,KAAK,YACL,KAAK,SAAS,SAAS,WACzB;AACE,mBAAO;UACvB;AACY,iBAAO,KAAK,eAAe,MAAM,SAAS,WAAW;QACjE;QACQ,iBAAiB,MAAM,SAAS,aAAa;AACzC,cACI,QAAQ,kCACR,KAAK,YACL,KAAK,IAAI,SAAS,WACpB;AACE,mBAAO;UACvB;AACY,iBAAO,KAAK,eAAe,MAAM,SAAS,WAAW;QACjE;QACQ,gBAAgB;AACZ,iBAAO;QACnB;QACQ,SAAS,MAAM,SAAS,aAAa;AACjC,cACI,QAAQ,kCACR,KAAK,YACL,KAAK,IAAI,SAAS,WACpB;AACE,mBAAO;UACvB;AACY,iBAAO,KAAK,eAAe,MAAM,SAAS,WAAW;QACjE;QACQ,mBAAmB,MAAM,SAAS,aAAa;AAC3C,cACI,QAAQ,kCACR,KAAK,YACL,KAAK,IAAI,SAAS,WACpB;AACE,mBAAO;UACvB;AACY,iBAAO,KAAK,eAAe,MAAM,SAAS,WAAW;QACjE;QACQ,gBAAgB,MAAM,SAAS,aAAa;AACxC,cAAI,KAAK,aAAa,UAAU;AAC5B,mBAAO;UACvB;AACY,cACI,QAAQ,kCACR,uBAAuB,IAAI,KAAK,QAAQ,KACxC,KAAK,SAAS,SAAS,WACzB;AACE,mBAAO;UACvB;AACY,iBAAO,KAAK,eAAe,MAAM,SAAS,WAAW;QACjE;QACQ,mBAAmB;AACf,iBAAO;QACnB;QACQ,kBAAkB;AACd,iBAAO;QACnB;MACA,CAAK;IACL;AGnKA,IAAM,cAAc;AACpB,IAAM,MAAM,SAAS,KAAK,KAAK,OAAO,cAAc;AAExC,IAAC,OAAO,OAAO,MAAM;AACrB,IAAC,OAAO,OAAO,MAAM;AACrB,IAAC,YAAY,OAAO,WAAW;AAC/B,IAAC,MAAM,OAAO,KAAK;AAE/B,IAAM,cAAc,EAAE,SAAS,EAAE,CAAC,IAAI,GAAG,KAAI,EAAE;AA0CxC,IAAM,mBAAN,MAAuB;;;;;;;;MAQ1B,YACI,aACA;QACI,OAAO;QACP,oBAAoB,CAAC,UAAU,cAAc,QAAQ,QAAQ;MACzE,IAAY,CAAA,GACN;AACE,aAAK,gBAAgB,CAAA;AACrB,aAAK,cAAc;AACnB,aAAK,OAAO;AACZ,aAAK,oBAAoB,kBAAkB,MAAM,CAAC;MAC1D;;;;;;MAOI,CAAC,wBAAwB,UAAU;AAC/B,mBAAW,OAAO,OAAO,KAAK,QAAQ,GAAG;AACrC,gBAAM,eAAe,SAAS,GAAG;AACjC,gBAAM,OAAO,CAAC,GAAG;AACjB,gBAAM,WAAW,KAAK,YAAY,IAAI,IAAI,GAAG;AAE7C,cAAI,iBAAiB,QAAQ,GAAG;AAC5B;UAChB;AAEY,iBAAO,KAAK;YACR;YACA;YACA;YACA;UAChB;QACA;AAEQ,mBAAW,OAAO,KAAK,mBAAmB;AACtC,gBAAM,OAAO,CAAA;AACb,gBAAM,WAAW,KAAK,YAAY,IAAI,IAAI,GAAG;AAE7C,cAAI,iBAAiB,QAAQ,GAAG;AAC5B;UAChB;AAEY,iBAAO,KAAK;YACR;YACA;YACA;YACA;UAChB;QACA;MACA;;;;;;MAOI,CAAC,qBAAqB,UAAU;AAC5B,mBAAW,EAAE,KAAI,KAAM,KAAK,wBAAwB,WAAW,GAAG;AAC9D,gBAAM,MAAM,oBAAoB,KAAK,UAAU,CAAC,CAAC;AACjD,cAAI,OAAO,QAAQ,CAAC,IAAI,UAAU,GAAG,GAAG;AACpC;UAChB;AAEY,gBAAM,eAAe,SAAS,GAAG;AACjC,gBAAM,OAAO,CAAC,GAAG;AAEjB,cAAI,aAAa,IAAI,GAAG;AACpB,kBAAM;cACF;cACA;cACA,MAAM;cACN,MAAM,aAAa,IAAI;YAC3C;UACA;AACY,iBAAO,KAAK,2BAA2B,MAAM,MAAM,YAAY;QAC3E;MACA;;;;;;MAOI,CAAC,qBAAqB,UAAU;AAC5B,cAAM,cAAc,KAAK,YAAY;AAErC,mBAAW,QAAQ,YAAY,MAAM;AACjC,cAAI,CAAC,YAAY,KAAK,KAAK,IAAI,KAAK,KAAK,UAAU,MAAM;AACrD;UAChB;AACY,gBAAM,WAAW,KAAK,OAAO;AAE7B,cAAI,CAAC,IAAI,UAAU,QAAQ,GAAG;AAC1B;UAChB;AACY,gBAAM,eAAe,SAAS,QAAQ;AACtC,gBAAM,OAAO,CAAC,QAAQ;AAEtB,cAAI,aAAa,IAAI,GAAG;AACpB,kBAAM,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,aAAa,IAAI,EAAC;UACxE;AAEY,cAAI,KAAK,SAAS,wBAAwB;AACtC,uBAAW,OAAO,OAAO,KAAK,YAAY,GAAG;AACzC,oBAAM,iBAAiB,aAAa,GAAG;AACvC,kBAAI,eAAe,IAAI,GAAG;AACtB,sBAAM;kBACF;kBACA,MAAM,KAAK,OAAO,GAAG;kBACrB,MAAM;kBACN,MAAM,eAAe,IAAI;gBACrD;cACA;YACA;UACA,OAAmB;AACH,uBAAW,aAAa,KAAK,YAAY;AACrC,oBAAM,MAAM,IAAI,cAAc,GAAG;AACjC,oBAAM,KAAK,KAAK;gBACZ;gBACA;gBACA,MACM,eACA,KAAK,SAAS,WACd,EAAE,SAAS,cAAc,GAAG,aAAY,IACxC,EAAE,SAAS,aAAY;cACrD;AAEoB,kBAAI,KAAK;AACL,uBAAO;cAC/B,OAA2B;AACH,2BAAW,UAAU,IAAI;AACrB,yBAAO,OAAO,OAAO,KAAK,OAAO,aAAa;AAC9C,sBACI,OAAO,KAAK,UAAU,KACtB,OAAO,SAAS,MAClB;AACE,0BAAM;kBACtC;gBACA;cACA;YACA;UACA;QACA;MACA;;;;;;;;;MAUI,CAAC,2BAA2B,UAAU,MAAM,UAAU,cAAc;AAChE,YAAI,KAAK,cAAc,SAAS,QAAQ,GAAG;AACvC;QACZ;AACQ,aAAK,cAAc,KAAK,QAAQ;AAChC,YAAI;AACA,qBAAW,aAAa,SAAS,YAAY;AACzC,gBAAI,CAAC,UAAU,OAAM,GAAI;AACrB;YACpB;AACgB,kBAAM,OAAO,UAAU;AAEvB,gBAAI,gBAAgB,SAAS,IAAI,GAAG;AAChC,oBAAM,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,SAAS,IAAI,EAAC;YACxE;AACgB,mBAAO,KAAK,2BAA2B,MAAM,MAAM,QAAQ;UAC3E;QACA,UAAS;AACG,eAAK,cAAc,IAAG;QAClC;MACA;;;;;;;;;MAUI,CAAC,2BAA2B,UAAU,MAAM,UAAU;AAClD,YAAI,OAAO;AACX,eAAO,cAAc,IAAI,GAAG;AACxB,iBAAO,KAAK;QACxB;AAEQ,cAAM,SAAS,KAAK;AACpB,YAAI,OAAO,SAAS,oBAAoB;AACpC,cAAI,OAAO,WAAW,MAAM;AACxB,kBAAM,MAAM,gBAAgB,MAAM;AAClC,gBAAI,OAAO,QAAQ,CAAC,IAAI,UAAU,GAAG,GAAG;AACpC;YACpB;AAEgB,mBAAO,KAAK,OAAO,GAAG;AACtB,kBAAM,eAAe,SAAS,GAAG;AACjC,gBAAI,aAAa,IAAI,GAAG;AACpB,oBAAM;gBACF,MAAM;gBACN;gBACA,MAAM;gBACN,MAAM,aAAa,IAAI;cAC/C;YACA;AACgB,mBAAO,KAAK;cACR;cACA;cACA;YACpB;UACA;AACY;QACZ;AACQ,YAAI,OAAO,SAAS,kBAAkB;AAClC,cAAI,OAAO,WAAW,QAAQ,SAAS,IAAI,GAAG;AAC1C,kBAAM,EAAE,MAAM,QAAQ,MAAM,MAAM,MAAM,MAAM,SAAS,IAAI,EAAC;UAC5E;AACY;QACZ;AACQ,YAAI,OAAO,SAAS,iBAAiB;AACjC,cAAI,OAAO,WAAW,QAAQ,SAAS,SAAS,GAAG;AAC/C,kBAAM;cACF,MAAM;cACN;cACA,MAAM;cACN,MAAM,SAAS,SAAS;YAC5C;UACA;AACY;QACZ;AACQ,YAAI,OAAO,SAAS,wBAAwB;AACxC,cAAI,OAAO,UAAU,MAAM;AACvB,mBAAO,KAAK,sBAAsB,OAAO,MAAM,MAAM,QAAQ;AAC7D,mBAAO,KAAK,2BAA2B,QAAQ,MAAM,QAAQ;UAC7E;AACY;QACZ;AACQ,YAAI,OAAO,SAAS,qBAAqB;AACrC,cAAI,OAAO,UAAU,MAAM;AACvB,mBAAO,KAAK,sBAAsB,OAAO,MAAM,MAAM,QAAQ;UAC7E;AACY;QACZ;AACQ,YAAI,OAAO,SAAS,sBAAsB;AACtC,cAAI,OAAO,SAAS,MAAM;AACtB,mBAAO,KAAK,sBAAsB,OAAO,IAAI,MAAM,QAAQ;UAC3E;QACA;MACA;;;;;;;;MASI,CAAC,sBAAsB,aAAa,MAAM,UAAU;AAChD,YAAI,YAAY,SAAS,cAAc;AACnC,gBAAM,WAAW,aAAa,KAAK,aAAa,WAAW;AAC3D,cAAI,YAAY,MAAM;AAClB,mBAAO,KAAK;cACR;cACA;cACA;cACA;YACpB;UACA;AACY;QACZ;AACQ,YAAI,YAAY,SAAS,iBAAiB;AACtC,qBAAW,YAAY,YAAY,YAAY;AAC3C,kBAAM,MAAM,gBAAgB,QAAQ;AAEpC,gBAAI,OAAO,QAAQ,CAAC,IAAI,UAAU,GAAG,GAAG;AACpC;YACpB;AAEgB,kBAAM,WAAW,KAAK,OAAO,GAAG;AAChC,kBAAM,eAAe,SAAS,GAAG;AACjC,gBAAI,aAAa,IAAI,GAAG;AACpB,oBAAM;gBACF,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM,aAAa,IAAI;cAC/C;YACA;AACgB,mBAAO,KAAK;cACR,SAAS;cACT;cACA;YACpB;UACA;AACY;QACZ;AACQ,YAAI,YAAY,SAAS,qBAAqB;AAC1C,iBAAO,KAAK,sBAAsB,YAAY,MAAM,MAAM,QAAQ;QAC9E;MACA;;;;;;;;MASI,CAAC,yBAAyB,eAAe,MAAM,UAAU;AACrD,cAAM,OAAO,cAAc;AAE3B,YAAI,SAAS,qBAAqB,SAAS,0BAA0B;AACjE,gBAAM,MACF,SAAS,2BACH,YACA,cAAc,SAAS;AACjC,cAAI,CAAC,IAAI,UAAU,GAAG,GAAG;AACrB;UAChB;AAEY,iBAAO,KAAK,OAAO,GAAG;AACtB,gBAAM,eAAe,SAAS,GAAG;AACjC,cAAI,aAAa,IAAI,GAAG;AACpB,kBAAM;cACF,MAAM;cACN;cACA,MAAM;cACN,MAAM,aAAa,IAAI;YAC3C;UACA;AACY,iBAAO,KAAK;YACR,aAAa,KAAK,aAAa,cAAc,KAAK;YAClD;YACA;YACA;UAChB;AAEY;QACZ;AAEQ,YAAI,SAAS,4BAA4B;AACrC,iBAAO,KAAK;YACR,aAAa,KAAK,aAAa,cAAc,KAAK;YAClD;YACA;YACA;UAChB;AACY;QACZ;AAEQ,YAAI,SAAS,mBAAmB;AAC5B,gBAAM,MAAM,cAAc,MAAM;AAChC,cAAI,CAAC,IAAI,UAAU,GAAG,GAAG;AACrB;UAChB;AAEY,iBAAO,KAAK,OAAO,GAAG;AACtB,gBAAM,eAAe,SAAS,GAAG;AACjC,cAAI,aAAa,IAAI,GAAG;AACpB,kBAAM;cACF,MAAM;cACN;cACA,MAAM;cACN,MAAM,aAAa,IAAI;YAC3C;UACA;QACA;MACA;IACA;AAEA,qBAAiB,OAAO;AACxB,qBAAiB,OAAO;AACxB,qBAAiB,YAAY;AAC7B,qBAAiB,MAAM;;;;;AEvbvB,IAEa,UAqBP,0BAUO,sBAiCA,mBAKP,QAMO,aAoBA;AAjGb;AAAA;AAAA;AAEO,IAAM,WAAW,CAAC,SAAS,MAAM,OAAO,UAAU,oBAAI,IAAI,MAAM;AACrE,UAAI,QAAQ,IAAI,IAAI,GAAG;AACrB;AAAA,MACF;AAEA,cAAQ,IAAI,IAAI;AAChB,YAAM,IAAI;AAEV,OAAC,QAAQ,WAAW,YAAY,KAAK,IAAI,KAAK,CAAC,GAC5C,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC,EAEtB,OAAO,OAAO,EAEd,QAAQ,CAAC,UAAW,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAE,EAE3D,OAAO,OAAO,EAEd,OAAO,CAAC,UAAU,OAAO,MAAM,SAAS,QAAQ,EAChD,QAAQ,CAAC,UAAU,SAAS,SAAS,OAAO,OAAO,OAAO,CAAC;AAAA,IAChE;AAEA,IAAM,2BAA2B,CAAC,SAAS,aAAa;AACtD,YAAM,cAAc,CAAC;AACrB,eAAS,SAAS,UAAU,CAAC,SAAS;AACpC,YAAI,KAAK,SAAS,cAAc;AAC9B,sBAAY,KAAK,IAAI;AAAA,QACvB;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAEO,IAAM,uBAAuB,CAClC,SACA,MACA,QACA,UAAU,oBAAI,IAAI,MACf;AACH,UAAI,QAAQ,IAAI,IAAI,GAAG;AACrB,eAAO,CAAC;AAAA,MACV;AAEA,cAAQ,IAAI,IAAI;AAEhB,YAAM,WAAW,aAAa,QAAQ,WAAW,SAAS,IAAI,GAAG,IAAI;AACrE,UAAI,CAAC,UAAU;AAKb,eAAO,CAAC;AAAA,MACV;AAEA,YAAM,oBAAoB,SAAS,KAChC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,IAAI,EAC/B,OAAO,CAAC,QAAQ,OAAO,IAAI,IAAI,CAAC,EAChC,QAAQ,CAAC,QAAQ,yBAAyB,SAAS,IAAI,KAAK,IAAI,CAAC,EACjE;AAAA,QAAQ,CAAC,eACR,qBAAqB,SAAS,YAAY,QAAQ,OAAO;AAAA,MAC3D;AAGF,aAAO,kBAAkB,WAAW,IAAI,CAAC,QAAQ,IAAI;AAAA,IACvD;AAEO,IAAM,oBAAoB,CAAC,SAAS,SACzC,yBAAyB,SAAS,IAAI,EACnC,IAAI,CAAC,eAAe,OAAO,SAAS,UAAU,CAAC,EAC/C,OAAO,OAAO;AAEnB,IAAM,SAAS,CAAC,SAAS,eACvB;AAAA,MACE,QAAQ,WAAW,SAAS,UAAU;AAAA,MACtC;AAAA,IACF,GAAG,WAAW,KAAK,CAAC,QAAQ,IAAI,eAAe,UAAU;AAEpD,IAAM,cAAc,CAAC,KAAK,UAAU,IAAI,WAAW,WAAW;AACnE,UAAI,QAAQ,SAAS,kBAAkB;AAErC,YAAI,OAAO,IAAI;AACf,eAAO,KAAK,OAAO,SAAS,oBAAoB;AAC9C,iBAAO,KAAK;AAAA,QACd;AAEA,YAAI,QAAQ,WAAW,MAAM;AAC3B,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,QAAQ,SAAS,oBAAoB;AACvC,eAAO,YAAY,KAAK,QAAQ,MAAM;AAAA,MACxC;AAEA,aAAO;AAAA,IACT;AAEO,IAAM,SAAS,CAAC,SACrB,KAAK,SAAS,qBACb,KAAK,OAAO,SAAS,6BACpB,KAAK,OAAO,SAAS;AAAA;AAAA;;;ACTlB,SAAS,oBAAoB,SAAS,MAAM;AACjD,MAAI,CAAC,YAAY,IAAI,KAAK,KAAK,UAAU,SAAS,GAAG;AACnD,WAAO;AAAA,EACT;AAEA,QAAM,UAAU,KAAK,UAAU,CAAC;AAChC,MAAI,QAAQ,SAAS,mBAAmB;AACtC,WAAO;AAAA,EACT;AAEA,SAAO,kBAAkB,SAAS,OAAO;AAC3C;AAtGA,IAQa,4BAWA,sBAYA,cAUA,YAcA,aAUA,aAiBA,iBAsBA,SAMA,eAKA,gBAQA,SAEA,QAOA,WAMP,aAOO,iBAQA,cAcA,6BAqBP,0BA+BA,gBAYO,uBAIP,oBAcO;AAzPb;AAAA;AAAA;AAQO,IAAM,6BAA6B,CAAC,UACxC,KAAK,SAAS,yBACZ,KAAK,SAAS,yBACZ,KAAK,KAAK,SAAS,6BAClB,KAAK,KAAK,SAAS,sBACzB,KAAK,GAAG,SAAS,gBACjB,KAAK,GAAG,KAAK,CAAC,EAAE,YAAY,MAAM,KAAK,GAAG,KAAK,CAAC;AAK3C,IAAM,uBAAuB,CAAC,SACnC,KAAK,SAAS,wBACd,KAAK,QACL,KAAK,KAAK,SAAS,oBACnB,KAAK,KAAK,OAAO,SAAS,gBAC1B,CAAC,CAAC,QAAQ,YAAY,EAAE,SAAS,KAAK,KAAK,OAAO,IAAI,KACtD,KAAK,KAAK,UAAU,SAAS,MAC5B,KAAK,KAAK,UAAU,CAAC,EAAE,SAAS,6BAC/B,KAAK,KAAK,UAAU,CAAC,EAAE,SAAS,yBAClC,KAAK,GAAG,SAAS,gBACjB,KAAK,GAAG,KAAK,CAAC,EAAE,YAAY,MAAM,KAAK,GAAG,KAAK,CAAC;AAE3C,IAAM,eAAe,CAAC,UAC1B,KAAK,SAAS,yBACZ,KAAK,SAAS,wBACb,KAAK,SACJ,KAAK,KAAK,SAAS,6BAClB,KAAK,KAAK,SAAS,0BACzB,KAAK,GAAG,SAAS,gBACjB,KAAK,GAAG,KAAK,WAAW,KAAK,KAC7B,KAAK,GAAG,KAAK,CAAC,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,YAAY;AAE3C,IAAM,aAAa,CAAC,SACzB,KAAK,SAAS,wBACd,KAAK,QACL,KAAK,KAAK,SAAS,oBACnB,KAAK,KAAK,OAAO,SAAS,cAC1B,KAAK,GAAG,SAAS;AAAA,KAEhB,KAAK,GAAG,SAAS,WAAW,KAAK,KAAK,GAAG,SAAS,WAAW,MAC9D,KAAK,GAAG,SAAS,MAAM,CAAC,OAAO;AAG7B,aAAO,CAAC,MAAM,GAAG,SAAS;AAAA,IAC5B,CAAC;AAEI,IAAM,cAAc,CAAC,SAC1B,KAAK,SAAS,qBACZ,KAAK,OAAO,SAAS,iBACpB,KAAK,OAAO,SAAS,eACpB,KAAK,OAAO,SAAS,sBACtB,KAAK,OAAO,SAAS,sBACpB,KAAK,OAAO,OAAO,SAAS,YAC3B,KAAK,OAAO,SAAS,SAAS,eAC7B,KAAK,OAAO,SAAS,SAAS;AAE/B,IAAM,cAAc,CAAC,SAAS;AACnC,UAAI,CAAC,YAAY,IAAI,KAAK,KAAK,UAAU,SAAS,GAAG;AACnD,eAAO;AAAA,MACT;AAEA,YAAM,WAAW,KAAK,UAAU,CAAC;AACjC,UACE,SAAS,SAAS,6BAClB,SAAS,SAAS,sBAClB;AACA,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAGO,IAAM,kBAAkB,CAAC,SAAS,SAAS;AAChD,YAAM,WAAW,YAAY,IAAI;AACjC,UAAI,CAAC,UAAU;AACb,eAAO;AAAA,MACT;AAEA,aAAO,kBAAkB,SAAS,QAAQ;AAAA,IAC5C;AAeO,IAAM,UAAU,CAAC,QAAQ,YAAY,GAAG,MAAM;AAM9C,IAAM,gBAAgB,CAAC,SAAS,QACrC,QAAQ,GAAG,KACX,0BAA0B,SAAS,IAAI,UAAU,EAAE;AAAA,MAAc,CAAC,aAChE,QAAQ,QAAQ;AAAA,IAClB;AACK,IAAM,iBAAiB,CAAC,SAAS,QACtC,QAAQ,GAAG,KACX,0BAA0B,SAAS,IAAI,UAAU,EAAE;AAAA,MAAc,CAAC,aAChE,OAAO,QAAQ;AAAA,IACjB;AAIK,IAAM,UAAU,CAAC,aACtB,SAAS,KAAK,KAAK,CAAC,QAAQ,WAAW,IAAI,IAAI,CAAC;AAC3C,IAAM,SAAS,CAAC,aACrB,SAAS,KAAK;AAAA,MACZ,CAAC,QACC,IAAI,SAAS,gBACZ,2BAA2B,YAAY,IAAI,IAAI,CAAC,KAC/C,aAAa,YAAY,IAAI,IAAI,CAAC;AAAA,IACxC;AACK,IAAM,YAAY,CAAC,aACxB,SAAS,KAAK;AAAA,MACZ,CAAC,QACC,IAAI,SAAS,eAAe,qBAAqB,YAAY,IAAI,IAAI,CAAC;AAAA,IAC1E;AAEF,IAAM,cAAc,CAAC,SACnB,KAAK,SAAS,4BACV,KAAK,OAAO,SAAS,mBACnB,KAAK,OAAO,SACZ,KAAK,SACP;AAEC,IAAM,kBAAkB,CAAC,SAAS,QAAQ;AAC/C,aAAO,0BAA0B,SAAS,IAAI,UAAU,EACrD,KAAK,CAAC,aAAa,QAAQ,QAAQ,CAAC,GACnC,KAAK,KAAK,CAAC,QAAQ,WAAW,IAAI,IAAI,CAAC,GAAG;AAAA,IAChD;AAIO,IAAM,eAAe,CAAC,SAAS;AACpC,UAAI,CAAC,MAAM;AACT,eAAO;AAAA,MACT,YACG,KAAK,SAAS,6BACb,KAAK,SAAS,yBAChB,CAAC,OAAO,KAAK,MAAM,GACnB;AACA,eAAO,YAAY,KAAK,MAAM;AAAA,MAChC,OAAO;AACL,eAAO,aAAa,KAAK,MAAM;AAAA,MACjC;AAAA,IACF;AAEO,IAAM,8BAA8B,CACzC,SACA,cACA,UACA,kBACG;AACH,YAAM,kBAAkB,aAAa;AAAA,QAAO,CAAC,QAC3C,cAAc,SAAS,GAAG;AAAA,MAC5B;AAEA,YAAM,kBACJ,gBAAgB,SAAS,KACzB,gBAAgB,MAAM,CAAC,QAAQ,yBAAyB,SAAS,GAAG,CAAC,KACrE,gBAAgB,WACd,eAAe,SAAS,mBAAmB,aAAa,CAAC;AAE7D,aAAO,kBACH,SAAS,KAAK,CAAC,QAAQ,OAAO,IAAI,QAAQ,CAAC,IAC3C;AAAA,IACN;AAEA,IAAM,2BAA2B,CAAC,SAAS,cAAc;AACvD,YAAM,kBAAkB,YAAY,SAAS,EAAE,UAAU,CAAC;AAC1D,YAAM,oBAAoB,gBAAgB,SAAS,SAAS,EAAE,KAC3D,UAAU,CAAC;AAOd,YAAM,cAAc,CAAC,SAAS,SAAS,UAAa,KAAK,SAAS;AAClE,UAAI,YAAY,eAAe,KAAK,YAAY,iBAAiB,GAAG;AAClE,eAAO;AAAA,MACT;AAGA,UAAI,oBAAoB,QAAQ,sBAAsB,MAAM;AAC1D,eAAO;AAAA,MACT,WACG,mBAAmB,CAAC,qBACpB,CAAC,mBAAmB,mBACrB;AACA,eAAO;AAAA,MACT;AAEA,aACE,QAAQ,WAAW,QAAQ,eAAe,MAC1C,QAAQ,WAAW,QAAQ,iBAAiB;AAAA,IAEhD;AAEA,IAAM,iBAAiB,CAAC,SAAS,kBAAkB;AACjD,UAAI,QAAQ;AAEZ,eAAS,SAAS,eAAe,CAAC,SAAS;AACzC,YAAI,WAAW,IAAI,GAAG;AACpB;AAAA,QACF;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAEO,IAAM,wBAAwB,CAAC,mBACpC,eAAe,SAAS,WAAW,SAAS;AAG9C,IAAM,qBAAqB,CAAC,SAAS;AACnC,UAAI,CAAC,MAAM;AACT,eAAO;AAAA,MACT,WACE,2BAA2B,IAAI,KAC/B,qBAAqB,IAAI,KACzB,aAAa,IAAI,GACjB;AACA,eAAO;AAAA,MACT,OAAO;AACL,eAAO,mBAAmB,KAAK,MAAM;AAAA,MACvC;AAAA,IACF;AAEO,IAAM,4BAA4B,CAAC,SAAS,SACjD;AAAA,MACE;AAAA,MACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,CAACC,UAAS,CAAC,WAAWA,KAAI;AAAA,IAC5B,EAAE;AAAA,MACA,CAAC,aACC,OAAO,QAAQ,KACf,SAAS,KAAK,MAAM,CAAC,QAAQ,IAAI,SAAS,WAAW;AAAA,IACzD;AAAA;AAAA;;;ACxQF,IAIa;AAJb;AAAA;AAAA,UAAM,UAAU,gBAAgB,SAAU,WAAW;AACnD,aAAO,KAAK,SAAS,KAAK,KAAK,MAAM,SAAS;AAAA,IAChD;AAEO,IAAM,cAAc,CAAC,MAAM,SAAS;AACzC,UAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,eAAO;AAAA,MACT;AACA,aAAO,KAAK,MAAM,CAAC,SAAS,UAAU,YAAY,KAAK,KAAK,CAAC;AAAA,IAC/D;AAAA;AAAA;;;ACTA,IAmBa,MAEA;AArBb;AAAA;AAAA;AACA;AACA;AAeA;AAEO,IAAM,OAAO;AAEb,IAAM,OAAO;AAAA,MAClB,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,aAAa;AAAA,UACb,KAAK;AAAA,QACP;AAAA,QACA,QAAQ,CAAC;AAAA,QACT;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,aAAa;AAAA,QACpB,gBAAgB,CAAC,SAAS;AACxB,cAAI,CAAC,YAAY,IAAI,GAAG;AACtB;AAAA,UACF;AAEA,gBAAM,eAAe,gBAAgB,SAAS,IAAI;AAClD,gBAAM,WAAW,oBAAoB,SAAS,IAAI;AAElD,cAAI,CAAC,gBAAgB,CAAC,UAAU;AAC9B;AAAA,UACF,WAAW,aAAa,WAAW,GAAG;AAGpC,oBAAQ,OAAO;AAAA,cACb;AAAA,cACA,WAAW,WAAW;AAAA,YACxB,CAAC;AACD;AAAA,UACF;AAEA,gBAAM,0BAA0B;AAAA,YAC9B;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,cAAI,yBAAyB;AAC3B,kBAAM,WAAW,wBAAwB,WAAW;AACpD,oBAAQ,OAAO;AAAA,cACb;AAAA,cACA,WAAW,WAAW;AAAA,cACtB,MAAM,EAAE,MAAM,SAAS;AAAA,YACzB,CAAC;AAED;AAAA,UACF;AAEA,uBACG;AAAA,YACC,CAAC,QACC,cAAc,SAAS,GAAG,KACzB,eAAe,SAAS,GAAG;AAAA,YAE1B,CAAC,UAAU,IAAI,QAAQ;AAAA,UAC7B,EAKC,OAAO,CAAC,QAAQ,aAAa,IAAI,UAAU,CAAC,EAC5C,QAAQ,CAAC,QAAQ;AAChB,kBAAM,WAAW,YAAY,GAAG;AAEhC,gBAAI,cAAc,SAAS,GAAG,GAAG;AAC/B,oBAAM,eAAe,gBAAgB,SAAS,GAAG;AACjD,oBAAM,aACJ,aAAa,GAAG,SAAS,CAAC,KAAK,aAAa,GAAG,SAAS,CAAC,IACxD;AAEH,kBAAI,SAAS,WAAW,GAAG;AACzB,wBAAQ,OAAO;AAAA,kBACb,MAAM;AAAA,kBACN,WAAW,WAAW;AAAA,kBACtB,MAAM,EAAE,OAAO,UAAU;AAAA,gBAC3B,CAAC;AAAA,cACH;AAGA,oBAAM,oBAAoB,SAAS,UAChC,QAAQ,CAAC,QAAQ,kBAAkB,SAAS,GAAG,CAAC,EAChD;AAAA,gBAAQ,CAACC,SACR,0BAA0B,SAASA,KAAI,UAAU;AAAA,cACnD,EACC;AAAA,gBACC,CAAC,aACC,QAAQ,QAAQ,KACf,OAAO,QAAQ,KAAK,CAAC,UAAU,QAAQ;AAAA,cAC5C;AACF,oBAAM,qBAAqB,SAAS,UACjC,QAAQ,CAAC,QAAQ,kBAAkB,SAAS,GAAG,CAAC,EAChD;AAAA,gBAAQ,CAACA,SACR,0BAA0B,SAASA,KAAI,UAAU;AAAA,cACnD,EACC;AAAA,gBACC,CAAC,aACE,CAAC,QAAQ,QAAQ,KAAK,CAAC,OAAO,QAAQ,KACvC,UAAU,QAAQ;AAAA,cACtB;AACF,oBAAM,kBAAkB,SAAS,UAC9B,QAAQ,CAAC,QAAQ,kBAAkB,SAAS,GAAG,CAAC,EAIhD;AAAA,gBAAO,CAACA,SACPA,KAAI,SAAS,KAAK,MAAM,CAAC,QAAQ,IAAI,SAAS,WAAW;AAAA,cAC3D,EACC;AAAA,gBAAc,CAAC,WACd,SAAS;AAAA,kBAAK,CAAC;AAAA;AAAA,oBAEb;AAAA,sBACE,0BAA0B,SAAS,OAAO,UAAU;AAAA,sBACpD,0BAA0B,SAAS,OAAO,UAAU;AAAA,oBACtD;AAAA;AAAA,gBACF;AAAA,cACF;AACF,oBAAM,oBAAoB,SACvB;AAAA,gBAAQ,CAACA,SACR,0BAA0B,SAASA,KAAI,UAAU;AAAA,cACnD,EACC;AAAA,gBACC,CAAC,aACC,QAAQ,QAAQ,KACf,OAAO,QAAQ,KAAK,CAAC,UAAU,QAAQ;AAAA,cAC5C;AAEF,kBACE;AAAA;AAAA;AAAA;AAAA,cAKC,mBAAmB,sBAAsB,GAAG,MAAM,GACnD;AACA,wBAAQ,OAAO;AAAA,kBACb,MAAM;AAAA,kBACN,WAAW,WAAW;AAAA,kBACtB,MAAM,EAAE,OAAO,UAAU;AAAA,gBAC3B,CAAC;AAAA,cACH;AAEA,kBACE,CAAC,qBACD,CAAC,sBACD,mBACA;AACA,wBAAQ,OAAO;AAAA,kBACb,MAAM;AAAA,kBACN,WAAW,WAAW;AAAA,gBACxB,CAAC;AAAA,cACH;AAAA,YACF,WAAW,eAAe,SAAS,GAAG,GAAG;AAcvC,sBAAQ,OAAO;AAAA,gBACb,MAAM;AAAA,gBACN,WAAW,WAAW;AAAA,cACxB,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AClMA;AAAA,sCAAAC,UAAAC,SAAA;AAAA,IAAAA,QAAA;AAAA,MACC,KAAO;AAAA,QACN,QAAU;AAAA,QACV,SAAW;AAAA,MACZ;AAAA,MACA,aAAe;AAAA,QACd,GAAK;AAAA,QACL,aAAe;AAAA,QACf,YAAc;AAAA,QACd,SAAW;AAAA,QACX,OAAS;AAAA,QACT,SAAW;AAAA,QACX,MAAQ;AAAA,QACR,iBAAmB;AAAA,QACnB,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,KAAO;AAAA,MACR;AAAA,MACA,UAAY;AAAA,QACX,cAAgB;AAAA,QAChB,MAAQ;AAAA,QACR,mBAAqB;AAAA,QACrB,kBAAoB;AAAA,QACpB,iBAAmB;AAAA,QACnB,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,iBAAmB;AAAA,MACpB;AAAA,MACA,SAAW;AAAA,QACV,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,2BAA6B;AAAA,QAC7B,eAAiB;AAAA,QACjB,eAAiB;AAAA,QACjB,kBAAoB;AAAA,QACpB,IAAM;AAAA,QACN,IAAM;AAAA,QACN,iBAAmB;AAAA,QACnB,eAAiB;AAAA,QACjB,OAAS;AAAA,QACT,cAAgB;AAAA,QAChB,WAAa;AAAA,QACb,iBAAmB;AAAA,QACnB,gBAAkB;AAAA,QAClB,wBAA0B;AAAA,QAC1B,mBAAqB;AAAA,QACrB,sBAAwB;AAAA,QACxB,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,OAAS;AAAA,QACT,aAAe;AAAA,QACf,uBAAyB;AAAA,QACzB,cAAgB;AAAA,QAChB,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,sBAAwB;AAAA,QACxB,cAAgB;AAAA,QAChB,eAAiB;AAAA,QACjB,WAAa;AAAA,QACb,YAAc;AAAA,QACd,eAAiB;AAAA,QACjB,sBAAwB;AAAA,QACxB,0BAA4B;AAAA,QAC5B,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,yBAA2B;AAAA,QAC3B,kBAAoB;AAAA,QACpB,uBAAyB;AAAA,QACzB,gCAAkC;AAAA,QAClC,kCAAoC;AAAA,QACpC,uBAAyB;AAAA,QACzB,wBAA0B;AAAA,QAC1B,uBAAyB;AAAA,QACzB,6BAA+B;AAAA,QAC/B,iBAAmB;AAAA,QACnB,SAAW;AAAA,QACX,kBAAoB;AAAA,QACpB,gBAAkB;AAAA,QAClB,mBAAqB;AAAA,QACrB,kBAAoB;AAAA,QACpB,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,WAAa;AAAA,QACb,mCAAqC;AAAA,QACrC,iBAAmB;AAAA,QACnB,mCAAqC;AAAA,QACrC,+BAAiC;AAAA,QACjC,2BAA6B;AAAA,QAC7B,4BAA8B;AAAA,QAC9B,eAAiB;AAAA,QACjB,MAAQ;AAAA,QACR,kBAAoB;AAAA,QACpB,gCAAkC;AAAA,QAClC,MAAQ;AAAA,QACR,2BAA6B;AAAA,QAC7B,OAAS;AAAA,QACT,QAAU;AAAA,QACV,cAAgB;AAAA,QAChB,sBAAwB;AAAA,QACxB,oBAAsB;AAAA,QACtB,0BAA4B;AAAA,QAC5B,+BAAiC;AAAA,QACjC,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,0BAA4B;AAAA,QAC5B,mBAAqB;AAAA,QACrB,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,mBAAqB;AAAA,QACrB,qBAAuB;AAAA,QACvB,oBAAsB;AAAA,QACtB,4BAA8B;AAAA,QAC9B,eAAiB;AAAA,QACjB,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,YAAc;AAAA,QACd,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,SAAW;AAAA,QACX,kBAAoB;AAAA,QACpB,mBAAqB;AAAA,QACrB,SAAW;AAAA,QACX,SAAW;AAAA,QACX,oBAAsB;AAAA,QACtB,uCAAyC;AAAA,QACzC,eAAiB;AAAA,QACjB,mBAAqB;AAAA,QACrB,wBAA0B;AAAA,QAC1B,aAAe;AAAA,QACf,aAAe;AAAA,QACf,oBAAsB;AAAA,QACtB,sBAAwB;AAAA,QACxB,mBAAqB;AAAA,QACrB,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,sBAAwB;AAAA,QACxB,YAAc;AAAA,QACd,qBAAuB;AAAA,QACvB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,WAAa;AAAA,QACb,wBAA0B;AAAA,QAC1B,KAAO;AAAA,QACP,cAAgB;AAAA,QAChB,kBAAoB;AAAA,QACpB,kBAAoB;AAAA,QACpB,qBAAuB;AAAA,QACvB,iBAAmB;AAAA,QACnB,0BAA4B;AAAA,QAC5B,0BAA4B;AAAA,QAC5B,iBAAmB;AAAA,QACnB,eAAiB;AAAA,QACjB,eAAiB;AAAA,QACjB,iBAAmB;AAAA,QACnB,kBAAoB;AAAA,QACpB,iBAAmB;AAAA,QACnB,mBAAqB;AAAA,QACrB,uBAAyB;AAAA,QACzB,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,eAAiB;AAAA,QACjB,YAAc;AAAA,QACd,YAAc;AAAA,QACd,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,YAAc;AAAA,QACd,cAAgB;AAAA,QAChB,oBAAsB;AAAA,QACtB,cAAgB;AAAA,QAChB,kBAAoB;AAAA,QACpB,uBAAyB;AAAA,QACzB,iBAAmB;AAAA,QACnB,iBAAmB;AAAA,QACnB,oBAAsB;AAAA,QACtB,aAAe;AAAA,QACf,gBAAkB;AAAA,QAClB,2BAA6B;AAAA,QAC7B,oBAAsB;AAAA,QACtB,kBAAoB;AAAA,QACpB,iBAAmB;AAAA,QACnB,WAAa;AAAA,QACb,SAAW;AAAA,QACX,aAAe;AAAA,QACf,UAAY;AAAA,QACZ,cAAgB;AAAA,QAChB,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,sBAAwB;AAAA,QACxB,qBAAuB;AAAA,QACvB,cAAgB;AAAA,QAChB,eAAiB;AAAA,QACjB,eAAiB;AAAA,QACjB,iBAAmB;AAAA,QACnB,uBAAyB;AAAA,QACzB,mBAAqB;AAAA,QACrB,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,kBAAoB;AAAA,QACpB,2BAA6B;AAAA,QAC7B,uBAAyB;AAAA,QACzB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,uBAAyB;AAAA,QACzB,gBAAkB;AAAA,QAClB,aAAe;AAAA,QACf,gBAAkB;AAAA,QAClB,cAAgB;AAAA,QAChB,kBAAoB;AAAA,QACpB,sBAAwB;AAAA,QACxB,qBAAuB;AAAA,QACvB,WAAa;AAAA,QACb,4BAA8B;AAAA,QAC9B,mBAAqB;AAAA,QACrB,+BAAiC;AAAA,QACjC,+BAAiC;AAAA,QACjC,wBAA0B;AAAA,QAC1B,kBAAoB;AAAA,QACpB,eAAiB;AAAA,QACjB,eAAiB;AAAA,QACjB,iBAAmB;AAAA,QACnB,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,kBAAoB;AAAA,QACpB,0BAA4B;AAAA,QAC5B,0BAA4B;AAAA,QAC5B,+BAAiC;AAAA,QACjC,kBAAoB;AAAA,QACpB,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,cAAgB;AAAA,QAChB,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,kBAAoB;AAAA,QACpB,SAAW;AAAA,QACX,SAAW;AAAA,QACX,aAAe;AAAA,QACf,iBAAmB;AAAA,QACnB,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,WAAa;AAAA,QACb,wBAA0B;AAAA,QAC1B,aAAe;AAAA,QACf,SAAW;AAAA,QACX,kBAAoB;AAAA,QACpB,mBAAqB;AAAA,QACrB,mBAAqB;AAAA,QACrB,YAAc;AAAA,QACd,OAAS;AAAA,QACT,OAAS;AAAA,QACT,aAAe;AAAA,QACf,aAAe;AAAA,QACf,aAAe;AAAA,QACf,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,eAAiB;AAAA,QACjB,qBAAuB;AAAA,QACvB,OAAS;AAAA,QACT,OAAS;AAAA,QACT,mBAAqB;AAAA,QACrB,OAAS;AAAA,QACT,YAAc;AAAA,QACd,kBAAoB;AAAA,QACpB,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,YAAc;AAAA,QACd,0BAA4B;AAAA,QAC5B,2BAA6B;AAAA,QAC7B,2BAA6B;AAAA,QAC7B,iBAAmB;AAAA,QACnB,qBAAuB;AAAA,QACvB,sBAAwB;AAAA,QACxB,kBAAoB;AAAA,QACpB,oBAAsB;AAAA,QACtB,8BAAgC;AAAA,QAChC,MAAQ;AAAA,QACR,OAAS;AAAA,QACT,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,aAAe;AAAA,QACf,sBAAwB;AAAA,QACxB,UAAY;AAAA,QACZ,eAAiB;AAAA,QACjB,mBAAqB;AAAA,QACrB,cAAgB;AAAA,QAChB,QAAU;AAAA,QACV,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,sBAAwB;AAAA,QACxB,eAAiB;AAAA,QACjB,oBAAsB;AAAA,QACtB,cAAgB;AAAA,QAChB,uBAAyB;AAAA,QACzB,aAAe;AAAA,QACf,aAAe;AAAA,QACf,wBAA0B;AAAA,QAC1B,qBAAuB;AAAA,QACvB,0BAA4B;AAAA,QAC5B,kBAAoB;AAAA,QACpB,kBAAoB;AAAA,QACpB,cAAgB;AAAA,QAChB,KAAO;AAAA,QACP,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,cAAgB;AAAA,QAChB,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,kBAAoB;AAAA,QACpB,eAAiB;AAAA,QACjB,kBAAoB;AAAA,QACpB,mBAAqB;AAAA,QACrB,oBAAsB;AAAA,QACtB,uBAAyB;AAAA,QACzB,uBAAyB;AAAA,QACzB,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,mBAAqB;AAAA,QACrB,UAAY;AAAA,QACZ,oBAAsB;AAAA,QACtB,kBAAoB;AAAA,QACpB,YAAc;AAAA,QACd,qBAAuB;AAAA,QACvB,kBAAoB;AAAA,QACpB,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,UAAY;AAAA,QACZ,iBAAmB;AAAA,QACnB,wBAA0B;AAAA,QAC1B,sBAAwB;AAAA,QACxB,mBAAqB;AAAA,QACrB,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,gBAAkB;AAAA,QAClB,sBAAwB;AAAA,QACxB,oBAAsB;AAAA,QACtB,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,gBAAkB;AAAA,QAClB,yBAA2B;AAAA,QAC3B,oBAAsB;AAAA,QACtB,eAAiB;AAAA,QACjB,WAAa;AAAA,QACb,iBAAmB;AAAA,QACnB,SAAW;AAAA,QACX,KAAO;AAAA,QACP,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,qBAAuB;AAAA,QACvB,WAAa;AAAA,QACb,mBAAqB;AAAA,QACrB,SAAW;AAAA,QACX,SAAW;AAAA,QACX,mBAAqB;AAAA,QACrB,mBAAqB;AAAA,QACrB,iBAAmB;AAAA,QACnB,kBAAoB;AAAA,QACpB,iBAAmB;AAAA,QACnB,iBAAmB;AAAA,QACnB,eAAiB;AAAA,QACjB,mBAAqB;AAAA,QACrB,mBAAqB;AAAA,QACrB,gBAAkB;AAAA,QAClB,iBAAmB;AAAA,QACnB,qBAAuB;AAAA,QACvB,oBAAsB;AAAA,QACtB,mBAAqB;AAAA,QACrB,sBAAwB;AAAA,QACxB,gBAAkB;AAAA,QAClB,kBAAoB;AAAA,QACpB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,wBAA0B;AAAA,QAC1B,qBAAuB;AAAA,QACvB,iBAAmB;AAAA,QACnB,4BAA8B;AAAA,QAC9B,iBAAmB;AAAA,QACnB,kBAAoB;AAAA,QACpB,qBAAuB;AAAA,QACvB,iBAAmB;AAAA,QACnB,oBAAsB;AAAA,QACtB,eAAiB;AAAA,QACjB,iBAAmB;AAAA,QACnB,mBAAqB;AAAA,QACrB,kBAAoB;AAAA,QACpB,kBAAoB;AAAA,QACpB,kBAAoB;AAAA,QACpB,mBAAqB;AAAA,QACrB,eAAiB;AAAA,QACjB,iBAAmB;AAAA,QACnB,gBAAkB;AAAA,QAClB,oBAAsB;AAAA,QACtB,kBAAoB;AAAA,QACpB,iBAAmB;AAAA,QACnB,iBAAmB;AAAA,QACnB,kBAAoB;AAAA,QACpB,gBAAkB;AAAA,QAClB,mBAAqB;AAAA,QACrB,kBAAoB;AAAA,QACpB,qBAAuB;AAAA,QACvB,mBAAqB;AAAA,QACrB,uBAAyB;AAAA,QACzB,mBAAqB;AAAA,QACrB,sBAAwB;AAAA,QACxB,kBAAoB;AAAA,QACpB,oBAAsB;AAAA,QACtB,gBAAkB;AAAA,QAClB,qBAAuB;AAAA,QACvB,kBAAoB;AAAA,QACpB,mBAAqB;AAAA,QACrB,4BAA8B;AAAA,QAC9B,mBAAqB;AAAA,QACrB,iBAAmB;AAAA,QACnB,mBAAqB;AAAA,QACrB,iBAAmB;AAAA,QACnB,kBAAoB;AAAA,QACpB,yBAA2B;AAAA,QAC3B,sBAAwB;AAAA,QACxB,qBAAuB;AAAA,QACvB,kBAAoB;AAAA,QACpB,qBAAuB;AAAA,QACvB,yBAA2B;AAAA,QAC3B,qBAAuB;AAAA,QACvB,qBAAuB;AAAA,QACvB,iBAAmB;AAAA,QACnB,kBAAoB;AAAA,QACpB,kBAAoB;AAAA,QACpB,kBAAoB;AAAA,QACpB,oBAAsB;AAAA,QACtB,kBAAoB;AAAA,QACpB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,aAAe;AAAA,QACf,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,aAAe;AAAA,QACf,gBAAkB;AAAA,QAClB,kBAAoB;AAAA,QACpB,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,uBAAyB;AAAA,QACzB,oBAAsB;AAAA,QACtB,yBAA2B;AAAA,QAC3B,kBAAoB;AAAA,QACpB,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,eAAiB;AAAA,QACjB,OAAS;AAAA,QACT,aAAe;AAAA,QACf,6BAA+B;AAAA,QAC/B,cAAgB;AAAA,QAChB,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,WAAa;AAAA,QACb,KAAO;AAAA,QACP,aAAe;AAAA,QACf,YAAc;AAAA,QACd,yBAA2B;AAAA,QAC3B,iBAAmB;AAAA,QACnB,YAAc;AAAA,QACd,sBAAwB;AAAA,QACxB,2BAA6B;AAAA,QAC7B,iBAAmB;AAAA,QACnB,UAAY;AAAA,QACZ,eAAiB;AAAA,QACjB,mBAAqB;AAAA,QACrB,gBAAkB;AAAA,QAClB,kBAAoB;AAAA,QACpB,wBAA0B;AAAA,QAC1B,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,aAAe;AAAA,QACf,aAAe;AAAA,QACf,wBAA0B;AAAA,QAC1B,QAAU;AAAA,QACV,0BAA4B;AAAA,QAC5B,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,aAAe;AAAA,QACf,MAAQ;AAAA,QACR,aAAe;AAAA,QACf,YAAc;AAAA,QACd,eAAiB;AAAA,QACjB,mBAAqB;AAAA,QACrB,uBAAyB;AAAA,QACzB,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,6BAA+B;AAAA,QAC/B,qBAAuB;AAAA,QACvB,YAAc;AAAA,QACd,eAAiB;AAAA,QACjB,sBAAwB;AAAA,QACxB,WAAa;AAAA,QACb,iBAAmB;AAAA,QACnB,mBAAqB;AAAA,QACrB,sBAAwB;AAAA,QACxB,WAAa;AAAA,QACb,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,qBAAuB;AAAA,QACvB,eAAiB;AAAA,QACjB,yBAA2B;AAAA,QAC3B,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,iCAAmC;AAAA,QACnC,4BAA8B;AAAA,QAC9B,kBAAoB;AAAA,QACpB,kBAAoB;AAAA,QACpB,iCAAmC;AAAA,QACnC,4BAA8B;AAAA,QAC9B,uBAAyB;AAAA,QACzB,2BAA6B;AAAA,QAC7B,2BAA6B;AAAA,QAC7B,4BAA8B;AAAA,QAC9B,SAAW;AAAA,QACX,gBAAkB;AAAA,QAClB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,YAAc;AAAA,QACd,qBAAuB;AAAA,QACvB,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,kBAAoB;AAAA,QACpB,YAAc;AAAA,QACd,eAAiB;AAAA,QACjB,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,eAAiB;AAAA,QACjB,OAAS;AAAA,QACT,qBAAuB;AAAA,QACvB,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,QAAU;AAAA,QACV,QAAU;AAAA,QACV,eAAiB;AAAA,QACjB,kBAAoB;AAAA,QACpB,gBAAkB;AAAA,QAClB,MAAQ;AAAA,QACR,cAAgB;AAAA,QAChB,eAAiB;AAAA,QACjB,YAAc;AAAA,QACd,YAAc;AAAA,QACd,sBAAwB;AAAA,QACxB,mCAAqC;AAAA,QACrC,uBAAyB;AAAA,QACzB,wBAA0B;AAAA,QAC1B,0BAA4B;AAAA,QAC5B,sBAAwB;AAAA,QACxB,WAAa;AAAA,QACb,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,sBAAwB;AAAA,QACxB,iBAAmB;AAAA,QACnB,oBAAsB;AAAA,QACtB,MAAQ;AAAA,QACR,YAAc;AAAA,QACd,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,cAAgB;AAAA,QAChB,kBAAoB;AAAA,QACpB,0BAA4B;AAAA,QAC5B,oBAAsB;AAAA,QACtB,YAAc;AAAA,QACd,6BAA+B;AAAA,QAC/B,qBAAuB;AAAA,QACvB,oBAAsB;AAAA,QACtB,iBAAmB;AAAA,QACnB,mCAAqC;AAAA,QACrC,SAAW;AAAA,QACX,cAAgB;AAAA,QAChB,mBAAqB;AAAA,QACrB,gBAAkB;AAAA,QAClB,sBAAwB;AAAA,QACxB,kBAAoB;AAAA,QACpB,gBAAkB;AAAA,QAClB,YAAc;AAAA,QACd,eAAiB;AAAA,QACjB,uBAAyB;AAAA,QACzB,eAAiB;AAAA,QACjB,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,gBAAkB;AAAA,QAClB,kBAAoB;AAAA,QACpB,QAAU;AAAA,QACV,UAAY;AAAA,QACZ,WAAa;AAAA,QACb,kBAAoB;AAAA,QACpB,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,QACX,WAAa;AAAA,QACb,oCAAsC;AAAA,QACtC,eAAiB;AAAA,QACjB,eAAiB;AAAA,QACjB,mBAAqB;AAAA,QACrB,QAAU;AAAA,QACV,aAAe;AAAA,QACf,OAAS;AAAA,QACT,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,qBAAuB;AAAA,QACvB,6BAA+B;AAAA,QAC/B,QAAU;AAAA,QACV,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,aAAe;AAAA,QACf,QAAU;AAAA,QACV,kBAAoB;AAAA,QACpB,WAAa;AAAA,QACb,SAAW;AAAA,QACX,SAAW;AAAA,QACX,SAAW;AAAA,QACX,YAAc;AAAA,QACd,oBAAsB;AAAA,QACtB,uBAAyB;AAAA,QACzB,qBAAuB;AAAA,QACvB,cAAgB;AAAA,QAChB,SAAW;AAAA,QACX,WAAa;AAAA,QACb,WAAa;AAAA,QACb,YAAc;AAAA,QACd,SAAW;AAAA,QACX,kBAAoB;AAAA,QACpB,QAAU;AAAA,QACV,cAAgB;AAAA,QAChB,kBAAoB;AAAA,QACpB,aAAe;AAAA,QACf,sBAAwB;AAAA,QACxB,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,aAAe;AAAA,QACf,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,YAAc;AAAA,QACd,aAAe;AAAA,QACf,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,YAAc;AAAA,QACd,SAAW;AAAA,QACX,SAAW;AAAA,QACX,QAAU;AAAA,QACV,WAAa;AAAA,QACb,iBAAmB;AAAA,QACnB,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,eAAiB;AAAA,QACjB,oBAAsB;AAAA,QACtB,aAAe;AAAA,QACf,YAAc;AAAA,QACd,YAAc;AAAA,QACd,cAAgB;AAAA,QAChB,oBAAsB;AAAA,QACtB,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,aAAe;AAAA,QACf,oBAAsB;AAAA,QACtB,sBAAwB;AAAA,QACxB,UAAY;AAAA,QACZ,2BAA6B;AAAA,QAC7B,UAAY;AAAA,QACZ,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,mBAAqB;AAAA,QACrB,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,WAAa;AAAA,QACb,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,oBAAsB;AAAA,QACtB,iBAAmB;AAAA,QACnB,iBAAmB;AAAA,QACnB,mBAAqB;AAAA,QACrB,sBAAwB;AAAA,QACxB,UAAY;AAAA,QACZ,gBAAkB;AAAA,QAClB,WAAa;AAAA,QACb,SAAW;AAAA,QACX,MAAQ;AAAA,QACR,QAAU;AAAA,QACV,QAAU;AAAA,QACV,mBAAqB;AAAA,QACrB,QAAU;AAAA,QACV,oBAAsB;AAAA,QACtB,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,aAAe;AAAA,QACf,YAAc;AAAA,QACd,sBAAwB;AAAA,QACxB,iBAAmB;AAAA,QACnB,eAAiB;AAAA,QACjB,qBAAuB;AAAA,QACvB,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,QAAU;AAAA,QACV,oBAAsB;AAAA,QACtB,QAAU;AAAA,QACV,gBAAkB;AAAA,QAClB,gBAAkB;AAAA,QAClB,0BAA4B;AAAA,QAC5B,gBAAkB;AAAA,QAClB,2BAA6B;AAAA,QAC7B,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,aAAe;AAAA,QACf,0BAA4B;AAAA,QAC5B,kBAAoB;AAAA,QACpB,wBAA0B;AAAA,QAC1B,qCAAuC;AAAA,QACvC,2BAA6B;AAAA,QAC7B,iBAAmB;AAAA,QACnB,oBAAsB;AAAA,QACtB,uBAAyB;AAAA,QACzB,6BAA+B;AAAA,QAC/B,qBAAuB;AAAA,QACvB,8BAAgC;AAAA,QAChC,wBAA0B;AAAA,QAC1B,2BAA6B;AAAA,QAC7B,yBAA2B;AAAA,QAC3B,yBAA2B;AAAA,QAC3B,mBAAqB;AAAA,QACrB,qBAAuB;AAAA,QACvB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,YAAc;AAAA,QACd,aAAe;AAAA,QACf,uBAAyB;AAAA,QACzB,wBAA0B;AAAA,QAC1B,QAAU;AAAA,QACV,aAAe;AAAA,QACf,cAAgB;AAAA,QAChB,eAAiB;AAAA,QACjB,aAAe;AAAA,QACf,cAAgB;AAAA,QAChB,0BAA4B;AAAA,QAC5B,wBAA0B;AAAA,QAC1B,sCAAwC;AAAA,QACxC,kCAAoC;AAAA,QACpC,4BAA8B;AAAA,QAC9B,sBAAwB;AAAA,QACxB,qBAAuB;AAAA,QACvB,kBAAoB;AAAA,QACpB,gBAAkB;AAAA,QAClB,OAAS;AAAA,QACT,uBAAyB;AAAA,QACzB,UAAY;AAAA,QACZ,eAAiB;AAAA,QACjB,uBAAyB;AAAA,QACzB,QAAU;AAAA,QACV,mBAAqB;AAAA,QACrB,qBAAuB;AAAA,QACvB,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,yBAA2B;AAAA,QAC3B,iBAAmB;AAAA,QACnB,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,OAAS;AAAA,QACT,8BAAgC;AAAA,QAChC,gBAAkB;AAAA,QAClB,0BAA4B;AAAA,QAC5B,2BAA6B;AAAA,QAC7B,iCAAmC;AAAA,QACnC,6BAA+B;AAAA,QAC/B,mBAAqB;AAAA,QACrB,2BAA6B;AAAA,QAC7B,gBAAkB;AAAA,QAClB,qBAAuB;AAAA,QACvB,YAAc;AAAA,QACd,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,SAAW;AAAA,QACX,uBAAyB;AAAA,QACzB,qBAAuB;AAAA,QACvB,UAAY;AAAA,QACZ,gBAAkB;AAAA,QAClB,qBAAuB;AAAA,QACvB,oBAAsB;AAAA,QACtB,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,mBAAqB;AAAA,QACrB,gBAAkB;AAAA,QAClB,gBAAkB;AAAA,QAClB,qBAAuB;AAAA,QACvB,kBAAoB;AAAA,QACpB,eAAiB;AAAA,QACjB,wBAA0B;AAAA,QAC1B,sBAAwB;AAAA,QACxB,sBAAwB;AAAA,QACxB,UAAY;AAAA,QACZ,eAAiB;AAAA,QACjB,iBAAmB;AAAA,QACnB,iBAAmB;AAAA,QACnB,mBAAqB;AAAA,QACrB,gCAAkC;AAAA,QAClC,2BAA6B;AAAA,QAC7B,gBAAkB;AAAA,QAClB,uBAAyB;AAAA,QACzB,cAAgB;AAAA,QAChB,mBAAqB;AAAA,QACrB,kBAAoB;AAAA,QACpB,uBAAyB;AAAA,QACzB,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,YAAc;AAAA,QACd,WAAa;AAAA,QACb,WAAa;AAAA,QACb,YAAc;AAAA,QACd,QAAU;AAAA,QACV,QAAU;AAAA,QACV,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,SAAW;AAAA,QACX,SAAW;AAAA,QACX,qBAAuB;AAAA,QACvB,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,gBAAkB;AAAA,QAClB,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,QACX,8BAAgC;AAAA,QAChC,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,QAAU;AAAA,QACV,kBAAoB;AAAA,QACpB,QAAU;AAAA,QACV,YAAc;AAAA,QACd,eAAiB;AAAA,QACjB,wBAA0B;AAAA,QAC1B,2BAA6B;AAAA,QAC7B,gBAAkB;AAAA,QAClB,aAAe;AAAA,QACf,YAAc;AAAA,QACd,YAAc;AAAA,QACd,eAAiB;AAAA,QACjB,eAAiB;AAAA,QACjB,2BAA6B;AAAA,QAC7B,0BAA4B;AAAA,QAC5B,2BAA6B;AAAA,QAC7B,6BAA+B;AAAA,QAC/B,wBAA0B;AAAA,QAC1B,sBAAwB;AAAA,QACxB,cAAgB;AAAA,QAChB,qBAAuB;AAAA,QACvB,oBAAsB;AAAA,QACtB,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,kBAAoB;AAAA,QACpB,iBAAmB;AAAA,QACnB,iBAAmB;AAAA,QACnB,2BAA6B;AAAA,QAC7B,sBAAwB;AAAA,QACxB,0BAA4B;AAAA,QAC5B,sBAAwB;AAAA,QACxB,aAAe;AAAA,QACf,QAAU;AAAA,QACV,WAAa;AAAA,QACb,kBAAoB;AAAA,QACpB,MAAQ;AAAA,QACR,SAAW;AAAA,QACX,eAAiB;AAAA,QACjB,sBAAwB;AAAA,QACxB,cAAgB;AAAA,QAChB,gBAAkB;AAAA,QAClB,iBAAmB;AAAA,QACnB,YAAc;AAAA,QACd,kBAAoB;AAAA,QACpB,0BAA4B;AAAA,QAC5B,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,aAAe;AAAA,QACf,YAAc;AAAA,QACd,cAAgB;AAAA,QAChB,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,UAAY;AAAA,QACZ,kBAAoB;AAAA,QACpB,oBAAsB;AAAA,QACtB,wBAA0B;AAAA,QAC1B,oBAAsB;AAAA,QACtB,mBAAqB;AAAA,QACrB,uBAAyB;AAAA,QACzB,mBAAqB;AAAA,QACrB,uBAAyB;AAAA,QACzB,gCAAkC;AAAA,QAClC,iBAAmB;AAAA,QACnB,mBAAqB;AAAA,QACrB,0BAA4B;AAAA,QAC5B,mBAAqB;AAAA,QACrB,yBAA2B;AAAA,QAC3B,4BAA8B;AAAA,QAC9B,qBAAuB;AAAA,QACvB,kBAAoB;AAAA,QACpB,oBAAsB;AAAA,QACtB,qCAAuC;AAAA,QACvC,gBAAkB;AAAA,QAClB,gBAAkB;AAAA,QAClB,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,mBAAqB;AAAA,QACrB,yBAA2B;AAAA,QAC3B,+BAAiC;AAAA,QACjC,uBAAyB;AAAA,QACzB,4BAA8B;AAAA,QAC9B,6BAA+B;AAAA,QAC/B,6BAA+B;AAAA,QAC/B,0BAA4B;AAAA,QAC5B,wBAA0B;AAAA,QAC1B,mBAAqB;AAAA,QACrB,mBAAqB;AAAA,QACrB,mBAAqB;AAAA,QACrB,mBAAqB;AAAA,QACrB,mBAAqB;AAAA,QACrB,0BAA4B;AAAA,QAC5B,mBAAqB;AAAA,QACrB,mBAAqB;AAAA,QACrB,uBAAyB;AAAA,QACzB,wBAA0B;AAAA,QAC1B,oBAAsB;AAAA,QACtB,wBAA0B;AAAA,QAC1B,8BAAgC;AAAA,QAChC,uBAAyB;AAAA,QACzB,kBAAoB;AAAA,QACpB,wBAA0B;AAAA,QAC1B,kBAAoB;AAAA,QACpB,yBAA2B;AAAA,QAC3B,aAAe;AAAA,QACf,oBAAsB;AAAA,QACtB,oBAAsB;AAAA,QACtB,oBAAsB;AAAA,QACtB,iBAAmB;AAAA,QACnB,WAAa;AAAA,QACb,eAAiB;AAAA,QACjB,0BAA4B;AAAA,QAC5B,gBAAkB;AAAA,QAClB,kBAAoB;AAAA,QACpB,gBAAkB;AAAA,QAClB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,iBAAmB;AAAA,QACnB,WAAa;AAAA,QACb,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,mBAAqB;AAAA,QACrB,UAAY;AAAA,QACZ,cAAgB;AAAA,QAChB,mBAAqB;AAAA,QACrB,oBAAsB;AAAA,QACtB,wBAA0B;AAAA,QAC1B,0BAA4B;AAAA,QAC5B,SAAW;AAAA,QACX,gBAAkB;AAAA,QAClB,kBAAoB;AAAA,QACpB,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,iBAAmB;AAAA,QACnB,eAAiB;AAAA,QACjB,kBAAoB;AAAA,QACpB,kBAAoB;AAAA,QACpB,uBAAyB;AAAA,QACzB,gBAAkB;AAAA,QAClB,oBAAsB;AAAA,QACtB,2BAA6B;AAAA,QAC7B,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,kBAAoB;AAAA,QACpB,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,aAAe;AAAA,QACf,uBAAyB;AAAA,QACzB,gBAAkB;AAAA,QAClB,yBAA2B;AAAA,QAC3B,YAAc;AAAA,QACd,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,YAAc;AAAA,QACd,uBAAyB;AAAA,QACzB,aAAe;AAAA,QACf,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,kBAAoB;AAAA,QACpB,eAAiB;AAAA,QACjB,iBAAmB;AAAA,QACnB,WAAa;AAAA,QACb,YAAc;AAAA,QACd,aAAe;AAAA,QACf,SAAW;AAAA,QACX,KAAO;AAAA,QACP,OAAS;AAAA,QACT,YAAc;AAAA,QACd,WAAa;AAAA,QACb,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,kCAAoC;AAAA,QACpC,iBAAmB;AAAA,QACnB,YAAc;AAAA,QACd,aAAe;AAAA,QACf,eAAiB;AAAA,QACjB,kBAAoB;AAAA,QACpB,mBAAqB;AAAA,QACrB,0BAA4B;AAAA,QAC5B,cAAgB;AAAA,QAChB,SAAW;AAAA,QACX,KAAO;AAAA,QACP,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,KAAO;AAAA,QACP,uBAAyB;AAAA,QACzB,kBAAoB;AAAA,QACpB,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,aAAe;AAAA,QACf,cAAgB;AAAA,QAChB,qBAAuB;AAAA,QACvB,gCAAkC;AAAA,QAClC,gCAAkC;AAAA,QAClC,iCAAmC;AAAA,QACnC,iCAAmC;AAAA,QACnC,sBAAwB;AAAA,QACxB,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,sBAAwB;AAAA,QACxB,cAAgB;AAAA,QAChB,gBAAkB;AAAA,QAClB,uBAAyB;AAAA,QACzB,iBAAmB;AAAA,QACnB,oCAAsC;AAAA,QACtC,sBAAwB;AAAA,QACxB,gBAAkB;AAAA,QAClB,gBAAkB;AAAA,QAClB,QAAU;AAAA,QACV,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,kBAAoB;AAAA,QACpB,gBAAkB;AAAA,QAClB,aAAe;AAAA,QACf,wBAA0B;AAAA,QAC1B,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,kBAAoB;AAAA,QACpB,aAAe;AAAA,QACf,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,uBAAyB;AAAA,QACzB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,4BAA8B;AAAA,QAC9B,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,wBAA0B;AAAA,QAC1B,sBAAwB;AAAA,QACxB,wBAA0B;AAAA,QAC1B,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,iCAAmC;AAAA,QACnC,kCAAoC;AAAA,QACpC,mBAAqB;AAAA,QACrB,2BAA6B;AAAA,QAC7B,wBAA0B;AAAA,QAC1B,sBAAwB;AAAA,QACxB,YAAc;AAAA,QACd,MAAQ;AAAA,QACR,QAAU;AAAA,QACV,QAAU;AAAA,QACV,uBAAyB;AAAA,QACzB,0CAA4C;AAAA,QAC5C,QAAU;AAAA,QACV,SAAW;AAAA,QACX,oBAAsB;AAAA,QACtB,gBAAkB;AAAA,QAClB,iCAAmC;AAAA,QACnC,6BAA+B;AAAA,QAC/B,aAAe;AAAA,QACf,gBAAkB;AAAA,QAClB,2BAA6B;AAAA,QAC7B,sBAAwB;AAAA,QACxB,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,UAAY;AAAA,QACZ,aAAe;AAAA,QACf,yBAA2B;AAAA,QAC3B,UAAY;AAAA,QACZ,uBAAyB;AAAA,QACzB,oBAAsB;AAAA,QACtB,mBAAqB;AAAA,QACrB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,iBAAmB;AAAA,QACnB,iBAAmB;AAAA,QACnB,eAAiB;AAAA,QACjB,oBAAsB;AAAA,QACtB,oBAAsB;AAAA,QACtB,2BAA6B;AAAA,QAC7B,aAAe;AAAA,QACf,cAAgB;AAAA,QAChB,SAAW;AAAA,QACX,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,QAAU;AAAA,QACV,OAAS;AAAA,QACT,kBAAoB;AAAA,QACpB,uBAAyB;AAAA,QACzB,eAAiB;AAAA,QACjB,kBAAoB;AAAA,QACpB,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,+BAAiC;AAAA,QACjC,+BAAiC;AAAA,QACjC,QAAU;AAAA,QACV,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,yBAA2B;AAAA,QAC3B,cAAgB;AAAA,QAChB,eAAiB;AAAA,MAClB;AAAA,MACA,SAAW;AAAA,QACV,gBAAkB;AAAA,QAClB,OAAS;AAAA,QACT,aAAe;AAAA,QACf,SAAW;AAAA,QACX,QAAU;AAAA,QACV,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,sBAAwB;AAAA,QACxB,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,YAAc;AAAA,QACd,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,OAAS;AAAA,QACT,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,QACP,mBAAqB;AAAA,QACrB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,MAAQ;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,QAAU;AAAA,MACX;AAAA,MACA,UAAY;AAAA,QACX,SAAW;AAAA,QACX,QAAU;AAAA,QACV,QAAU;AAAA,QACV,SAAW;AAAA,MACZ;AAAA,MACA,OAAS;AAAA,QACR,MAAQ;AAAA,QACR,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,QACP,QAAU;AAAA,QACV,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,QACX,MAAQ;AAAA,QACR,OAAS;AAAA,QACT,KAAO;AAAA,MACR;AAAA,MACA,UAAY;AAAA,QACX,GAAK;AAAA,QACL,IAAM;AAAA,QACN,IAAM;AAAA,QACN,IAAM;AAAA,QACN,IAAM;AAAA,QACN,IAAM;AAAA,QACN,IAAM;AAAA,QACN,IAAM;AAAA,QACN,IAAM;AAAA,QACN,QAAU;AAAA,QACV,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,OAAS;AAAA,QACT,KAAO;AAAA,QACP,QAAU;AAAA,QACV,mBAAqB;AAAA,QACrB,SAAW;AAAA,QACX,MAAQ;AAAA,QACR,SAAW;AAAA,QACX,eAAiB;AAAA,QACjB,SAAW;AAAA,QACX,YAAc;AAAA,QACd,cAAgB;AAAA,QAChB,OAAS;AAAA,QACT,SAAW;AAAA,QACX,WAAa;AAAA,QACb,iBAAmB;AAAA,QACnB,QAAU;AAAA,MACX;AAAA,MACA,WAAa;AAAA,QACZ,SAAW;AAAA,QACX,OAAS;AAAA,QACT,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,YAAc;AAAA,QACd,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,SAAW;AAAA,QACX,gBAAkB;AAAA,QAClB,UAAY;AAAA,QACZ,WAAa;AAAA,QACb,YAAc;AAAA,QACd,cAAgB;AAAA,QAChB,OAAS;AAAA,QACT,MAAQ;AAAA,MACT;AAAA,MACA,QAAU;AAAA,QACT,OAAS;AAAA,QACT,aAAe;AAAA,QACf,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,YAAc;AAAA,QACd,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,OAAS;AAAA,QACT,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,QAAU;AAAA,QACT,OAAS;AAAA,QACT,aAAe;AAAA,QACf,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,YAAc;AAAA,QACd,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,OAAS;AAAA,QACT,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,QAAU;AAAA,QACT,OAAS;AAAA,QACT,aAAe;AAAA,QACf,SAAW;AAAA,QACX,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,YAAc;AAAA,QACd,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,OAAS;AAAA,QACT,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,QACP,mBAAqB;AAAA,QACrB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,QAAU;AAAA,QACT,OAAS;AAAA,QACT,aAAe;AAAA,QACf,SAAW;AAAA,QACX,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,YAAc;AAAA,QACd,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,OAAS;AAAA,QACT,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,QACP,mBAAqB;AAAA,QACrB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,QAAU;AAAA,QACT,OAAS;AAAA,QACT,aAAe;AAAA,QACf,SAAW;AAAA,QACX,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,YAAc;AAAA,QACd,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,OAAS;AAAA,QACT,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,QACP,mBAAqB;AAAA,QACrB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,QAAU;AAAA,QACT,OAAS;AAAA,QACT,aAAe;AAAA,QACf,SAAW;AAAA,QACX,QAAU;AAAA,QACV,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,YAAc;AAAA,QACd,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,OAAS;AAAA,QACT,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,QACP,mBAAqB;AAAA,QACrB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,QAAU;AAAA,QACT,gBAAkB;AAAA,QAClB,OAAS;AAAA,QACT,aAAe;AAAA,QACf,SAAW;AAAA,QACX,QAAU;AAAA,QACV,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,sBAAwB;AAAA,QACxB,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,YAAc;AAAA,QACd,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,OAAS;AAAA,QACT,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,QACP,mBAAqB;AAAA,QACrB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,QAAU;AAAA,QACT,gBAAkB;AAAA,QAClB,OAAS;AAAA,QACT,aAAe;AAAA,QACf,SAAW;AAAA,QACX,QAAU;AAAA,QACV,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,sBAAwB;AAAA,QACxB,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,YAAc;AAAA,QACd,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,OAAS;AAAA,QACT,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,QACP,mBAAqB;AAAA,QACrB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,QAAU;AAAA,QACT,gBAAkB;AAAA,QAClB,OAAS;AAAA,QACT,aAAe;AAAA,QACf,SAAW;AAAA,QACX,QAAU;AAAA,QACV,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,sBAAwB;AAAA,QACxB,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,YAAc;AAAA,QACd,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,OAAS;AAAA,QACT,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,QACP,mBAAqB;AAAA,QACrB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,QAAU;AAAA,QACT,gBAAkB;AAAA,QAClB,OAAS;AAAA,QACT,aAAe;AAAA,QACf,SAAW;AAAA,QACX,QAAU;AAAA,QACV,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,sBAAwB;AAAA,QACxB,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,YAAc;AAAA,QACd,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,OAAS;AAAA,QACT,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,QACP,mBAAqB;AAAA,QACrB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,QAAU;AAAA,QACT,gBAAkB;AAAA,QAClB,OAAS;AAAA,QACT,aAAe;AAAA,QACf,SAAW;AAAA,QACX,QAAU;AAAA,QACV,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,sBAAwB;AAAA,QACxB,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,YAAc;AAAA,QACd,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,OAAS;AAAA,QACT,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,QACP,mBAAqB;AAAA,QACrB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,KAAO;AAAA,QACN,OAAS;AAAA,QACT,SAAW;AAAA,QACX,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,MACb;AAAA,MACA,KAAO;AAAA,QACN,OAAS;AAAA,QACT,SAAW;AAAA,QACX,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,OAAS;AAAA,QACT,QAAU;AAAA,QACV,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,QAAU;AAAA,QACV,QAAU;AAAA,QACV,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,WAAa;AAAA,QACb,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,MACb;AAAA,MACA,cAAgB;AAAA,QACf,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,gBAAkB;AAAA,QAClB,IAAM;AAAA,QACN,eAAiB;AAAA,QACjB,aAAe;AAAA,QACf,2BAA6B;AAAA,QAC7B,gBAAkB;AAAA,QAClB,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,oBAAsB;AAAA,QACtB,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,YAAc;AAAA,QACd,aAAe;AAAA,QACf,cAAgB;AAAA,QAChB,SAAW;AAAA,QACX,eAAiB;AAAA,QACjB,QAAU;AAAA,QACV,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,wBAA0B;AAAA,QAC1B,8BAAgC;AAAA,QAChC,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,cAAgB;AAAA,QAChB,0BAA4B;AAAA,QAC5B,mBAAqB;AAAA,QACrB,cAAgB;AAAA,MACjB;AAAA,MACA,SAAW;AAAA,QACV,UAAY;AAAA,QACZ,WAAa;AAAA,QACb,WAAa;AAAA,QACb,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,QAAU;AAAA,QACV,aAAe;AAAA,QACf,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,KAAO;AAAA,QACP,IAAM;AAAA,QACN,SAAW;AAAA,QACX,SAAW;AAAA,QACX,MAAQ;AAAA,QACR,OAAS;AAAA,QACT,mBAAqB;AAAA,QACrB,eAAiB;AAAA,QACjB,OAAS;AAAA,QACT,UAAY;AAAA,QACZ,WAAa;AAAA,QACb,KAAO;AAAA,MACR;AAAA,MACA,MAAQ;AAAA,QACP,UAAY;AAAA,QACZ,WAAa;AAAA,QACb,WAAa;AAAA,QACb,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,QAAU;AAAA,QACV,KAAO;AAAA,QACP,IAAM;AAAA,QACN,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,KAAO;AAAA,QACP,OAAS;AAAA,MACV;AAAA,MACA,QAAU;AAAA,QACT,GAAK;AAAA,QACL,QAAU;AAAA,MACX;AAAA,MACA,QAAU;AAAA,QACT,GAAK;AAAA,QACL,UAAY;AAAA,QACZ,gBAAkB;AAAA,QAClB,gBAAkB;AAAA,QAClB,gBAAkB;AAAA,QAClB,KAAO;AAAA,QACP,QAAU;AAAA,QACV,OAAS;AAAA,QACT,OAAS;AAAA,QACT,SAAW;AAAA,QACX,KAAO;AAAA,QACP,gBAAkB;AAAA,QAClB,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,OAAS;AAAA,QACT,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,OAAS;AAAA,QACT,QAAU;AAAA,QACV,OAAS;AAAA,QACT,gBAAkB;AAAA,QAClB,KAAO;AAAA,QACP,SAAW;AAAA,QACX,QAAU;AAAA,QACV,SAAW;AAAA,QACX,QAAU;AAAA,QACV,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,QAAU;AAAA,QACV,sBAAwB;AAAA,QACxB,SAAW;AAAA,QACX,OAAS;AAAA,QACT,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,IAAM;AAAA,QACN,OAAS;AAAA,QACT,QAAU;AAAA,QACV,iBAAmB;AAAA,MACpB;AAAA,MACA,OAAS;AAAA,QACR,OAAS;AAAA,QACT,WAAa;AAAA,QACb,QAAU;AAAA,QACV,YAAc;AAAA,QACd,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,IAAM;AAAA,QACN,OAAS;AAAA,QACT,KAAO;AAAA,QACP,OAAS;AAAA,QACT,SAAW;AAAA,QACX,OAAS;AAAA,QACT,YAAc;AAAA,QACd,eAAiB;AAAA,QACjB,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,WAAa;AAAA,QACb,KAAO;AAAA,QACP,UAAY;AAAA,MACb;AAAA,MACA,OAAS;AAAA,QACR,YAAc;AAAA,QACd,OAAS;AAAA,QACT,iBAAmB;AAAA,QACnB,KAAO;AAAA,QACP,IAAM;AAAA,QACN,SAAW;AAAA,QACX,IAAM;AAAA,QACN,aAAe;AAAA,QACf,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,IAAM;AAAA,QACN,YAAc;AAAA,QACd,OAAS;AAAA,QACT,OAAS;AAAA,QACT,WAAa;AAAA,QACb,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,WAAa;AAAA,QACb,OAAS;AAAA,QACT,WAAa;AAAA,QACb,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,YAAc;AAAA,QACd,IAAM;AAAA,QACN,IAAM;AAAA,QACN,MAAQ;AAAA,QACR,SAAW;AAAA,QACX,aAAe;AAAA,MAChB;AAAA,MACA,SAAW;AAAA,QACV,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,KAAO;AAAA,QACP,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,QAAU;AAAA,QACV,cAAgB;AAAA,QAChB,OAAS;AAAA,QACT,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,mBAAqB;AAAA,QACrB,KAAO;AAAA,QACP,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,MACT;AAAA,MACA,MAAQ;AAAA,QACP,WAAa;AAAA,QACb,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,sBAAwB;AAAA,QACxB,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,kBAAoB;AAAA,QACpB,MAAQ;AAAA,QACR,QAAU;AAAA,QACV,2BAA6B;AAAA,QAC7B,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,SAAW;AAAA,QACX,sBAAwB;AAAA,QACxB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,WAAa;AAAA,QACb,aAAe;AAAA,QACf,qBAAuB;AAAA,QACvB,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,OAAS;AAAA,QACT,aAAe;AAAA,QACf,SAAW;AAAA,QACX,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,QAAU;AAAA,QACV,SAAW;AAAA,QACX,gBAAkB;AAAA,QAClB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,QAAU;AAAA,QACV,WAAa;AAAA,QACb,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,iBAAmB;AAAA,QACnB,oBAAsB;AAAA,QACtB,qBAAuB;AAAA,QACvB,8BAAgC;AAAA,QAChC,2BAA6B;AAAA,QAC7B,SAAW;AAAA,QACX,gBAAkB;AAAA,QAClB,8BAAgC;AAAA,QAChC,gBAAkB;AAAA,QAClB,0BAA4B;AAAA,QAC5B,2BAA6B;AAAA,QAC7B,iCAAmC;AAAA,QACnC,6BAA+B;AAAA,QAC/B,SAAW;AAAA,QACX,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,iBAAmB;AAAA,QACnB,kCAAoC;AAAA,QACpC,KAAO;AAAA,QACP,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,iCAAmC;AAAA,QACnC,6BAA+B;AAAA,MAChC;AAAA,MACA,aAAe;AAAA,QACd,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,sBAAwB;AAAA,QACxB,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,kBAAoB;AAAA,QACpB,MAAQ;AAAA,QACR,QAAU;AAAA,QACV,2BAA6B;AAAA,QAC7B,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,SAAW;AAAA,QACX,sBAAwB;AAAA,QACxB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,WAAa;AAAA,QACb,aAAe;AAAA,QACf,qBAAuB;AAAA,QACvB,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,OAAS;AAAA,QACT,aAAe;AAAA,QACf,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,QAAU;AAAA,QACV,SAAW;AAAA,QACX,gBAAkB;AAAA,QAClB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,WAAa;AAAA,QACb,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,iBAAmB;AAAA,QACnB,oBAAsB;AAAA,QACtB,qBAAuB;AAAA,QACvB,8BAAgC;AAAA,QAChC,2BAA6B;AAAA,QAC7B,SAAW;AAAA,QACX,gBAAkB;AAAA,QAClB,8BAAgC;AAAA,QAChC,gBAAkB;AAAA,QAClB,0BAA4B;AAAA,QAC5B,2BAA6B;AAAA,QAC7B,iCAAmC;AAAA,QACnC,6BAA+B;AAAA,QAC/B,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,iBAAmB;AAAA,QACnB,kCAAoC;AAAA,QACpC,KAAO;AAAA,QACP,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,iCAAmC;AAAA,QACnC,6BAA+B;AAAA,MAChC;AAAA,MACA,WAAa;AAAA,QACZ,SAAW;AAAA,QACX,SAAW;AAAA,QACX,SAAW;AAAA,QACX,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,aAAe;AAAA,QACd,GAAK;AAAA,QACL,IAAM;AAAA,QACN,IAAM;AAAA,QACN,QAAU;AAAA,QACV,WAAa;AAAA,QACb,IAAM;AAAA,QACN,IAAM;AAAA,QACN,IAAM;AAAA,QACN,IAAM;AAAA,QACN,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,eAAiB;AAAA,QACjB,SAAW;AAAA,QACX,OAAS;AAAA,QACT,SAAW;AAAA,QACX,WAAa;AAAA,QACb,YAAc;AAAA,QACd,YAAc;AAAA,QACd,QAAU;AAAA,QACV,SAAW;AAAA,QACX,YAAc;AAAA,QACd,OAAS;AAAA,QACT,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,aAAe;AAAA,QACf,oBAAsB;AAAA,QACtB,UAAY;AAAA,QACZ,WAAa;AAAA,QACb,eAAiB;AAAA,QACjB,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,kBAAoB;AAAA,QACpB,OAAS;AAAA,QACT,UAAY;AAAA,QACZ,QAAU;AAAA,QACV,KAAO;AAAA,MACR;AAAA,MACA,YAAc;AAAA,QACb,GAAK;AAAA,QACL,IAAM;AAAA,QACN,SAAW;AAAA,QACX,IAAM;AAAA,QACN,IAAM;AAAA,QACN,YAAc;AAAA,QACd,SAAW;AAAA,QACX,YAAc;AAAA,MACf;AAAA,MACA,OAAS;AAAA,QACR,WAAa;AAAA,QACb,WAAa;AAAA,QACb,OAAS;AAAA,QACT,QAAU;AAAA,QACV,QAAU;AAAA,QACV,cAAgB;AAAA,QAChB,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,cAAgB;AAAA,QAChB,gBAAkB;AAAA,QAClB,IAAM;AAAA,QACN,WAAa;AAAA,QACb,OAAS;AAAA,QACT,QAAU;AAAA,QACV,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,aAAe;AAAA,QACf,MAAQ;AAAA,QACR,QAAU;AAAA,MACX;AAAA,MACA,OAAS;AAAA,QACR,aAAe;AAAA,QACf,aAAe;AAAA,QACf,IAAM;AAAA,QACN,MAAQ;AAAA,QACR,aAAe;AAAA,QACf,eAAiB;AAAA,QACjB,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,YAAc;AAAA,QACd,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,SAAW;AAAA,QACX,SAAW;AAAA,MACZ;AAAA,MACA,eAAiB;AAAA,QAChB,iBAAmB;AAAA,QACnB,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,IAAM;AAAA,QACN,IAAM;AAAA,QACN,iBAAmB;AAAA,QACnB,sBAAwB;AAAA,QACxB,MAAQ;AAAA,QACR,sBAAwB;AAAA,QACxB,wBAA0B;AAAA,QAC1B,uBAAyB;AAAA,QACzB,6BAA+B;AAAA,QAC/B,8BAAgC;AAAA,QAChC,iBAAmB;AAAA,QACnB,MAAQ;AAAA,QACR,kBAAoB;AAAA,QACpB,MAAQ;AAAA,QACR,2BAA6B;AAAA,QAC7B,OAAS;AAAA,QACT,QAAU;AAAA,QACV,cAAgB;AAAA,QAChB,qBAAuB;AAAA,QACvB,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,QAAU;AAAA,QACV,SAAW;AAAA,QACX,SAAW;AAAA,QACX,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,SAAW;AAAA,QACX,aAAe;AAAA,QACf,aAAe;AAAA,QACf,oBAAsB;AAAA,QACtB,sBAAwB;AAAA,QACxB,mBAAqB;AAAA,QACrB,YAAc;AAAA,QACd,qBAAuB;AAAA,QACvB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,aAAe;AAAA,QACf,qBAAuB;AAAA,QACvB,eAAiB;AAAA,QACjB,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,WAAa;AAAA,QACb,mBAAqB;AAAA,QACrB,UAAY;AAAA,QACZ,kBAAoB;AAAA,QACpB,SAAW;AAAA,QACX,SAAW;AAAA,QACX,iBAAmB;AAAA,QACnB,eAAiB;AAAA,QACjB,YAAc;AAAA,QACd,OAAS;AAAA,QACT,aAAe;AAAA,QACf,aAAe;AAAA,QACf,6BAA+B;AAAA,QAC/B,iBAAmB;AAAA,QACnB,wBAA0B;AAAA,QAC1B,OAAS;AAAA,QACT,YAAc;AAAA,QACd,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,2BAA6B;AAAA,QAC7B,sBAAwB;AAAA,QACxB,kBAAoB;AAAA,QACpB,8BAAgC;AAAA,QAChC,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,UAAY;AAAA,QACZ,KAAO;AAAA,QACP,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,cAAgB;AAAA,QAChB,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,kBAAoB;AAAA,QACpB,eAAiB;AAAA,QACjB,kBAAoB;AAAA,QACpB,mBAAqB;AAAA,QACrB,oBAAsB;AAAA,QACtB,uBAAyB;AAAA,QACzB,uBAAyB;AAAA,QACzB,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,mBAAqB;AAAA,QACrB,UAAY;AAAA,QACZ,oBAAsB;AAAA,QACtB,kBAAoB;AAAA,QACpB,YAAc;AAAA,QACd,qBAAuB;AAAA,QACvB,kBAAoB;AAAA,QACpB,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,UAAY;AAAA,QACZ,iBAAmB;AAAA,QACnB,wBAA0B;AAAA,QAC1B,sBAAwB;AAAA,QACxB,mBAAqB;AAAA,QACrB,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,gBAAkB;AAAA,QAClB,sBAAwB;AAAA,QACxB,oBAAsB;AAAA,QACtB,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,gBAAkB;AAAA,QAClB,yBAA2B;AAAA,QAC3B,oBAAsB;AAAA,QACtB,SAAW;AAAA,QACX,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,aAAe;AAAA,QACf,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,aAAe;AAAA,QACf,gBAAkB;AAAA,QAClB,kBAAoB;AAAA,QACpB,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,uBAAyB;AAAA,QACzB,aAAe;AAAA,QACf,6BAA+B;AAAA,QAC/B,WAAa;AAAA,QACb,eAAiB;AAAA,QACjB,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,iBAAmB;AAAA,QACnB,kBAAoB;AAAA,QACpB,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,gBAAkB;AAAA,QAClB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,0BAA4B;AAAA,QAC5B,WAAa;AAAA,QACb,iBAAmB;AAAA,QACnB,oBAAsB;AAAA,QACtB,cAAgB;AAAA,QAChB,mBAAqB;AAAA,QACrB,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,mCAAqC;AAAA,QACrC,gBAAkB;AAAA,QAClB,YAAc;AAAA,QACd,wBAA0B;AAAA,QAC1B,wBAA0B;AAAA,QAC1B,uBAAyB;AAAA,QACzB,0BAA4B;AAAA,QAC5B,kBAAoB;AAAA,QACpB,gBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,SAAW;AAAA,QACX,WAAa;AAAA,QACb,kBAAoB;AAAA,QACpB,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,qBAAuB;AAAA,QACvB,qBAAuB;AAAA,QACvB,kBAAoB;AAAA,QACpB,gBAAkB;AAAA,QAClB,QAAU;AAAA,QACV,0BAA4B;AAAA,QAC5B,oBAAsB;AAAA,QACtB,QAAU;AAAA,QACV,sBAAwB;AAAA,QACxB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,qBAAuB;AAAA,QACvB,aAAe;AAAA,QACf,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,iBAAmB;AAAA,QACnB,oBAAsB;AAAA,QACtB,qBAAuB;AAAA,QACvB,8BAAgC;AAAA,QAChC,2BAA6B;AAAA,QAC7B,yBAA2B;AAAA,QAC3B,mBAAqB;AAAA,QACrB,qBAAuB;AAAA,QACvB,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,uBAAyB;AAAA,QACzB,WAAa;AAAA,QACb,aAAe;AAAA,QACf,iBAAmB;AAAA,QACnB,kBAAoB;AAAA,QACpB,yBAA2B;AAAA,QAC3B,gBAAkB;AAAA,QAClB,8BAAgC;AAAA,QAChC,gBAAkB;AAAA,QAClB,0BAA4B;AAAA,QAC5B,2BAA6B;AAAA,QAC7B,iCAAmC;AAAA,QACnC,6BAA+B;AAAA,QAC/B,cAAgB;AAAA,QAChB,qBAAuB;AAAA,QACvB,YAAc;AAAA,QACd,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,mBAAqB;AAAA,QACrB,WAAa;AAAA,QACb,WAAa;AAAA,QACb,8BAAgC;AAAA,QAChC,MAAQ;AAAA,QACR,eAAiB;AAAA,QACjB,eAAiB;AAAA,QACjB,0BAA4B;AAAA,QAC5B,2BAA6B;AAAA,QAC7B,aAAe;AAAA,QACf,YAAc;AAAA,QACd,aAAe;AAAA,QACf,eAAiB;AAAA,QACjB,sBAAwB;AAAA,QACxB,gBAAkB;AAAA,QAClB,iBAAmB;AAAA,QACnB,YAAc;AAAA,QACd,cAAgB;AAAA,QAChB,iBAAmB;AAAA,QACnB,WAAa;AAAA,QACb,aAAe;AAAA,QACf,gBAAkB;AAAA,QAClB,yBAA2B;AAAA,QAC3B,YAAc;AAAA,QACd,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,iBAAmB;AAAA,QACnB,kCAAoC;AAAA,QACpC,aAAe;AAAA,QACf,eAAiB;AAAA,QACjB,kBAAoB;AAAA,QACpB,mBAAqB;AAAA,QACrB,0BAA4B;AAAA,QAC5B,cAAgB;AAAA,QAChB,KAAO;AAAA,QACP,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,gBAAkB;AAAA,QAClB,aAAe;AAAA,QACf,wBAA0B;AAAA,QAC1B,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,kBAAoB;AAAA,QACpB,aAAe;AAAA,QACf,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,uBAAyB;AAAA,QACzB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,4BAA8B;AAAA,QAC9B,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,wBAA0B;AAAA,QAC1B,sBAAwB;AAAA,QACxB,wBAA0B;AAAA,QAC1B,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,iCAAmC;AAAA,QACnC,kCAAoC;AAAA,QACpC,mBAAqB;AAAA,QACrB,sBAAwB;AAAA,QACxB,MAAQ;AAAA,QACR,cAAgB;AAAA,QAChB,mBAAqB;AAAA,QACrB,gBAAkB;AAAA,QAClB,iBAAmB;AAAA,QACnB,gBAAkB;AAAA,QAClB,iCAAmC;AAAA,QACnC,6BAA+B;AAAA,MAChC;AAAA,MACA,uBAAuB;AAAA,QACtB,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,sBAAwB;AAAA,QACxB,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,kBAAoB;AAAA,QACpB,MAAQ;AAAA,QACR,2BAA6B;AAAA,QAC7B,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,SAAW;AAAA,QACX,sBAAwB;AAAA,QACxB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,WAAa;AAAA,QACb,aAAe;AAAA,QACf,qBAAuB;AAAA,QACvB,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,OAAS;AAAA,QACT,aAAe;AAAA,QACf,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,SAAW;AAAA,QACX,gBAAkB;AAAA,QAClB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,WAAa;AAAA,QACb,WAAa;AAAA,QACb,aAAe;AAAA,QACf,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,iBAAmB;AAAA,QACnB,oBAAsB;AAAA,QACtB,qBAAuB;AAAA,QACvB,8BAAgC;AAAA,QAChC,2BAA6B;AAAA,QAC7B,gBAAkB;AAAA,QAClB,8BAAgC;AAAA,QAChC,gBAAkB;AAAA,QAClB,0BAA4B;AAAA,QAC5B,2BAA6B;AAAA,QAC7B,iCAAmC;AAAA,QACnC,6BAA+B;AAAA,QAC/B,SAAW;AAAA,QACX,UAAY;AAAA,QACZ,aAAe;AAAA,QACf,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,iBAAmB;AAAA,QACnB,kCAAoC;AAAA,QACpC,KAAO;AAAA,QACP,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,iCAAmC;AAAA,QACnC,6BAA+B;AAAA,MAChC;AAAA,MACA,SAAW;AAAA,QACV,KAAO;AAAA,QACP,IAAM;AAAA,QACN,OAAS;AAAA,QACT,KAAO;AAAA,QACP,QAAU;AAAA,QACV,IAAM;AAAA,QACN,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,OAAS;AAAA,QACT,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,IAAM;AAAA,QACN,IAAM;AAAA,QACN,OAAS;AAAA,QACT,IAAM;AAAA,QACN,MAAQ;AAAA,QACR,OAAS;AAAA,QACT,KAAO;AAAA,QACP,IAAM;AAAA,QACN,KAAO;AAAA,QACP,KAAO;AAAA,QACP,aAAe;AAAA,QACf,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,SAAW;AAAA,QACX,MAAQ;AAAA,QACR,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,OAAS;AAAA,MACV;AAAA,MACA,QAAU;AAAA,QACT,UAAY;AAAA,QACZ,WAAa;AAAA,QACb,QAAU;AAAA,QACV,YAAc;AAAA,QACd,WAAa;AAAA,QACb,YAAc;AAAA,QACd,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,QAAU;AAAA,QACV,cAAgB;AAAA,QAChB,IAAM;AAAA,QACN,cAAgB;AAAA,QAChB,gBAAkB;AAAA,QAClB,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,IAAM;AAAA,QACN,QAAU;AAAA,MACX;AAAA,MACA,eAAiB;AAAA,QAChB,SAAW;AAAA,QACX,QAAU;AAAA,QACV,KAAO;AAAA,MACR;AAAA,MACA,QAAU;AAAA,QACT,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,IAAM;AAAA,QACN,IAAM;AAAA,QACN,iBAAmB;AAAA,QACnB,sBAAwB;AAAA,QACxB,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,wBAA0B;AAAA,QAC1B,uBAAyB;AAAA,QACzB,6BAA+B;AAAA,QAC/B,iBAAmB;AAAA,QACnB,MAAQ;AAAA,QACR,kBAAoB;AAAA,QACpB,MAAQ;AAAA,QACR,2BAA6B;AAAA,QAC7B,OAAS;AAAA,QACT,QAAU;AAAA,QACV,cAAgB;AAAA,QAChB,sBAAwB;AAAA,QACxB,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,OAAS;AAAA,QACT,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,SAAW;AAAA,QACX,sBAAwB;AAAA,QACxB,mBAAqB;AAAA,QACrB,YAAc;AAAA,QACd,qBAAuB;AAAA,QACvB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,WAAa;AAAA,QACb,UAAY;AAAA,QACZ,UAAY;AAAA,QACZ,aAAe;AAAA,QACf,qBAAuB;AAAA,QACvB,4BAA8B;AAAA,QAC9B,eAAiB;AAAA,QACjB,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,WAAa;AAAA,QACb,mBAAqB;AAAA,QACrB,UAAY;AAAA,QACZ,kBAAoB;AAAA,QACpB,SAAW;AAAA,QACX,SAAW;AAAA,QACX,iBAAmB;AAAA,QACnB,eAAiB;AAAA,QACjB,mBAAqB;AAAA,QACrB,mBAAqB;AAAA,QACrB,YAAc;AAAA,QACd,OAAS;AAAA,QACT,aAAe;AAAA,QACf,aAAe;AAAA,QACf,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,UAAY;AAAA,QACZ,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,2BAA6B;AAAA,QAC7B,sBAAwB;AAAA,QACxB,kBAAoB;AAAA,QACpB,oBAAsB;AAAA,QACtB,4BAA8B;AAAA,QAC9B,8BAAgC;AAAA,QAChC,UAAY;AAAA,QACZ,OAAS;AAAA,QACT,UAAY;AAAA,QACZ,KAAO;AAAA,QACP,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,cAAgB;AAAA,QAChB,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,kBAAoB;AAAA,QACpB,eAAiB;AAAA,QACjB,kBAAoB;AAAA,QACpB,mBAAqB;AAAA,QACrB,oBAAsB;AAAA,QACtB,uBAAyB;AAAA,QACzB,uBAAyB;AAAA,QACzB,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,mBAAqB;AAAA,QACrB,UAAY;AAAA,QACZ,oBAAsB;AAAA,QACtB,kBAAoB;AAAA,QACpB,YAAc;AAAA,QACd,qBAAuB;AAAA,QACvB,kBAAoB;AAAA,QACpB,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,UAAY;AAAA,QACZ,iBAAmB;AAAA,QACnB,wBAA0B;AAAA,QAC1B,sBAAwB;AAAA,QACxB,mBAAqB;AAAA,QACrB,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,gBAAkB;AAAA,QAClB,sBAAwB;AAAA,QACxB,oBAAsB;AAAA,QACtB,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,gBAAkB;AAAA,QAClB,yBAA2B;AAAA,QAC3B,oBAAsB;AAAA,QACtB,SAAW;AAAA,QACX,KAAO;AAAA,QACP,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,qBAAuB;AAAA,QACvB,WAAa;AAAA,QACb,oBAAsB;AAAA,QACtB,aAAe;AAAA,QACf,YAAc;AAAA,QACd,UAAY;AAAA,QACZ,aAAe;AAAA,QACf,gBAAkB;AAAA,QAClB,kBAAoB;AAAA,QACpB,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,uBAAyB;AAAA,QACzB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,6BAA+B;AAAA,QAC/B,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,WAAa;AAAA,QACb,iBAAmB;AAAA,QACnB,kBAAoB;AAAA,QACpB,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,gBAAkB;AAAA,QAClB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,MAAQ;AAAA,QACR,0BAA4B;AAAA,QAC5B,WAAa;AAAA,QACb,iBAAmB;AAAA,QACnB,oBAAsB;AAAA,QACtB,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,mCAAqC;AAAA,QACrC,SAAW;AAAA,QACX,kBAAoB;AAAA,QACpB,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,oBAAsB;AAAA,QACtB,sBAAwB;AAAA,QACxB,QAAU;AAAA,QACV,QAAU;AAAA,QACV,aAAe;AAAA,QACf,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,iBAAmB;AAAA,QACnB,oBAAsB;AAAA,QACtB,qBAAuB;AAAA,QACvB,8BAAgC;AAAA,QAChC,2BAA6B;AAAA,QAC7B,yBAA2B;AAAA,QAC3B,qBAAuB;AAAA,QACvB,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,YAAc;AAAA,QACd,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,gBAAkB;AAAA,QAClB,eAAiB;AAAA,QACjB,uBAAyB;AAAA,QACzB,aAAe;AAAA,QACf,kBAAoB;AAAA,QACpB,yBAA2B;AAAA,QAC3B,gBAAkB;AAAA,QAClB,8BAAgC;AAAA,QAChC,gBAAkB;AAAA,QAClB,0BAA4B;AAAA,QAC5B,2BAA6B;AAAA,QAC7B,iCAAmC;AAAA,QACnC,6BAA+B;AAAA,QAC/B,qBAAuB;AAAA,QACvB,YAAc;AAAA,QACd,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,SAAW;AAAA,QACX,uBAAyB;AAAA,QACzB,UAAY;AAAA,QACZ,mBAAqB;AAAA,QACrB,gBAAkB;AAAA,QAClB,sBAAwB;AAAA,QACxB,sBAAwB;AAAA,QACxB,WAAa;AAAA,QACb,WAAa;AAAA,QACb,8BAAgC;AAAA,QAChC,MAAQ;AAAA,QACR,QAAU;AAAA,QACV,YAAc;AAAA,QACd,2BAA6B;AAAA,QAC7B,aAAe;AAAA,QACf,YAAc;AAAA,QACd,cAAgB;AAAA,QAChB,kBAAoB;AAAA,QACpB,eAAiB;AAAA,QACjB,sBAAwB;AAAA,QACxB,gBAAkB;AAAA,QAClB,iBAAmB;AAAA,QACnB,YAAc;AAAA,QACd,cAAgB;AAAA,QAChB,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,gBAAkB;AAAA,QAClB,yBAA2B;AAAA,QAC3B,YAAc;AAAA,QACd,WAAa;AAAA,QACb,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,aAAe;AAAA,QACf,iBAAmB;AAAA,QACnB,kCAAoC;AAAA,QACpC,aAAe;AAAA,QACf,eAAiB;AAAA,QACjB,kBAAoB;AAAA,QACpB,mBAAqB;AAAA,QACrB,0BAA4B;AAAA,QAC5B,cAAgB;AAAA,QAChB,KAAO;AAAA,QACP,YAAc;AAAA,QACd,iBAAmB;AAAA,QACnB,KAAO;AAAA,QACP,uBAAyB;AAAA,QACzB,kBAAoB;AAAA,QACpB,oBAAsB;AAAA,QACtB,WAAa;AAAA,QACb,aAAe;AAAA,QACf,cAAgB;AAAA,QAChB,qBAAuB;AAAA,QACvB,gCAAkC;AAAA,QAClC,gCAAkC;AAAA,QAClC,iCAAmC;AAAA,QACnC,iCAAmC;AAAA,QACnC,sBAAwB;AAAA,QACxB,gBAAkB;AAAA,QAClB,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,aAAe;AAAA,QACf,wBAA0B;AAAA,QAC1B,iBAAmB;AAAA,QACnB,aAAe;AAAA,QACf,mBAAqB;AAAA,QACrB,kBAAoB;AAAA,QACpB,aAAe;AAAA,QACf,cAAgB;AAAA,QAChB,YAAc;AAAA,QACd,mBAAqB;AAAA,QACrB,uBAAyB;AAAA,QACzB,cAAgB;AAAA,QAChB,aAAe;AAAA,QACf,4BAA8B;AAAA,QAC9B,WAAa;AAAA,QACb,cAAgB;AAAA,QAChB,wBAA0B;AAAA,QAC1B,sBAAwB;AAAA,QACxB,wBAA0B;AAAA,QAC1B,yBAA2B;AAAA,QAC3B,6BAA+B;AAAA,QAC/B,qCAAuC;AAAA,QACvC,iCAAmC;AAAA,QACnC,WAAa;AAAA,QACb,gBAAkB;AAAA,QAClB,iBAAmB;AAAA,QACnB,cAAgB;AAAA,QAChB,iCAAmC;AAAA,QACnC,kCAAoC;AAAA,QACpC,mBAAqB;AAAA,QACrB,sBAAwB;AAAA,QACxB,MAAQ;AAAA,QACR,QAAU;AAAA,QACV,mBAAqB;AAAA,QACrB,gBAAkB;AAAA,QAClB,iBAAmB;AAAA,QACnB,gBAAkB;AAAA,QAClB,iCAAmC;AAAA,QACnC,6BAA+B;AAAA,QAC/B,gBAAkB;AAAA,QAClB,2BAA6B;AAAA,QAC7B,sBAAwB;AAAA,MACzB;AAAA,MACA,KAAO;AAAA,QACN,eAAiB;AAAA,QACjB,gBAAkB;AAAA,QAClB,OAAS;AAAA,QACT,YAAc;AAAA,QACd,WAAa;AAAA,QACb,eAAiB;AAAA,QACjB,cAAgB;AAAA,QAChB,0BAA4B;AAAA,QAC5B,0BAA4B;AAAA,QAC5B,0BAA4B;AAAA,QAC5B,SAAW;AAAA,QACX,SAAW;AAAA,QACX,KAAO;AAAA,MACR;AAAA,MACA,KAAO;AAAA,QACN,OAAS;AAAA,QACT,cAAgB;AAAA,QAChB,KAAO;AAAA,QACP,YAAc;AAAA,MACf;AAAA,IACD;AAAA;AAAA;;;AChpGA,IAAAC,mBAAA;AAAA,kCAAAC,UAAAC,SAAA;AAAA;AACA,IAAAA,QAAO,UAAU;AAAA;AAAA;;;ACDjB;AAAA;AAAA;AAAA;AAAA,IACA,gBAEM,YAEA,QAmDC;AAxDP;AAAA;AAAA;AACA,qBAAoB;AAEpB,IAAM,aAAa;AAEnB,IAAM,SAAS;AAAA,MACb,MAAM;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,SAAS,CAAC;AAAA,MACV,OAAO;AAAA,QACL,CAAC,IAAQ,GAAG;AAAA,MACd;AAAA,IACF;AAEA,WAAO,OAAO,OAAO,SAAS;AAAA;AAAA,MAE5B,aAAa;AAAA,QACX,OAAO,CAAC,sCAAsC;AAAA,QAC9C,SAAS;AAAA;AAAA,UAEP,CAAC,UAAU,GAAG;AAAA,QAChB;AAAA,QACA,OAAO;AAAA,UACL,CAAC,aAAa,MAAM,IAAQ,GAAG;AAAA,QACjC;AAAA,QACA,iBAAiB;AAAA,UACf,SAAS;AAAA;AAAA,YAEP,GAAG,eAAAC,QAAQ;AAAA,UACb;AAAA,UACA,eAAe;AAAA,YACb,cAAc;AAAA,cACZ,KAAK;AAAA,YACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,sBAAsB;AAAA,QACpB,SAAS,CAAC,UAAU;AAAA,QACpB,OAAO;AAAA,UACL,CAAC,aAAa,MAAM,IAAQ,GAAG;AAAA,QACjC;AAAA,QACA,SAAS;AAAA;AAAA,UAEP,GAAG,eAAAA,QAAQ;AAAA,QACb;AAAA,QACA,eAAe;AAAA,UACb,cAAc;AAAA,YACZ,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAED,IAAO,gBAAQ;AAAA;AAAA;;;ACpDf,OAAO,UAAU,4CAAsB;", "names": ["exports", "module", "exports", "module", "name", "evk", "node", "ref", "exports", "module", "require_globals", "exports", "module", "globals"]}