var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __esm = (fn, res) => function __init() {
  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
};
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __export = (target, all) => {
  for (var name2 in all)
    __defProp(target, name2, { get: all[name2], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/messages.js
var messageIds, messages;
var init_messages = __esm({
  "src/messages.js"() {
    messageIds = {
      avoidEmptyEffect: "avoidEmptyEffect",
      avoidDerivedState: "avoidDerivedState",
      avoidInitializingState: "avoidInitializingState",
      avoidChainingState: "avoidChainingState",
      avoidParentChildCoupling: "avoidParentChildCoupling",
      avoidResettingStateFromProps: "avoidResettingStateFromProps"
      // TODO: This would be nice, but I'm not sure it can be done accurately
      // Maybe we can accurately warn about this when the state being reacted to is one of our own `useState`s?
      // Because if we have a setter then we have a callback.
      // But, I think that would also warn about valid uses that synchronize internal state to external state.
      // avoidEventHandler: "avoidEventHandler",
      // TODO: Possible to detect when `useSyncExternalStore` should be preferred?
    };
    messages = {
      [messageIds.avoidEmptyEffect]: "This effect is empty and could be removed.",
      [messageIds.avoidDerivedState]: 'Avoid storing derived state. Compute "{{state}}" directly during render, optionally with `useMemo` if it\'s expensive.',
      [messageIds.avoidInitializingState]: 'Avoid initializing state in an effect. Instead, pass "{{state}}"\'s initial value to its `useState`.',
      [messageIds.avoidChainingState]: "Avoid chaining state changes. When possible, update all relevant state simultaneously.",
      [messageIds.avoidParentChildCoupling]: "Avoid coupling parent behavior or state to a child component. Instead, lift shared logic or state up to the parent.",
      [messageIds.avoidResettingStateFromProps]: 'Avoid resetting state from props. If "{{prop}}" is a key, pass it as `key` instead so React will reset the component.'
      // [messages.avoidEventHandler]:
      //   "Avoid using state as an event handler. Instead, call the event handler directly.",
    };
  }
});

// node_modules/eslint-utils/node_modules/eslint-visitor-keys/lib/visitor-keys.json
var require_visitor_keys = __commonJS({
  "node_modules/eslint-utils/node_modules/eslint-visitor-keys/lib/visitor-keys.json"(exports2, module2) {
    module2.exports = {
      AssignmentExpression: [
        "left",
        "right"
      ],
      AssignmentPattern: [
        "left",
        "right"
      ],
      ArrayExpression: [
        "elements"
      ],
      ArrayPattern: [
        "elements"
      ],
      ArrowFunctionExpression: [
        "params",
        "body"
      ],
      AwaitExpression: [
        "argument"
      ],
      BlockStatement: [
        "body"
      ],
      BinaryExpression: [
        "left",
        "right"
      ],
      BreakStatement: [
        "label"
      ],
      CallExpression: [
        "callee",
        "arguments"
      ],
      CatchClause: [
        "param",
        "body"
      ],
      ChainExpression: [
        "expression"
      ],
      ClassBody: [
        "body"
      ],
      ClassDeclaration: [
        "id",
        "superClass",
        "body"
      ],
      ClassExpression: [
        "id",
        "superClass",
        "body"
      ],
      ConditionalExpression: [
        "test",
        "consequent",
        "alternate"
      ],
      ContinueStatement: [
        "label"
      ],
      DebuggerStatement: [],
      DoWhileStatement: [
        "body",
        "test"
      ],
      EmptyStatement: [],
      ExportAllDeclaration: [
        "exported",
        "source"
      ],
      ExportDefaultDeclaration: [
        "declaration"
      ],
      ExportNamedDeclaration: [
        "declaration",
        "specifiers",
        "source"
      ],
      ExportSpecifier: [
        "exported",
        "local"
      ],
      ExpressionStatement: [
        "expression"
      ],
      ExperimentalRestProperty: [
        "argument"
      ],
      ExperimentalSpreadProperty: [
        "argument"
      ],
      ForStatement: [
        "init",
        "test",
        "update",
        "body"
      ],
      ForInStatement: [
        "left",
        "right",
        "body"
      ],
      ForOfStatement: [
        "left",
        "right",
        "body"
      ],
      FunctionDeclaration: [
        "id",
        "params",
        "body"
      ],
      FunctionExpression: [
        "id",
        "params",
        "body"
      ],
      Identifier: [],
      IfStatement: [
        "test",
        "consequent",
        "alternate"
      ],
      ImportDeclaration: [
        "specifiers",
        "source"
      ],
      ImportDefaultSpecifier: [
        "local"
      ],
      ImportExpression: [
        "source"
      ],
      ImportNamespaceSpecifier: [
        "local"
      ],
      ImportSpecifier: [
        "imported",
        "local"
      ],
      JSXAttribute: [
        "name",
        "value"
      ],
      JSXClosingElement: [
        "name"
      ],
      JSXElement: [
        "openingElement",
        "children",
        "closingElement"
      ],
      JSXEmptyExpression: [],
      JSXExpressionContainer: [
        "expression"
      ],
      JSXIdentifier: [],
      JSXMemberExpression: [
        "object",
        "property"
      ],
      JSXNamespacedName: [
        "namespace",
        "name"
      ],
      JSXOpeningElement: [
        "name",
        "attributes"
      ],
      JSXSpreadAttribute: [
        "argument"
      ],
      JSXText: [],
      JSXFragment: [
        "openingFragment",
        "children",
        "closingFragment"
      ],
      Literal: [],
      LabeledStatement: [
        "label",
        "body"
      ],
      LogicalExpression: [
        "left",
        "right"
      ],
      MemberExpression: [
        "object",
        "property"
      ],
      MetaProperty: [
        "meta",
        "property"
      ],
      MethodDefinition: [
        "key",
        "value"
      ],
      NewExpression: [
        "callee",
        "arguments"
      ],
      ObjectExpression: [
        "properties"
      ],
      ObjectPattern: [
        "properties"
      ],
      PrivateIdentifier: [],
      Program: [
        "body"
      ],
      Property: [
        "key",
        "value"
      ],
      PropertyDefinition: [
        "key",
        "value"
      ],
      RestElement: [
        "argument"
      ],
      ReturnStatement: [
        "argument"
      ],
      SequenceExpression: [
        "expressions"
      ],
      SpreadElement: [
        "argument"
      ],
      Super: [],
      SwitchStatement: [
        "discriminant",
        "cases"
      ],
      SwitchCase: [
        "test",
        "consequent"
      ],
      TaggedTemplateExpression: [
        "tag",
        "quasi"
      ],
      TemplateElement: [],
      TemplateLiteral: [
        "quasis",
        "expressions"
      ],
      ThisExpression: [],
      ThrowStatement: [
        "argument"
      ],
      TryStatement: [
        "block",
        "handler",
        "finalizer"
      ],
      UnaryExpression: [
        "argument"
      ],
      UpdateExpression: [
        "argument"
      ],
      VariableDeclaration: [
        "declarations"
      ],
      VariableDeclarator: [
        "id",
        "init"
      ],
      WhileStatement: [
        "test",
        "body"
      ],
      WithStatement: [
        "object",
        "body"
      ],
      YieldExpression: [
        "argument"
      ]
    };
  }
});

// node_modules/eslint-utils/node_modules/eslint-visitor-keys/lib/index.js
var require_lib = __commonJS({
  "node_modules/eslint-utils/node_modules/eslint-visitor-keys/lib/index.js"(exports2, module2) {
    "use strict";
    var KEYS = require_visitor_keys();
    var NODE_TYPES = Object.freeze(Object.keys(KEYS));
    for (const type of NODE_TYPES) {
      Object.freeze(KEYS[type]);
    }
    Object.freeze(KEYS);
    var KEY_BLACKLIST = /* @__PURE__ */ new Set([
      "parent",
      "leadingComments",
      "trailingComments"
    ]);
    function filterKey(key) {
      return !KEY_BLACKLIST.has(key) && key[0] !== "_";
    }
    module2.exports = Object.freeze({
      /**
       * Visitor keys.
       * @type {{ [type: string]: string[] | undefined }}
       */
      KEYS,
      /**
       * Get visitor keys of a given node.
       * @param {Object} node The AST node to get keys.
       * @returns {string[]} Visitor keys of the node.
       */
      getKeys(node) {
        return Object.keys(node).filter(filterKey);
      },
      // Disable valid-jsdoc rule because it reports syntax error on the type of @returns.
      // eslint-disable-next-line valid-jsdoc
      /**
       * Make the union set with `KEYS` and given keys.
       * @param {Object} additionalKeys The additional keys.
       * @returns {{ [type: string]: string[] | undefined }} The union set.
       */
      unionWith(additionalKeys) {
        const retv = Object.assign({}, KEYS);
        for (const type of Object.keys(additionalKeys)) {
          if (retv.hasOwnProperty(type)) {
            const keys = new Set(additionalKeys[type]);
            for (const key of retv[type]) {
              keys.add(key);
            }
            retv[type] = Object.freeze(Array.from(keys));
          } else {
            retv[type] = Object.freeze(Array.from(additionalKeys[type]));
          }
        }
        return Object.freeze(retv);
      }
    });
  }
});

// node_modules/eslint-utils/index.mjs
function getInnermostScope(initialScope, node) {
  const location = node.range[0];
  let scope = initialScope;
  let found = false;
  do {
    found = false;
    for (const childScope of scope.childScopes) {
      const range = childScope.block.range;
      if (range[0] <= location && location < range[1]) {
        scope = childScope;
        found = true;
        break;
      }
    }
  } while (found);
  return scope;
}
function findVariable(initialScope, nameOrNode) {
  let name2 = "";
  let scope = initialScope;
  if (typeof nameOrNode === "string") {
    name2 = nameOrNode;
  } else {
    name2 = nameOrNode.name;
    scope = getInnermostScope(scope, nameOrNode);
  }
  while (scope != null) {
    const variable = scope.set.get(name2);
    if (variable != null) {
      return variable;
    }
    scope = scope.upper;
  }
  return null;
}
function negate0(token) {
  return !this(token);
}
function negate(f) {
  return negate0.bind(f);
}
function isPunctuatorTokenWithValue(token, value) {
  return token.type === "Punctuator" && token.value === value;
}
function isArrowToken(token) {
  return isPunctuatorTokenWithValue(token, "=>");
}
function isCommaToken(token) {
  return isPunctuatorTokenWithValue(token, ",");
}
function isSemicolonToken(token) {
  return isPunctuatorTokenWithValue(token, ";");
}
function isColonToken(token) {
  return isPunctuatorTokenWithValue(token, ":");
}
function isOpeningParenToken(token) {
  return isPunctuatorTokenWithValue(token, "(");
}
function isClosingParenToken(token) {
  return isPunctuatorTokenWithValue(token, ")");
}
function isOpeningBracketToken(token) {
  return isPunctuatorTokenWithValue(token, "[");
}
function isClosingBracketToken(token) {
  return isPunctuatorTokenWithValue(token, "]");
}
function isOpeningBraceToken(token) {
  return isPunctuatorTokenWithValue(token, "{");
}
function isClosingBraceToken(token) {
  return isPunctuatorTokenWithValue(token, "}");
}
function isCommentToken(token) {
  return ["Block", "Line", "Shebang"].includes(token.type);
}
function getPropertyDescriptor(object, name2) {
  let x = object;
  while ((typeof x === "object" || typeof x === "function") && x !== null) {
    const d = Object.getOwnPropertyDescriptor(x, name2);
    if (d) {
      return d;
    }
    x = Object.getPrototypeOf(x);
  }
  return null;
}
function isGetter(object, name2) {
  const d = getPropertyDescriptor(object, name2);
  return d != null && d.get != null;
}
function getElementValues(nodeList, initialScope) {
  const valueList = [];
  for (let i = 0; i < nodeList.length; ++i) {
    const elementNode = nodeList[i];
    if (elementNode == null) {
      valueList.length = i + 1;
    } else if (elementNode.type === "SpreadElement") {
      const argument = getStaticValueR(elementNode.argument, initialScope);
      if (argument == null) {
        return null;
      }
      valueList.push(...argument.value);
    } else {
      const element = getStaticValueR(elementNode, initialScope);
      if (element == null) {
        return null;
      }
      valueList.push(element.value);
    }
  }
  return valueList;
}
function getStaticValueR(node, initialScope) {
  if (node != null && Object.hasOwnProperty.call(operations, node.type)) {
    return operations[node.type](node, initialScope);
  }
  return null;
}
function getStaticPropertyNameValue(node, initialScope) {
  const nameNode = node.type === "Property" ? node.key : node.property;
  if (node.computed) {
    return getStaticValueR(nameNode, initialScope);
  }
  if (nameNode.type === "Identifier") {
    return { value: nameNode.name };
  }
  if (nameNode.type === "Literal") {
    if (nameNode.bigint) {
      return { value: nameNode.bigint };
    }
    return { value: String(nameNode.value) };
  }
  return null;
}
function getStaticValue(node, initialScope = null) {
  try {
    return getStaticValueR(node, initialScope);
  } catch (_error) {
    return null;
  }
}
function getStringIfConstant(node, initialScope = null) {
  if (node && node.type === "Literal" && node.value === null) {
    if (node.regex) {
      return `/${node.regex.pattern}/${node.regex.flags}`;
    }
    if (node.bigint) {
      return node.bigint;
    }
  }
  const evaluated = getStaticValue(node, initialScope);
  return evaluated && String(evaluated.value);
}
function getPropertyName(node, initialScope) {
  switch (node.type) {
    case "MemberExpression":
      if (node.computed) {
        return getStringIfConstant(node.property, initialScope);
      }
      if (node.property.type === "PrivateIdentifier") {
        return null;
      }
      return node.property.name;
    case "Property":
    case "MethodDefinition":
    case "PropertyDefinition":
      if (node.computed) {
        return getStringIfConstant(node.key, initialScope);
      }
      if (node.key.type === "Literal") {
        return String(node.key.value);
      }
      if (node.key.type === "PrivateIdentifier") {
        return null;
      }
      return node.key.name;
  }
  return null;
}
function isNode(x) {
  return x !== null && typeof x === "object" && typeof x.type === "string";
}
function isModifiedGlobal(variable) {
  return variable == null || variable.defs.length !== 0 || variable.references.some((r) => r.isWrite());
}
function isPassThrough(node) {
  const parent = node.parent;
  switch (parent && parent.type) {
    case "ConditionalExpression":
      return parent.consequent === node || parent.alternate === node;
    case "LogicalExpression":
      return true;
    case "SequenceExpression":
      return parent.expressions[parent.expressions.length - 1] === node;
    case "ChainExpression":
      return true;
    default:
      return false;
  }
}
function exceptDefault(name2, index) {
  return !(index === 1 && name2 === "default");
}
var import_eslint_visitor_keys, isNotArrowToken, isNotCommaToken, isNotSemicolonToken, isNotColonToken, isNotOpeningParenToken, isNotClosingParenToken, isNotOpeningBracketToken, isNotClosingBracketToken, isNotOpeningBraceToken, isNotClosingBraceToken, isNotCommentToken, globalObject, builtinNames, callAllowed, callPassThrough, operations, typeConversionBinaryOps, typeConversionUnaryOps, visitor, IMPORT_TYPE, has, READ, CALL, CONSTRUCT, ESM, requireCall, ReferenceTracker;
var init_eslint_utils = __esm({
  "node_modules/eslint-utils/index.mjs"() {
    import_eslint_visitor_keys = __toESM(require_lib(), 1);
    isNotArrowToken = negate(isArrowToken);
    isNotCommaToken = negate(isCommaToken);
    isNotSemicolonToken = negate(isSemicolonToken);
    isNotColonToken = negate(isColonToken);
    isNotOpeningParenToken = negate(isOpeningParenToken);
    isNotClosingParenToken = negate(isClosingParenToken);
    isNotOpeningBracketToken = negate(isOpeningBracketToken);
    isNotClosingBracketToken = negate(isClosingBracketToken);
    isNotOpeningBraceToken = negate(isOpeningBraceToken);
    isNotClosingBraceToken = negate(isClosingBraceToken);
    isNotCommentToken = negate(isCommentToken);
    globalObject = typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : {};
    builtinNames = Object.freeze(
      /* @__PURE__ */ new Set([
        "Array",
        "ArrayBuffer",
        "BigInt",
        "BigInt64Array",
        "BigUint64Array",
        "Boolean",
        "DataView",
        "Date",
        "decodeURI",
        "decodeURIComponent",
        "encodeURI",
        "encodeURIComponent",
        "escape",
        "Float32Array",
        "Float64Array",
        "Function",
        "Infinity",
        "Int16Array",
        "Int32Array",
        "Int8Array",
        "isFinite",
        "isNaN",
        "isPrototypeOf",
        "JSON",
        "Map",
        "Math",
        "NaN",
        "Number",
        "Object",
        "parseFloat",
        "parseInt",
        "Promise",
        "Proxy",
        "Reflect",
        "RegExp",
        "Set",
        "String",
        "Symbol",
        "Uint16Array",
        "Uint32Array",
        "Uint8Array",
        "Uint8ClampedArray",
        "undefined",
        "unescape",
        "WeakMap",
        "WeakSet"
      ])
    );
    callAllowed = new Set(
      [
        Array.isArray,
        typeof BigInt === "function" ? BigInt : void 0,
        Boolean,
        Date,
        Date.parse,
        decodeURI,
        decodeURIComponent,
        encodeURI,
        encodeURIComponent,
        escape,
        isFinite,
        isNaN,
        isPrototypeOf,
        ...Object.getOwnPropertyNames(Math).map((k) => Math[k]).filter((f) => typeof f === "function"),
        Number,
        Number.isFinite,
        Number.isNaN,
        Number.parseFloat,
        Number.parseInt,
        Object,
        Object.entries,
        Object.is,
        Object.isExtensible,
        Object.isFrozen,
        Object.isSealed,
        Object.keys,
        Object.values,
        parseFloat,
        parseInt,
        RegExp,
        String,
        String.fromCharCode,
        String.fromCodePoint,
        String.raw,
        Symbol.for,
        Symbol.keyFor,
        unescape
      ].filter((f) => typeof f === "function")
    );
    callPassThrough = /* @__PURE__ */ new Set([
      Object.freeze,
      Object.preventExtensions,
      Object.seal
    ]);
    operations = Object.freeze({
      ArrayExpression(node, initialScope) {
        const elements = getElementValues(node.elements, initialScope);
        return elements != null ? { value: elements } : null;
      },
      AssignmentExpression(node, initialScope) {
        if (node.operator === "=") {
          return getStaticValueR(node.right, initialScope);
        }
        return null;
      },
      //eslint-disable-next-line complexity
      BinaryExpression(node, initialScope) {
        if (node.operator === "in" || node.operator === "instanceof") {
          return null;
        }
        const left = getStaticValueR(node.left, initialScope);
        const right = getStaticValueR(node.right, initialScope);
        if (left != null && right != null) {
          switch (node.operator) {
            case "==":
              return { value: left.value == right.value };
            //eslint-disable-line eqeqeq
            case "!=":
              return { value: left.value != right.value };
            //eslint-disable-line eqeqeq
            case "===":
              return { value: left.value === right.value };
            case "!==":
              return { value: left.value !== right.value };
            case "<":
              return { value: left.value < right.value };
            case "<=":
              return { value: left.value <= right.value };
            case ">":
              return { value: left.value > right.value };
            case ">=":
              return { value: left.value >= right.value };
            case "<<":
              return { value: left.value << right.value };
            case ">>":
              return { value: left.value >> right.value };
            case ">>>":
              return { value: left.value >>> right.value };
            case "+":
              return { value: left.value + right.value };
            case "-":
              return { value: left.value - right.value };
            case "*":
              return { value: left.value * right.value };
            case "/":
              return { value: left.value / right.value };
            case "%":
              return { value: left.value % right.value };
            case "**":
              return { value: Math.pow(left.value, right.value) };
            case "|":
              return { value: left.value | right.value };
            case "^":
              return { value: left.value ^ right.value };
            case "&":
              return { value: left.value & right.value };
          }
        }
        return null;
      },
      CallExpression(node, initialScope) {
        const calleeNode = node.callee;
        const args = getElementValues(node.arguments, initialScope);
        if (args != null) {
          if (calleeNode.type === "MemberExpression") {
            if (calleeNode.property.type === "PrivateIdentifier") {
              return null;
            }
            const object = getStaticValueR(calleeNode.object, initialScope);
            if (object != null) {
              if (object.value == null && (object.optional || node.optional)) {
                return { value: void 0, optional: true };
              }
              const property = getStaticPropertyNameValue(
                calleeNode,
                initialScope
              );
              if (property != null) {
                const receiver = object.value;
                const methodName = property.value;
                if (callAllowed.has(receiver[methodName])) {
                  return { value: receiver[methodName](...args) };
                }
                if (callPassThrough.has(receiver[methodName])) {
                  return { value: args[0] };
                }
              }
            }
          } else {
            const callee = getStaticValueR(calleeNode, initialScope);
            if (callee != null) {
              if (callee.value == null && node.optional) {
                return { value: void 0, optional: true };
              }
              const func = callee.value;
              if (callAllowed.has(func)) {
                return { value: func(...args) };
              }
              if (callPassThrough.has(func)) {
                return { value: args[0] };
              }
            }
          }
        }
        return null;
      },
      ConditionalExpression(node, initialScope) {
        const test = getStaticValueR(node.test, initialScope);
        if (test != null) {
          return test.value ? getStaticValueR(node.consequent, initialScope) : getStaticValueR(node.alternate, initialScope);
        }
        return null;
      },
      ExpressionStatement(node, initialScope) {
        return getStaticValueR(node.expression, initialScope);
      },
      Identifier(node, initialScope) {
        if (initialScope != null) {
          const variable = findVariable(initialScope, node);
          if (variable != null && variable.defs.length === 0 && builtinNames.has(variable.name) && variable.name in globalObject) {
            return { value: globalObject[variable.name] };
          }
          if (variable != null && variable.defs.length === 1) {
            const def = variable.defs[0];
            if (def.parent && def.parent.kind === "const" && // TODO(mysticatea): don't support destructuring here.
            def.node.id.type === "Identifier") {
              return getStaticValueR(def.node.init, initialScope);
            }
          }
        }
        return null;
      },
      Literal(node) {
        if ((node.regex != null || node.bigint != null) && node.value == null) {
          return null;
        }
        return { value: node.value };
      },
      LogicalExpression(node, initialScope) {
        const left = getStaticValueR(node.left, initialScope);
        if (left != null) {
          if (node.operator === "||" && Boolean(left.value) === true || node.operator === "&&" && Boolean(left.value) === false || node.operator === "??" && left.value != null) {
            return left;
          }
          const right = getStaticValueR(node.right, initialScope);
          if (right != null) {
            return right;
          }
        }
        return null;
      },
      MemberExpression(node, initialScope) {
        if (node.property.type === "PrivateIdentifier") {
          return null;
        }
        const object = getStaticValueR(node.object, initialScope);
        if (object != null) {
          if (object.value == null && (object.optional || node.optional)) {
            return { value: void 0, optional: true };
          }
          const property = getStaticPropertyNameValue(node, initialScope);
          if (property != null && !isGetter(object.value, property.value)) {
            return { value: object.value[property.value] };
          }
        }
        return null;
      },
      ChainExpression(node, initialScope) {
        const expression = getStaticValueR(node.expression, initialScope);
        if (expression != null) {
          return { value: expression.value };
        }
        return null;
      },
      NewExpression(node, initialScope) {
        const callee = getStaticValueR(node.callee, initialScope);
        const args = getElementValues(node.arguments, initialScope);
        if (callee != null && args != null) {
          const Func = callee.value;
          if (callAllowed.has(Func)) {
            return { value: new Func(...args) };
          }
        }
        return null;
      },
      ObjectExpression(node, initialScope) {
        const object = {};
        for (const propertyNode of node.properties) {
          if (propertyNode.type === "Property") {
            if (propertyNode.kind !== "init") {
              return null;
            }
            const key = getStaticPropertyNameValue(
              propertyNode,
              initialScope
            );
            const value = getStaticValueR(propertyNode.value, initialScope);
            if (key == null || value == null) {
              return null;
            }
            object[key.value] = value.value;
          } else if (propertyNode.type === "SpreadElement" || propertyNode.type === "ExperimentalSpreadProperty") {
            const argument = getStaticValueR(
              propertyNode.argument,
              initialScope
            );
            if (argument == null) {
              return null;
            }
            Object.assign(object, argument.value);
          } else {
            return null;
          }
        }
        return { value: object };
      },
      SequenceExpression(node, initialScope) {
        const last = node.expressions[node.expressions.length - 1];
        return getStaticValueR(last, initialScope);
      },
      TaggedTemplateExpression(node, initialScope) {
        const tag = getStaticValueR(node.tag, initialScope);
        const expressions = getElementValues(
          node.quasi.expressions,
          initialScope
        );
        if (tag != null && expressions != null) {
          const func = tag.value;
          const strings = node.quasi.quasis.map((q) => q.value.cooked);
          strings.raw = node.quasi.quasis.map((q) => q.value.raw);
          if (func === String.raw) {
            return { value: func(strings, ...expressions) };
          }
        }
        return null;
      },
      TemplateLiteral(node, initialScope) {
        const expressions = getElementValues(node.expressions, initialScope);
        if (expressions != null) {
          let value = node.quasis[0].value.cooked;
          for (let i = 0; i < expressions.length; ++i) {
            value += expressions[i];
            value += node.quasis[i + 1].value.cooked;
          }
          return { value };
        }
        return null;
      },
      UnaryExpression(node, initialScope) {
        if (node.operator === "delete") {
          return null;
        }
        if (node.operator === "void") {
          return { value: void 0 };
        }
        const arg = getStaticValueR(node.argument, initialScope);
        if (arg != null) {
          switch (node.operator) {
            case "-":
              return { value: -arg.value };
            case "+":
              return { value: +arg.value };
            //eslint-disable-line no-implicit-coercion
            case "!":
              return { value: !arg.value };
            case "~":
              return { value: ~arg.value };
            case "typeof":
              return { value: typeof arg.value };
          }
        }
        return null;
      }
    });
    typeConversionBinaryOps = Object.freeze(
      /* @__PURE__ */ new Set([
        "==",
        "!=",
        "<",
        "<=",
        ">",
        ">=",
        "<<",
        ">>",
        ">>>",
        "+",
        "-",
        "*",
        "/",
        "%",
        "|",
        "^",
        "&",
        "in"
      ])
    );
    typeConversionUnaryOps = Object.freeze(/* @__PURE__ */ new Set(["-", "+", "!", "~"]));
    visitor = Object.freeze(
      Object.assign(/* @__PURE__ */ Object.create(null), {
        $visit(node, options, visitorKeys) {
          const { type } = node;
          if (typeof this[type] === "function") {
            return this[type](node, options, visitorKeys);
          }
          return this.$visitChildren(node, options, visitorKeys);
        },
        $visitChildren(node, options, visitorKeys) {
          const { type } = node;
          for (const key of visitorKeys[type] || import_eslint_visitor_keys.default.getKeys(node)) {
            const value = node[key];
            if (Array.isArray(value)) {
              for (const element of value) {
                if (isNode(element) && this.$visit(element, options, visitorKeys)) {
                  return true;
                }
              }
            } else if (isNode(value) && this.$visit(value, options, visitorKeys)) {
              return true;
            }
          }
          return false;
        },
        ArrowFunctionExpression() {
          return false;
        },
        AssignmentExpression() {
          return true;
        },
        AwaitExpression() {
          return true;
        },
        BinaryExpression(node, options, visitorKeys) {
          if (options.considerImplicitTypeConversion && typeConversionBinaryOps.has(node.operator) && (node.left.type !== "Literal" || node.right.type !== "Literal")) {
            return true;
          }
          return this.$visitChildren(node, options, visitorKeys);
        },
        CallExpression() {
          return true;
        },
        FunctionExpression() {
          return false;
        },
        ImportExpression() {
          return true;
        },
        MemberExpression(node, options, visitorKeys) {
          if (options.considerGetters) {
            return true;
          }
          if (options.considerImplicitTypeConversion && node.computed && node.property.type !== "Literal") {
            return true;
          }
          return this.$visitChildren(node, options, visitorKeys);
        },
        MethodDefinition(node, options, visitorKeys) {
          if (options.considerImplicitTypeConversion && node.computed && node.key.type !== "Literal") {
            return true;
          }
          return this.$visitChildren(node, options, visitorKeys);
        },
        NewExpression() {
          return true;
        },
        Property(node, options, visitorKeys) {
          if (options.considerImplicitTypeConversion && node.computed && node.key.type !== "Literal") {
            return true;
          }
          return this.$visitChildren(node, options, visitorKeys);
        },
        PropertyDefinition(node, options, visitorKeys) {
          if (options.considerImplicitTypeConversion && node.computed && node.key.type !== "Literal") {
            return true;
          }
          return this.$visitChildren(node, options, visitorKeys);
        },
        UnaryExpression(node, options, visitorKeys) {
          if (node.operator === "delete") {
            return true;
          }
          if (options.considerImplicitTypeConversion && typeConversionUnaryOps.has(node.operator) && node.argument.type !== "Literal") {
            return true;
          }
          return this.$visitChildren(node, options, visitorKeys);
        },
        UpdateExpression() {
          return true;
        },
        YieldExpression() {
          return true;
        }
      })
    );
    IMPORT_TYPE = /^(?:Import|Export(?:All|Default|Named))Declaration$/u;
    has = Function.call.bind(Object.hasOwnProperty);
    READ = Symbol("read");
    CALL = Symbol("call");
    CONSTRUCT = Symbol("construct");
    ESM = Symbol("esm");
    requireCall = { require: { [CALL]: true } };
    ReferenceTracker = class {
      /**
       * Initialize this tracker.
       * @param {Scope} globalScope The global scope.
       * @param {object} [options] The options.
       * @param {"legacy"|"strict"} [options.mode="strict"] The mode to determine the ImportDeclaration's behavior for CJS modules.
       * @param {string[]} [options.globalObjectNames=["global","globalThis","self","window"]] The variable names for Global Object.
       */
      constructor(globalScope, {
        mode = "strict",
        globalObjectNames = ["global", "globalThis", "self", "window"]
      } = {}) {
        this.variableStack = [];
        this.globalScope = globalScope;
        this.mode = mode;
        this.globalObjectNames = globalObjectNames.slice(0);
      }
      /**
       * Iterate the references of global variables.
       * @param {object} traceMap The trace map.
       * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.
       */
      *iterateGlobalReferences(traceMap) {
        for (const key of Object.keys(traceMap)) {
          const nextTraceMap = traceMap[key];
          const path = [key];
          const variable = this.globalScope.set.get(key);
          if (isModifiedGlobal(variable)) {
            continue;
          }
          yield* this._iterateVariableReferences(
            variable,
            path,
            nextTraceMap,
            true
          );
        }
        for (const key of this.globalObjectNames) {
          const path = [];
          const variable = this.globalScope.set.get(key);
          if (isModifiedGlobal(variable)) {
            continue;
          }
          yield* this._iterateVariableReferences(
            variable,
            path,
            traceMap,
            false
          );
        }
      }
      /**
       * Iterate the references of CommonJS modules.
       * @param {object} traceMap The trace map.
       * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.
       */
      *iterateCjsReferences(traceMap) {
        for (const { node } of this.iterateGlobalReferences(requireCall)) {
          const key = getStringIfConstant(node.arguments[0]);
          if (key == null || !has(traceMap, key)) {
            continue;
          }
          const nextTraceMap = traceMap[key];
          const path = [key];
          if (nextTraceMap[READ]) {
            yield {
              node,
              path,
              type: READ,
              info: nextTraceMap[READ]
            };
          }
          yield* this._iteratePropertyReferences(node, path, nextTraceMap);
        }
      }
      /**
       * Iterate the references of ES modules.
       * @param {object} traceMap The trace map.
       * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.
       */
      *iterateEsmReferences(traceMap) {
        const programNode = this.globalScope.block;
        for (const node of programNode.body) {
          if (!IMPORT_TYPE.test(node.type) || node.source == null) {
            continue;
          }
          const moduleId = node.source.value;
          if (!has(traceMap, moduleId)) {
            continue;
          }
          const nextTraceMap = traceMap[moduleId];
          const path = [moduleId];
          if (nextTraceMap[READ]) {
            yield { node, path, type: READ, info: nextTraceMap[READ] };
          }
          if (node.type === "ExportAllDeclaration") {
            for (const key of Object.keys(nextTraceMap)) {
              const exportTraceMap = nextTraceMap[key];
              if (exportTraceMap[READ]) {
                yield {
                  node,
                  path: path.concat(key),
                  type: READ,
                  info: exportTraceMap[READ]
                };
              }
            }
          } else {
            for (const specifier of node.specifiers) {
              const esm = has(nextTraceMap, ESM);
              const it = this._iterateImportReferences(
                specifier,
                path,
                esm ? nextTraceMap : this.mode === "legacy" ? { default: nextTraceMap, ...nextTraceMap } : { default: nextTraceMap }
              );
              if (esm) {
                yield* it;
              } else {
                for (const report of it) {
                  report.path = report.path.filter(exceptDefault);
                  if (report.path.length >= 2 || report.type !== READ) {
                    yield report;
                  }
                }
              }
            }
          }
        }
      }
      /**
       * Iterate the references for a given variable.
       * @param {Variable} variable The variable to iterate that references.
       * @param {string[]} path The current path.
       * @param {object} traceMap The trace map.
       * @param {boolean} shouldReport = The flag to report those references.
       * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.
       */
      *_iterateVariableReferences(variable, path, traceMap, shouldReport) {
        if (this.variableStack.includes(variable)) {
          return;
        }
        this.variableStack.push(variable);
        try {
          for (const reference of variable.references) {
            if (!reference.isRead()) {
              continue;
            }
            const node = reference.identifier;
            if (shouldReport && traceMap[READ]) {
              yield { node, path, type: READ, info: traceMap[READ] };
            }
            yield* this._iteratePropertyReferences(node, path, traceMap);
          }
        } finally {
          this.variableStack.pop();
        }
      }
      /**
       * Iterate the references for a given AST node.
       * @param rootNode The AST node to iterate references.
       * @param {string[]} path The current path.
       * @param {object} traceMap The trace map.
       * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.
       */
      //eslint-disable-next-line complexity
      *_iteratePropertyReferences(rootNode, path, traceMap) {
        let node = rootNode;
        while (isPassThrough(node)) {
          node = node.parent;
        }
        const parent = node.parent;
        if (parent.type === "MemberExpression") {
          if (parent.object === node) {
            const key = getPropertyName(parent);
            if (key == null || !has(traceMap, key)) {
              return;
            }
            path = path.concat(key);
            const nextTraceMap = traceMap[key];
            if (nextTraceMap[READ]) {
              yield {
                node: parent,
                path,
                type: READ,
                info: nextTraceMap[READ]
              };
            }
            yield* this._iteratePropertyReferences(
              parent,
              path,
              nextTraceMap
            );
          }
          return;
        }
        if (parent.type === "CallExpression") {
          if (parent.callee === node && traceMap[CALL]) {
            yield { node: parent, path, type: CALL, info: traceMap[CALL] };
          }
          return;
        }
        if (parent.type === "NewExpression") {
          if (parent.callee === node && traceMap[CONSTRUCT]) {
            yield {
              node: parent,
              path,
              type: CONSTRUCT,
              info: traceMap[CONSTRUCT]
            };
          }
          return;
        }
        if (parent.type === "AssignmentExpression") {
          if (parent.right === node) {
            yield* this._iterateLhsReferences(parent.left, path, traceMap);
            yield* this._iteratePropertyReferences(parent, path, traceMap);
          }
          return;
        }
        if (parent.type === "AssignmentPattern") {
          if (parent.right === node) {
            yield* this._iterateLhsReferences(parent.left, path, traceMap);
          }
          return;
        }
        if (parent.type === "VariableDeclarator") {
          if (parent.init === node) {
            yield* this._iterateLhsReferences(parent.id, path, traceMap);
          }
        }
      }
      /**
       * Iterate the references for a given Pattern node.
       * @param {Node} patternNode The Pattern node to iterate references.
       * @param {string[]} path The current path.
       * @param {object} traceMap The trace map.
       * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.
       */
      *_iterateLhsReferences(patternNode, path, traceMap) {
        if (patternNode.type === "Identifier") {
          const variable = findVariable(this.globalScope, patternNode);
          if (variable != null) {
            yield* this._iterateVariableReferences(
              variable,
              path,
              traceMap,
              false
            );
          }
          return;
        }
        if (patternNode.type === "ObjectPattern") {
          for (const property of patternNode.properties) {
            const key = getPropertyName(property);
            if (key == null || !has(traceMap, key)) {
              continue;
            }
            const nextPath = path.concat(key);
            const nextTraceMap = traceMap[key];
            if (nextTraceMap[READ]) {
              yield {
                node: property,
                path: nextPath,
                type: READ,
                info: nextTraceMap[READ]
              };
            }
            yield* this._iterateLhsReferences(
              property.value,
              nextPath,
              nextTraceMap
            );
          }
          return;
        }
        if (patternNode.type === "AssignmentPattern") {
          yield* this._iterateLhsReferences(patternNode.left, path, traceMap);
        }
      }
      /**
       * Iterate the references for a given ModuleSpecifier node.
       * @param {Node} specifierNode The ModuleSpecifier node to iterate references.
       * @param {string[]} path The current path.
       * @param {object} traceMap The trace map.
       * @returns {IterableIterator<{node:Node,path:string[],type:symbol,info:any}>} The iterator to iterate references.
       */
      *_iterateImportReferences(specifierNode, path, traceMap) {
        const type = specifierNode.type;
        if (type === "ImportSpecifier" || type === "ImportDefaultSpecifier") {
          const key = type === "ImportDefaultSpecifier" ? "default" : specifierNode.imported.name;
          if (!has(traceMap, key)) {
            return;
          }
          path = path.concat(key);
          const nextTraceMap = traceMap[key];
          if (nextTraceMap[READ]) {
            yield {
              node: specifierNode,
              path,
              type: READ,
              info: nextTraceMap[READ]
            };
          }
          yield* this._iterateVariableReferences(
            findVariable(this.globalScope, specifierNode.local),
            path,
            nextTraceMap,
            false
          );
          return;
        }
        if (type === "ImportNamespaceSpecifier") {
          yield* this._iterateVariableReferences(
            findVariable(this.globalScope, specifierNode.local),
            path,
            traceMap,
            false
          );
          return;
        }
        if (type === "ExportSpecifier") {
          const key = specifierNode.local.name;
          if (!has(traceMap, key)) {
            return;
          }
          path = path.concat(key);
          const nextTraceMap = traceMap[key];
          if (nextTraceMap[READ]) {
            yield {
              node: specifierNode,
              path,
              type: READ,
              info: nextTraceMap[READ]
            };
          }
        }
      }
    };
    ReferenceTracker.READ = READ;
    ReferenceTracker.CALL = CALL;
    ReferenceTracker.CONSTRUCT = CONSTRUCT;
    ReferenceTracker.ESM = ESM;
  }
});

// src/util/ast.js
var traverse, getDownstreamIdentifiers, getUpstreamVariables, getDownstreamRefs, getRef, getCallExpr, isIIFE;
var init_ast = __esm({
  "src/util/ast.js"() {
    init_eslint_utils();
    traverse = (context, node, visit, visited = /* @__PURE__ */ new Set()) => {
      if (visited.has(node)) {
        return;
      }
      visited.add(node);
      visit(node);
      (context.sourceCode.visitorKeys[node.type] || []).map((key) => node[key]).filter(Boolean).flatMap((child) => Array.isArray(child) ? child : [child]).filter(Boolean).filter((child) => typeof child.type === "string").forEach((child) => traverse(context, child, visit, visited));
    };
    getDownstreamIdentifiers = (context, rootNode) => {
      const identifiers = [];
      traverse(context, rootNode, (node) => {
        if (node.type === "Identifier") {
          identifiers.push(node);
        }
      });
      return identifiers;
    };
    getUpstreamVariables = (context, node, filter, visited = /* @__PURE__ */ new Set()) => {
      if (visited.has(node)) {
        return [];
      }
      visited.add(node);
      const variable = findVariable(context.sourceCode.getScope(node), node);
      if (!variable) {
        return [];
      }
      const upstreamVariables = variable.defs.filter((def) => !!def.node.init).filter((def) => filter(def.node)).flatMap((def) => getDownstreamIdentifiers(context, def.node.init)).flatMap(
        (identifier) => getUpstreamVariables(context, identifier, filter, visited)
      );
      return upstreamVariables.length === 0 ? [variable] : upstreamVariables;
    };
    getDownstreamRefs = (context, node) => getDownstreamIdentifiers(context, node).map((identifier) => getRef(context, identifier)).filter(Boolean);
    getRef = (context, identifier) => findVariable(
      context.sourceCode.getScope(identifier),
      identifier
    )?.references.find((ref) => ref.identifier === identifier);
    getCallExpr = (ref, current = ref.identifier.parent) => {
      if (current.type === "CallExpression") {
        let node = ref.identifier;
        while (node.parent.type === "MemberExpression") {
          node = node.parent;
        }
        if (current.callee === node) {
          return current;
        }
      }
      if (current.type === "MemberExpression") {
        return getCallExpr(ref, current.parent);
      }
      return void 0;
    };
    isIIFE = (node) => node.type === "CallExpression" && (node.callee.type === "ArrowFunctionExpression" || node.callee.type === "FunctionExpression");
  }
});

// src/util/react.js
function getDependenciesRefs(context, node) {
  if (!isUseEffect(node) || node.arguments.length < 2) {
    return void 0;
  }
  const depsArr = node.arguments[1];
  if (depsArr.type !== "ArrayExpression") {
    return void 0;
  }
  return getDownstreamRefs(context, depsArr);
}
var isReactFunctionalComponent, isReactFunctionalHOC, isCustomHook, isUseState, isUseEffect, getEffectFn, getEffectFnRefs, isFnRef, isStateSetter, isPropCallback, isState, isProp, isHOCProp, getDeclNode, getUseStateNode, isDirectCall, findPropUsedToResetAllState, isSetStateToInitialValue, countUseStates, countStateSetterCalls, findContainingNode, getUpstreamReactVariables;
var init_react = __esm({
  "src/util/react.js"() {
    init_ast();
    isReactFunctionalComponent = (node) => (node.type === "FunctionDeclaration" || node.type === "VariableDeclarator" && (node.init.type === "ArrowFunctionExpression" || node.init.type === "CallExpression")) && node.id.type === "Identifier" && node.id.name[0].toUpperCase() === node.id.name[0];
    isReactFunctionalHOC = (node) => node.type === "VariableDeclarator" && node.init && node.init.type === "CallExpression" && node.init.callee.type === "Identifier" && !["memo", "forwardRef"].includes(node.init.callee.name) && node.init.arguments.length > 0 && (node.init.arguments[0].type === "ArrowFunctionExpression" || node.init.arguments[0].type === "FunctionExpression") && node.id.type === "Identifier" && node.id.name[0].toUpperCase() === node.id.name[0];
    isCustomHook = (node) => (node.type === "FunctionDeclaration" || node.type === "VariableDeclarator" && node.init && (node.init.type === "ArrowFunctionExpression" || node.init.type === "FunctionExpression")) && node.id.type === "Identifier" && node.id.name.startsWith("use") && node.id.name[3] === node.id.name[3].toUpperCase();
    isUseState = (node) => node.type === "VariableDeclarator" && node.init && node.init.type === "CallExpression" && node.init.callee.name === "useState" && node.id.type === "ArrayPattern" && // Not sure its usecase, but may just have the setter
    (node.id.elements.length === 1 || node.id.elements.length === 2) && node.id.elements.every((el) => {
      return !el || el.type === "Identifier";
    });
    isUseEffect = (node) => node.type === "CallExpression" && (node.callee.type === "Identifier" && (node.callee.name === "useEffect" || node.callee.name === "useLayoutEffect") || node.callee.type === "MemberExpression" && node.callee.object.name === "React" && (node.callee.property.name === "useEffect" || node.callee.property.name === "useLayoutEffect"));
    getEffectFn = (node) => {
      if (!isUseEffect(node) || node.arguments.length < 1) {
        return void 0;
      }
      const effectFn = node.arguments[0];
      if (effectFn.type !== "ArrowFunctionExpression" && effectFn.type !== "FunctionExpression") {
        return void 0;
      }
      return effectFn;
    };
    getEffectFnRefs = (context, node) => {
      const effectFn = getEffectFn(node);
      if (!effectFn) {
        return null;
      }
      return getDownstreamRefs(context, effectFn);
    };
    isFnRef = (ref) => getCallExpr(ref) !== void 0;
    isStateSetter = (context, ref) => isFnRef(ref) && getUpstreamReactVariables(context, ref.identifier).notEmptyEvery(
      (variable) => isState(variable)
    );
    isPropCallback = (context, ref) => isFnRef(ref) && getUpstreamReactVariables(context, ref.identifier).notEmptyEvery(
      (variable) => isProp(variable)
    );
    isState = (variable) => variable.defs.some((def) => isUseState(def.node));
    isProp = (variable) => variable.defs.some(
      (def) => def.type === "Parameter" && (isReactFunctionalComponent(getDeclNode(def.node)) || isCustomHook(getDeclNode(def.node)))
    );
    isHOCProp = (variable) => variable.defs.some(
      (def) => def.type === "Parameter" && isReactFunctionalHOC(getDeclNode(def.node))
    );
    getDeclNode = (node) => node.type === "ArrowFunctionExpression" ? node.parent.type === "CallExpression" ? node.parent.parent : node.parent : node;
    getUseStateNode = (context, ref) => {
      return getUpstreamReactVariables(context, ref.identifier).find((variable) => isState(variable))?.defs.find((def) => isUseState(def.node))?.node;
    };
    isDirectCall = (node) => {
      if (!node) {
        return false;
      } else if ((node.type === "ArrowFunctionExpression" || node.type === "FunctionExpression") && !isIIFE(node.parent)) {
        return isUseEffect(node.parent);
      } else {
        return isDirectCall(node.parent);
      }
    };
    findPropUsedToResetAllState = (context, effectFnRefs, depsRefs, useEffectNode) => {
      const stateSetterRefs = effectFnRefs.filter(
        (ref) => isStateSetter(context, ref)
      );
      const isAllStateReset = stateSetterRefs.length > 0 && stateSetterRefs.every((ref) => isSetStateToInitialValue(context, ref)) && stateSetterRefs.length === countUseStates(context, findContainingNode(useEffectNode));
      return isAllStateReset ? depsRefs.find((ref) => isProp(ref.resolved)) : void 0;
    };
    isSetStateToInitialValue = (context, setterRef) => {
      const setStateToValue = getCallExpr(setterRef).arguments[0];
      const stateInitialValue = getUseStateNode(context, setterRef).init.arguments[0];
      const isUndefined = (node) => node === void 0 || node.name === "undefined";
      if (isUndefined(setStateToValue) && isUndefined(stateInitialValue)) {
        return true;
      }
      if (setStateToValue === null && stateInitialValue === null) {
        return true;
      } else if (setStateToValue && !stateInitialValue || !setStateToValue && stateInitialValue) {
        return false;
      }
      return context.sourceCode.getText(setStateToValue) === context.sourceCode.getText(stateInitialValue);
    };
    countUseStates = (context, componentNode) => {
      let count = 0;
      traverse(context, componentNode, (node) => {
        if (isUseState(node)) {
          count++;
        }
      });
      return count;
    };
    countStateSetterCalls = (stateSetterRef) => stateSetterRef.resolved.references.length - 1;
    findContainingNode = (node) => {
      if (!node) {
        return void 0;
      } else if (isReactFunctionalComponent(node) || isReactFunctionalHOC(node) || isCustomHook(node)) {
        return node;
      } else {
        return findContainingNode(node.parent);
      }
    };
    getUpstreamReactVariables = (context, node) => getUpstreamVariables(
      context,
      node,
      // Stop at the *usage* of `useState` - don't go up to the `useState` variable.
      // Not needed for props - they don't go "too far".
      // We could remove this and check for the `useState` variable instead,
      // but then all our tests need to import it so we can traverse up to it.
      // And would need to change `getUseStateNode()` too?
      // TODO: Could probably organize these filters better.
      (node2) => !isUseState(node2)
    ).filter(
      (variable) => isProp(variable) || variable.defs.every((def) => def.type !== "Parameter")
    );
  }
});

// src/util/javascript.js
var arraysEqual;
var init_javascript = __esm({
  "src/util/javascript.js"() {
    Array.prototype.notEmptyEvery = function(predicate) {
      return this.length > 0 && this.every(predicate);
    };
    arraysEqual = (arr1, arr2) => {
      if (arr1.length !== arr2.length) {
        return false;
      }
      return arr1.every((element, index) => element === arr2[index]);
    };
  }
});

// src/rule.js
var name, rule;
var init_rule = __esm({
  "src/rule.js"() {
    init_messages();
    init_ast();
    init_react();
    init_javascript();
    name = "you-might-not-need-an-effect";
    rule = {
      meta: {
        type: "suggestion",
        docs: {
          description: "Catch unnecessary React useEffect hooks.",
          url: "https://react.dev/learn/you-might-not-need-an-effect"
        },
        schema: [],
        messages
      },
      create: (context) => ({
        CallExpression: (node) => {
          if (!isUseEffect(node)) {
            return;
          }
          const effectFnRefs = getEffectFnRefs(context, node);
          const depsRefs = getDependenciesRefs(context, node);
          if (!effectFnRefs || !depsRefs) {
            return;
          } else if (effectFnRefs.length === 0) {
            context.report({
              node,
              messageId: messageIds.avoidEmptyEffect
            });
            return;
          }
          const propUsedToResetAllState = findPropUsedToResetAllState(
            context,
            effectFnRefs,
            depsRefs,
            node
          );
          if (propUsedToResetAllState) {
            const propName = propUsedToResetAllState.identifier.name;
            context.report({
              node,
              messageId: messageIds.avoidResettingStateFromProps,
              data: { prop: propName }
            });
            return;
          }
          effectFnRefs.filter(
            (ref) => isStateSetter(context, ref) || isPropCallback(context, ref) && // Don't analyze HOC prop callbacks -- we don't have control over them to lift state or logic
            !isHOCProp(ref.resolved)
          ).filter((ref) => isDirectCall(ref.identifier)).forEach((ref) => {
            const callExpr = getCallExpr(ref);
            if (isStateSetter(context, ref)) {
              const useStateNode = getUseStateNode(context, ref);
              const stateName = (useStateNode.id.elements[0] ?? useStateNode.id.elements[1])?.name;
              if (depsRefs.length === 0) {
                context.report({
                  node: callExpr,
                  messageId: messageIds.avoidInitializingState,
                  data: { state: stateName }
                });
              }
              const isAllArgsInternal = callExpr.arguments.flatMap((arg) => getDownstreamRefs(context, arg)).flatMap(
                (ref2) => getUpstreamReactVariables(context, ref2.identifier)
              ).notEmptyEvery(
                (variable) => isState(variable) || isProp(variable) && !isHOCProp(variable)
              );
              const isSomeArgsExternal = callExpr.arguments.flatMap((arg) => getDownstreamRefs(context, arg)).flatMap(
                (ref2) => getUpstreamReactVariables(context, ref2.identifier)
              ).some(
                (variable) => !isState(variable) && !isProp(variable) || isHOCProp(variable)
              );
              const isAllArgsInDeps = callExpr.arguments.flatMap((arg) => getDownstreamRefs(context, arg)).filter(
                (ref2) => ref2.resolved.defs.every((def) => def.type !== "Parameter")
              ).notEmptyEvery(
                (argRef) => depsRefs.some(
                  (depRef) => (
                    // If they have the same upstream variables, they're equivalent
                    arraysEqual(
                      getUpstreamReactVariables(context, argRef.identifier),
                      getUpstreamReactVariables(context, depRef.identifier)
                    )
                  )
                )
              );
              const isAllDepsInternal = depsRefs.flatMap(
                (ref2) => getUpstreamReactVariables(context, ref2.identifier)
              ).notEmptyEvery(
                (variable) => isState(variable) || isProp(variable) && !isHOCProp(variable)
              );
              if (isAllArgsInternal || // They are always in sync, regardless of source - could compute during render
              // TODO: Should we *always* check that the args are in deps?
              // Should/could that replace isArgsInternal?
              // Should it be chained state when not?
              isAllArgsInDeps && countStateSetterCalls(ref) === 1) {
                context.report({
                  node: callExpr,
                  messageId: messageIds.avoidDerivedState,
                  data: { state: stateName }
                });
              }
              if (!isAllArgsInternal && !isSomeArgsExternal && isAllDepsInternal) {
                context.report({
                  node: callExpr,
                  messageId: messageIds.avoidChainingState
                });
              }
            } else if (isPropCallback(context, ref)) {
              context.report({
                node: callExpr,
                messageId: messageIds.avoidParentChildCoupling
              });
            }
          });
        }
      })
    };
  }
});

// node_modules/globals/globals.json
var require_globals = __commonJS({
  "node_modules/globals/globals.json"(exports2, module2) {
    module2.exports = {
      amd: {
        define: false,
        require: false
      },
      applescript: {
        $: false,
        Application: false,
        Automation: false,
        console: false,
        delay: false,
        Library: false,
        ObjC: false,
        ObjectSpecifier: false,
        Path: false,
        Progress: false,
        Ref: false
      },
      atomtest: {
        advanceClock: false,
        atom: false,
        fakeClearInterval: false,
        fakeClearTimeout: false,
        fakeSetInterval: false,
        fakeSetTimeout: false,
        resetTimeouts: false,
        waitsForPromise: false
      },
      browser: {
        AbortController: false,
        AbortSignal: false,
        AbsoluteOrientationSensor: false,
        AbstractRange: false,
        Accelerometer: false,
        addEventListener: false,
        ai: false,
        AI: false,
        AICreateMonitor: false,
        AITextSession: false,
        alert: false,
        AnalyserNode: false,
        Animation: false,
        AnimationEffect: false,
        AnimationEvent: false,
        AnimationPlaybackEvent: false,
        AnimationTimeline: false,
        AsyncDisposableStack: false,
        atob: false,
        Attr: false,
        Audio: false,
        AudioBuffer: false,
        AudioBufferSourceNode: false,
        AudioContext: false,
        AudioData: false,
        AudioDecoder: false,
        AudioDestinationNode: false,
        AudioEncoder: false,
        AudioListener: false,
        AudioNode: false,
        AudioParam: false,
        AudioParamMap: false,
        AudioProcessingEvent: false,
        AudioScheduledSourceNode: false,
        AudioSinkInfo: false,
        AudioWorklet: false,
        AudioWorkletGlobalScope: false,
        AudioWorkletNode: false,
        AudioWorkletProcessor: false,
        AuthenticatorAssertionResponse: false,
        AuthenticatorAttestationResponse: false,
        AuthenticatorResponse: false,
        BackgroundFetchManager: false,
        BackgroundFetchRecord: false,
        BackgroundFetchRegistration: false,
        BarcodeDetector: false,
        BarProp: false,
        BaseAudioContext: false,
        BatteryManager: false,
        BeforeUnloadEvent: false,
        BiquadFilterNode: false,
        Blob: false,
        BlobEvent: false,
        Bluetooth: false,
        BluetoothCharacteristicProperties: false,
        BluetoothDevice: false,
        BluetoothRemoteGATTCharacteristic: false,
        BluetoothRemoteGATTDescriptor: false,
        BluetoothRemoteGATTServer: false,
        BluetoothRemoteGATTService: false,
        BluetoothUUID: false,
        blur: false,
        BroadcastChannel: false,
        BrowserCaptureMediaStreamTrack: false,
        btoa: false,
        ByteLengthQueuingStrategy: false,
        Cache: false,
        caches: false,
        CacheStorage: false,
        cancelAnimationFrame: false,
        cancelIdleCallback: false,
        CanvasCaptureMediaStream: false,
        CanvasCaptureMediaStreamTrack: false,
        CanvasGradient: false,
        CanvasPattern: false,
        CanvasRenderingContext2D: false,
        CaptureController: false,
        CaretPosition: false,
        CDATASection: false,
        ChannelMergerNode: false,
        ChannelSplitterNode: false,
        ChapterInformation: false,
        CharacterBoundsUpdateEvent: false,
        CharacterData: false,
        clearInterval: false,
        clearTimeout: false,
        clientInformation: false,
        Clipboard: false,
        ClipboardEvent: false,
        ClipboardItem: false,
        close: false,
        closed: false,
        CloseEvent: false,
        CloseWatcher: false,
        CommandEvent: false,
        Comment: false,
        CompositionEvent: false,
        CompressionStream: false,
        confirm: false,
        console: false,
        ConstantSourceNode: false,
        ContentVisibilityAutoStateChangeEvent: false,
        ConvolverNode: false,
        CookieChangeEvent: false,
        CookieDeprecationLabel: false,
        cookieStore: false,
        CookieStore: false,
        CookieStoreManager: false,
        CountQueuingStrategy: false,
        createImageBitmap: false,
        Credential: false,
        credentialless: false,
        CredentialsContainer: false,
        CropTarget: false,
        crossOriginIsolated: false,
        crypto: false,
        Crypto: false,
        CryptoKey: false,
        CSPViolationReportBody: false,
        CSS: false,
        CSSAnimation: false,
        CSSConditionRule: false,
        CSSContainerRule: false,
        CSSCounterStyleRule: false,
        CSSFontFaceRule: false,
        CSSFontFeatureValuesRule: false,
        CSSFontPaletteValuesRule: false,
        CSSGroupingRule: false,
        CSSImageValue: false,
        CSSImportRule: false,
        CSSKeyframeRule: false,
        CSSKeyframesRule: false,
        CSSKeywordValue: false,
        CSSLayerBlockRule: false,
        CSSLayerStatementRule: false,
        CSSMarginRule: false,
        CSSMathClamp: false,
        CSSMathInvert: false,
        CSSMathMax: false,
        CSSMathMin: false,
        CSSMathNegate: false,
        CSSMathProduct: false,
        CSSMathSum: false,
        CSSMathValue: false,
        CSSMatrixComponent: false,
        CSSMediaRule: false,
        CSSNamespaceRule: false,
        CSSNestedDeclarations: false,
        CSSNumericArray: false,
        CSSNumericValue: false,
        CSSPageDescriptors: false,
        CSSPageRule: false,
        CSSPerspective: false,
        CSSPositionTryDescriptors: false,
        CSSPositionTryRule: false,
        CSSPositionValue: false,
        CSSPropertyRule: false,
        CSSRotate: false,
        CSSRule: false,
        CSSRuleList: false,
        CSSScale: false,
        CSSScopeRule: false,
        CSSSkew: false,
        CSSSkewX: false,
        CSSSkewY: false,
        CSSStartingStyleRule: false,
        CSSStyleDeclaration: false,
        CSSStyleRule: false,
        CSSStyleSheet: false,
        CSSStyleValue: false,
        CSSSupportsRule: false,
        CSSTransformComponent: false,
        CSSTransformValue: false,
        CSSTransition: false,
        CSSTranslate: false,
        CSSUnitValue: false,
        CSSUnparsedValue: false,
        CSSVariableReferenceValue: false,
        CSSViewTransitionRule: false,
        currentFrame: false,
        currentTime: false,
        CustomElementRegistry: false,
        customElements: false,
        CustomEvent: false,
        CustomStateSet: false,
        DataTransfer: false,
        DataTransferItem: false,
        DataTransferItemList: false,
        DecompressionStream: false,
        DelayNode: false,
        DelegatedInkTrailPresenter: false,
        DeviceMotionEvent: false,
        DeviceMotionEventAcceleration: false,
        DeviceMotionEventRotationRate: false,
        DeviceOrientationEvent: false,
        devicePixelRatio: false,
        DevicePosture: false,
        dispatchEvent: false,
        DisposableStack: false,
        document: false,
        Document: false,
        DocumentFragment: false,
        documentPictureInPicture: false,
        DocumentPictureInPicture: false,
        DocumentPictureInPictureEvent: false,
        DocumentTimeline: false,
        DocumentType: false,
        DOMError: false,
        DOMException: false,
        DOMImplementation: false,
        DOMMatrix: false,
        DOMMatrixReadOnly: false,
        DOMParser: false,
        DOMPoint: false,
        DOMPointReadOnly: false,
        DOMQuad: false,
        DOMRect: false,
        DOMRectList: false,
        DOMRectReadOnly: false,
        DOMStringList: false,
        DOMStringMap: false,
        DOMTokenList: false,
        DragEvent: false,
        DynamicsCompressorNode: false,
        EditContext: false,
        Element: false,
        ElementInternals: false,
        EncodedAudioChunk: false,
        EncodedVideoChunk: false,
        ErrorEvent: false,
        event: false,
        Event: false,
        EventCounts: false,
        EventSource: false,
        EventTarget: false,
        external: false,
        External: false,
        EyeDropper: false,
        FeaturePolicy: false,
        FederatedCredential: false,
        fence: false,
        Fence: false,
        FencedFrameConfig: false,
        fetch: false,
        fetchLater: false,
        FetchLaterResult: false,
        File: false,
        FileList: false,
        FileReader: false,
        FileSystem: false,
        FileSystemDirectoryEntry: false,
        FileSystemDirectoryHandle: false,
        FileSystemDirectoryReader: false,
        FileSystemEntry: false,
        FileSystemFileEntry: false,
        FileSystemFileHandle: false,
        FileSystemHandle: false,
        FileSystemObserver: false,
        FileSystemWritableFileStream: false,
        find: false,
        focus: false,
        FocusEvent: false,
        FontData: false,
        FontFace: false,
        FontFaceSet: false,
        FontFaceSetLoadEvent: false,
        FormData: false,
        FormDataEvent: false,
        FragmentDirective: false,
        frameElement: false,
        frames: false,
        GainNode: false,
        Gamepad: false,
        GamepadAxisMoveEvent: false,
        GamepadButton: false,
        GamepadButtonEvent: false,
        GamepadEvent: false,
        GamepadHapticActuator: false,
        GamepadPose: false,
        Geolocation: false,
        GeolocationCoordinates: false,
        GeolocationPosition: false,
        GeolocationPositionError: false,
        getComputedStyle: false,
        getScreenDetails: false,
        getSelection: false,
        GPU: false,
        GPUAdapter: false,
        GPUAdapterInfo: false,
        GPUBindGroup: false,
        GPUBindGroupLayout: false,
        GPUBuffer: false,
        GPUBufferUsage: false,
        GPUCanvasContext: false,
        GPUColorWrite: false,
        GPUCommandBuffer: false,
        GPUCommandEncoder: false,
        GPUCompilationInfo: false,
        GPUCompilationMessage: false,
        GPUComputePassEncoder: false,
        GPUComputePipeline: false,
        GPUDevice: false,
        GPUDeviceLostInfo: false,
        GPUError: false,
        GPUExternalTexture: false,
        GPUInternalError: false,
        GPUMapMode: false,
        GPUOutOfMemoryError: false,
        GPUPipelineError: false,
        GPUPipelineLayout: false,
        GPUQuerySet: false,
        GPUQueue: false,
        GPURenderBundle: false,
        GPURenderBundleEncoder: false,
        GPURenderPassEncoder: false,
        GPURenderPipeline: false,
        GPUSampler: false,
        GPUShaderModule: false,
        GPUShaderStage: false,
        GPUSupportedFeatures: false,
        GPUSupportedLimits: false,
        GPUTexture: false,
        GPUTextureUsage: false,
        GPUTextureView: false,
        GPUUncapturedErrorEvent: false,
        GPUValidationError: false,
        GravitySensor: false,
        Gyroscope: false,
        HashChangeEvent: false,
        Headers: false,
        HID: false,
        HIDConnectionEvent: false,
        HIDDevice: false,
        HIDInputReportEvent: false,
        Highlight: false,
        HighlightRegistry: false,
        history: false,
        History: false,
        HTMLAllCollection: false,
        HTMLAnchorElement: false,
        HTMLAreaElement: false,
        HTMLAudioElement: false,
        HTMLBaseElement: false,
        HTMLBodyElement: false,
        HTMLBRElement: false,
        HTMLButtonElement: false,
        HTMLCanvasElement: false,
        HTMLCollection: false,
        HTMLDataElement: false,
        HTMLDataListElement: false,
        HTMLDetailsElement: false,
        HTMLDialogElement: false,
        HTMLDirectoryElement: false,
        HTMLDivElement: false,
        HTMLDListElement: false,
        HTMLDocument: false,
        HTMLElement: false,
        HTMLEmbedElement: false,
        HTMLFencedFrameElement: false,
        HTMLFieldSetElement: false,
        HTMLFontElement: false,
        HTMLFormControlsCollection: false,
        HTMLFormElement: false,
        HTMLFrameElement: false,
        HTMLFrameSetElement: false,
        HTMLHeadElement: false,
        HTMLHeadingElement: false,
        HTMLHRElement: false,
        HTMLHtmlElement: false,
        HTMLIFrameElement: false,
        HTMLImageElement: false,
        HTMLInputElement: false,
        HTMLLabelElement: false,
        HTMLLegendElement: false,
        HTMLLIElement: false,
        HTMLLinkElement: false,
        HTMLMapElement: false,
        HTMLMarqueeElement: false,
        HTMLMediaElement: false,
        HTMLMenuElement: false,
        HTMLMetaElement: false,
        HTMLMeterElement: false,
        HTMLModElement: false,
        HTMLObjectElement: false,
        HTMLOListElement: false,
        HTMLOptGroupElement: false,
        HTMLOptionElement: false,
        HTMLOptionsCollection: false,
        HTMLOutputElement: false,
        HTMLParagraphElement: false,
        HTMLParamElement: false,
        HTMLPictureElement: false,
        HTMLPreElement: false,
        HTMLProgressElement: false,
        HTMLQuoteElement: false,
        HTMLScriptElement: false,
        HTMLSelectedContentElement: false,
        HTMLSelectElement: false,
        HTMLSlotElement: false,
        HTMLSourceElement: false,
        HTMLSpanElement: false,
        HTMLStyleElement: false,
        HTMLTableCaptionElement: false,
        HTMLTableCellElement: false,
        HTMLTableColElement: false,
        HTMLTableElement: false,
        HTMLTableRowElement: false,
        HTMLTableSectionElement: false,
        HTMLTemplateElement: false,
        HTMLTextAreaElement: false,
        HTMLTimeElement: false,
        HTMLTitleElement: false,
        HTMLTrackElement: false,
        HTMLUListElement: false,
        HTMLUnknownElement: false,
        HTMLVideoElement: false,
        IDBCursor: false,
        IDBCursorWithValue: false,
        IDBDatabase: false,
        IDBFactory: false,
        IDBIndex: false,
        IDBKeyRange: false,
        IDBObjectStore: false,
        IDBOpenDBRequest: false,
        IDBRequest: false,
        IDBTransaction: false,
        IDBVersionChangeEvent: false,
        IdentityCredential: false,
        IdentityCredentialError: false,
        IdentityProvider: false,
        IdleDeadline: false,
        IdleDetector: false,
        IIRFilterNode: false,
        Image: false,
        ImageBitmap: false,
        ImageBitmapRenderingContext: false,
        ImageCapture: false,
        ImageData: false,
        ImageDecoder: false,
        ImageTrack: false,
        ImageTrackList: false,
        indexedDB: false,
        Ink: false,
        innerHeight: false,
        innerWidth: false,
        InputDeviceCapabilities: false,
        InputDeviceInfo: false,
        InputEvent: false,
        IntersectionObserver: false,
        IntersectionObserverEntry: false,
        isSecureContext: false,
        Keyboard: false,
        KeyboardEvent: false,
        KeyboardLayoutMap: false,
        KeyframeEffect: false,
        LanguageDetector: false,
        LargestContentfulPaint: false,
        LaunchParams: false,
        launchQueue: false,
        LaunchQueue: false,
        LayoutShift: false,
        LayoutShiftAttribution: false,
        length: false,
        LinearAccelerationSensor: false,
        localStorage: false,
        location: true,
        Location: false,
        locationbar: false,
        Lock: false,
        LockManager: false,
        matchMedia: false,
        MathMLElement: false,
        MediaCapabilities: false,
        MediaCapabilitiesInfo: false,
        MediaDeviceInfo: false,
        MediaDevices: false,
        MediaElementAudioSourceNode: false,
        MediaEncryptedEvent: false,
        MediaError: false,
        MediaKeyError: false,
        MediaKeyMessageEvent: false,
        MediaKeys: false,
        MediaKeySession: false,
        MediaKeyStatusMap: false,
        MediaKeySystemAccess: false,
        MediaList: false,
        MediaMetadata: false,
        MediaQueryList: false,
        MediaQueryListEvent: false,
        MediaRecorder: false,
        MediaRecorderErrorEvent: false,
        MediaSession: false,
        MediaSource: false,
        MediaSourceHandle: false,
        MediaStream: false,
        MediaStreamAudioDestinationNode: false,
        MediaStreamAudioSourceNode: false,
        MediaStreamEvent: false,
        MediaStreamTrack: false,
        MediaStreamTrackAudioSourceNode: false,
        MediaStreamTrackAudioStats: false,
        MediaStreamTrackEvent: false,
        MediaStreamTrackGenerator: false,
        MediaStreamTrackProcessor: false,
        MediaStreamTrackVideoStats: false,
        menubar: false,
        MessageChannel: false,
        MessageEvent: false,
        MessagePort: false,
        MIDIAccess: false,
        MIDIConnectionEvent: false,
        MIDIInput: false,
        MIDIInputMap: false,
        MIDIMessageEvent: false,
        MIDIOutput: false,
        MIDIOutputMap: false,
        MIDIPort: false,
        MimeType: false,
        MimeTypeArray: false,
        model: false,
        ModelGenericSession: false,
        ModelManager: false,
        MouseEvent: false,
        moveBy: false,
        moveTo: false,
        MutationEvent: false,
        MutationObserver: false,
        MutationRecord: false,
        name: false,
        NamedNodeMap: false,
        NavigateEvent: false,
        navigation: false,
        Navigation: false,
        NavigationActivation: false,
        NavigationCurrentEntryChangeEvent: false,
        NavigationDestination: false,
        NavigationHistoryEntry: false,
        NavigationPreloadManager: false,
        NavigationTransition: false,
        navigator: false,
        Navigator: false,
        NavigatorLogin: false,
        NavigatorManagedData: false,
        NavigatorUAData: false,
        NetworkInformation: false,
        Node: false,
        NodeFilter: false,
        NodeIterator: false,
        NodeList: false,
        Notification: false,
        NotifyPaintEvent: false,
        NotRestoredReasonDetails: false,
        NotRestoredReasons: false,
        Observable: false,
        OfflineAudioCompletionEvent: false,
        OfflineAudioContext: false,
        offscreenBuffering: false,
        OffscreenCanvas: false,
        OffscreenCanvasRenderingContext2D: false,
        onabort: true,
        onafterprint: true,
        onanimationcancel: true,
        onanimationend: true,
        onanimationiteration: true,
        onanimationstart: true,
        onappinstalled: true,
        onauxclick: true,
        onbeforeinput: true,
        onbeforeinstallprompt: true,
        onbeforematch: true,
        onbeforeprint: true,
        onbeforetoggle: true,
        onbeforeunload: true,
        onbeforexrselect: true,
        onblur: true,
        oncancel: true,
        oncanplay: true,
        oncanplaythrough: true,
        onchange: true,
        onclick: true,
        onclose: true,
        oncommand: true,
        oncontentvisibilityautostatechange: true,
        oncontextlost: true,
        oncontextmenu: true,
        oncontextrestored: true,
        oncopy: true,
        oncuechange: true,
        oncut: true,
        ondblclick: true,
        ondevicemotion: true,
        ondeviceorientation: true,
        ondeviceorientationabsolute: true,
        ondrag: true,
        ondragend: true,
        ondragenter: true,
        ondragleave: true,
        ondragover: true,
        ondragstart: true,
        ondrop: true,
        ondurationchange: true,
        onemptied: true,
        onended: true,
        onerror: true,
        onfocus: true,
        onformdata: true,
        ongamepadconnected: true,
        ongamepaddisconnected: true,
        ongotpointercapture: true,
        onhashchange: true,
        oninput: true,
        oninvalid: true,
        onkeydown: true,
        onkeypress: true,
        onkeyup: true,
        onlanguagechange: true,
        onload: true,
        onloadeddata: true,
        onloadedmetadata: true,
        onloadstart: true,
        onlostpointercapture: true,
        onmessage: true,
        onmessageerror: true,
        onmousedown: true,
        onmouseenter: true,
        onmouseleave: true,
        onmousemove: true,
        onmouseout: true,
        onmouseover: true,
        onmouseup: true,
        onmousewheel: true,
        onoffline: true,
        ononline: true,
        onpagehide: true,
        onpagereveal: true,
        onpageshow: true,
        onpageswap: true,
        onpaste: true,
        onpause: true,
        onplay: true,
        onplaying: true,
        onpointercancel: true,
        onpointerdown: true,
        onpointerenter: true,
        onpointerleave: true,
        onpointermove: true,
        onpointerout: true,
        onpointerover: true,
        onpointerrawupdate: true,
        onpointerup: true,
        onpopstate: true,
        onprogress: true,
        onratechange: true,
        onrejectionhandled: true,
        onreset: true,
        onresize: true,
        onscroll: true,
        onscrollend: true,
        onscrollsnapchange: true,
        onscrollsnapchanging: true,
        onsearch: true,
        onsecuritypolicyviolation: true,
        onseeked: true,
        onseeking: true,
        onselect: true,
        onselectionchange: true,
        onselectstart: true,
        onslotchange: true,
        onstalled: true,
        onstorage: true,
        onsubmit: true,
        onsuspend: true,
        ontimeupdate: true,
        ontoggle: true,
        ontransitioncancel: true,
        ontransitionend: true,
        ontransitionrun: true,
        ontransitionstart: true,
        onunhandledrejection: true,
        onunload: true,
        onvolumechange: true,
        onwaiting: true,
        onwheel: true,
        open: false,
        opener: false,
        Option: false,
        OrientationSensor: false,
        origin: false,
        originAgentCluster: false,
        OscillatorNode: false,
        OTPCredential: false,
        outerHeight: false,
        outerWidth: false,
        OverconstrainedError: false,
        PageRevealEvent: false,
        PageSwapEvent: false,
        PageTransitionEvent: false,
        pageXOffset: false,
        pageYOffset: false,
        PannerNode: false,
        parent: false,
        PasswordCredential: false,
        Path2D: false,
        PaymentAddress: false,
        PaymentManager: false,
        PaymentMethodChangeEvent: false,
        PaymentRequest: false,
        PaymentRequestUpdateEvent: false,
        PaymentResponse: false,
        performance: false,
        Performance: false,
        PerformanceElementTiming: false,
        PerformanceEntry: false,
        PerformanceEventTiming: false,
        PerformanceLongAnimationFrameTiming: false,
        PerformanceLongTaskTiming: false,
        PerformanceMark: false,
        PerformanceMeasure: false,
        PerformanceNavigation: false,
        PerformanceNavigationTiming: false,
        PerformanceObserver: false,
        PerformanceObserverEntryList: false,
        PerformancePaintTiming: false,
        PerformanceResourceTiming: false,
        PerformanceScriptTiming: false,
        PerformanceServerTiming: false,
        PerformanceTiming: false,
        PeriodicSyncManager: false,
        PeriodicWave: false,
        Permissions: false,
        PermissionStatus: false,
        PERSISTENT: false,
        personalbar: false,
        PictureInPictureEvent: false,
        PictureInPictureWindow: false,
        Plugin: false,
        PluginArray: false,
        PointerEvent: false,
        PopStateEvent: false,
        postMessage: false,
        Presentation: false,
        PresentationAvailability: false,
        PresentationConnection: false,
        PresentationConnectionAvailableEvent: false,
        PresentationConnectionCloseEvent: false,
        PresentationConnectionList: false,
        PresentationReceiver: false,
        PresentationRequest: false,
        PressureObserver: false,
        PressureRecord: false,
        print: false,
        ProcessingInstruction: false,
        Profiler: false,
        ProgressEvent: false,
        PromiseRejectionEvent: false,
        prompt: false,
        ProtectedAudience: false,
        PublicKeyCredential: false,
        PushManager: false,
        PushSubscription: false,
        PushSubscriptionOptions: false,
        queryLocalFonts: false,
        queueMicrotask: false,
        RadioNodeList: false,
        Range: false,
        ReadableByteStreamController: false,
        ReadableStream: false,
        ReadableStreamBYOBReader: false,
        ReadableStreamBYOBRequest: false,
        ReadableStreamDefaultController: false,
        ReadableStreamDefaultReader: false,
        registerProcessor: false,
        RelativeOrientationSensor: false,
        RemotePlayback: false,
        removeEventListener: false,
        ReportBody: false,
        reportError: false,
        ReportingObserver: false,
        Request: false,
        requestAnimationFrame: false,
        requestIdleCallback: false,
        resizeBy: false,
        ResizeObserver: false,
        ResizeObserverEntry: false,
        ResizeObserverSize: false,
        resizeTo: false,
        Response: false,
        RestrictionTarget: false,
        RTCCertificate: false,
        RTCDataChannel: false,
        RTCDataChannelEvent: false,
        RTCDtlsTransport: false,
        RTCDTMFSender: false,
        RTCDTMFToneChangeEvent: false,
        RTCEncodedAudioFrame: false,
        RTCEncodedVideoFrame: false,
        RTCError: false,
        RTCErrorEvent: false,
        RTCIceCandidate: false,
        RTCIceTransport: false,
        RTCPeerConnection: false,
        RTCPeerConnectionIceErrorEvent: false,
        RTCPeerConnectionIceEvent: false,
        RTCRtpReceiver: false,
        RTCRtpScriptTransform: false,
        RTCRtpSender: false,
        RTCRtpTransceiver: false,
        RTCSctpTransport: false,
        RTCSessionDescription: false,
        RTCStatsReport: false,
        RTCTrackEvent: false,
        sampleRate: false,
        scheduler: false,
        Scheduler: false,
        Scheduling: false,
        screen: false,
        Screen: false,
        ScreenDetailed: false,
        ScreenDetails: false,
        screenLeft: false,
        ScreenOrientation: false,
        screenTop: false,
        screenX: false,
        screenY: false,
        ScriptProcessorNode: false,
        scroll: false,
        scrollbars: false,
        scrollBy: false,
        ScrollTimeline: false,
        scrollTo: false,
        scrollX: false,
        scrollY: false,
        SecurityPolicyViolationEvent: false,
        Selection: false,
        self: false,
        Sensor: false,
        SensorErrorEvent: false,
        Serial: false,
        SerialPort: false,
        ServiceWorker: false,
        ServiceWorkerContainer: false,
        ServiceWorkerRegistration: false,
        sessionStorage: false,
        setInterval: false,
        setTimeout: false,
        ShadowRoot: false,
        sharedStorage: false,
        SharedStorage: false,
        SharedStorageAppendMethod: false,
        SharedStorageClearMethod: false,
        SharedStorageDeleteMethod: false,
        SharedStorageModifierMethod: false,
        SharedStorageSetMethod: false,
        SharedStorageWorklet: false,
        SharedWorker: false,
        showDirectoryPicker: false,
        showOpenFilePicker: false,
        showSaveFilePicker: false,
        SnapEvent: false,
        SourceBuffer: false,
        SourceBufferList: false,
        speechSynthesis: false,
        SpeechSynthesis: false,
        SpeechSynthesisErrorEvent: false,
        SpeechSynthesisEvent: false,
        SpeechSynthesisUtterance: false,
        SpeechSynthesisVoice: false,
        StaticRange: false,
        status: false,
        statusbar: false,
        StereoPannerNode: false,
        stop: false,
        Storage: false,
        StorageBucket: false,
        StorageBucketManager: false,
        StorageEvent: false,
        StorageManager: false,
        structuredClone: false,
        styleMedia: false,
        StylePropertyMap: false,
        StylePropertyMapReadOnly: false,
        StyleSheet: false,
        StyleSheetList: false,
        SubmitEvent: false,
        Subscriber: false,
        SubtleCrypto: false,
        SuppressedError: false,
        SVGAElement: false,
        SVGAngle: false,
        SVGAnimatedAngle: false,
        SVGAnimatedBoolean: false,
        SVGAnimatedEnumeration: false,
        SVGAnimatedInteger: false,
        SVGAnimatedLength: false,
        SVGAnimatedLengthList: false,
        SVGAnimatedNumber: false,
        SVGAnimatedNumberList: false,
        SVGAnimatedPreserveAspectRatio: false,
        SVGAnimatedRect: false,
        SVGAnimatedString: false,
        SVGAnimatedTransformList: false,
        SVGAnimateElement: false,
        SVGAnimateMotionElement: false,
        SVGAnimateTransformElement: false,
        SVGAnimationElement: false,
        SVGCircleElement: false,
        SVGClipPathElement: false,
        SVGComponentTransferFunctionElement: false,
        SVGDefsElement: false,
        SVGDescElement: false,
        SVGElement: false,
        SVGEllipseElement: false,
        SVGFEBlendElement: false,
        SVGFEColorMatrixElement: false,
        SVGFEComponentTransferElement: false,
        SVGFECompositeElement: false,
        SVGFEConvolveMatrixElement: false,
        SVGFEDiffuseLightingElement: false,
        SVGFEDisplacementMapElement: false,
        SVGFEDistantLightElement: false,
        SVGFEDropShadowElement: false,
        SVGFEFloodElement: false,
        SVGFEFuncAElement: false,
        SVGFEFuncBElement: false,
        SVGFEFuncGElement: false,
        SVGFEFuncRElement: false,
        SVGFEGaussianBlurElement: false,
        SVGFEImageElement: false,
        SVGFEMergeElement: false,
        SVGFEMergeNodeElement: false,
        SVGFEMorphologyElement: false,
        SVGFEOffsetElement: false,
        SVGFEPointLightElement: false,
        SVGFESpecularLightingElement: false,
        SVGFESpotLightElement: false,
        SVGFETileElement: false,
        SVGFETurbulenceElement: false,
        SVGFilterElement: false,
        SVGForeignObjectElement: false,
        SVGGElement: false,
        SVGGeometryElement: false,
        SVGGradientElement: false,
        SVGGraphicsElement: false,
        SVGImageElement: false,
        SVGLength: false,
        SVGLengthList: false,
        SVGLinearGradientElement: false,
        SVGLineElement: false,
        SVGMarkerElement: false,
        SVGMaskElement: false,
        SVGMatrix: false,
        SVGMetadataElement: false,
        SVGMPathElement: false,
        SVGNumber: false,
        SVGNumberList: false,
        SVGPathElement: false,
        SVGPatternElement: false,
        SVGPoint: false,
        SVGPointList: false,
        SVGPolygonElement: false,
        SVGPolylineElement: false,
        SVGPreserveAspectRatio: false,
        SVGRadialGradientElement: false,
        SVGRect: false,
        SVGRectElement: false,
        SVGScriptElement: false,
        SVGSetElement: false,
        SVGStopElement: false,
        SVGStringList: false,
        SVGStyleElement: false,
        SVGSVGElement: false,
        SVGSwitchElement: false,
        SVGSymbolElement: false,
        SVGTextContentElement: false,
        SVGTextElement: false,
        SVGTextPathElement: false,
        SVGTextPositioningElement: false,
        SVGTitleElement: false,
        SVGTransform: false,
        SVGTransformList: false,
        SVGTSpanElement: false,
        SVGUnitTypes: false,
        SVGUseElement: false,
        SVGViewElement: false,
        SyncManager: false,
        TaskAttributionTiming: false,
        TaskController: false,
        TaskPriorityChangeEvent: false,
        TaskSignal: false,
        TEMPORARY: false,
        Text: false,
        TextDecoder: false,
        TextDecoderStream: false,
        TextEncoder: false,
        TextEncoderStream: false,
        TextEvent: false,
        TextFormat: false,
        TextFormatUpdateEvent: false,
        TextMetrics: false,
        TextTrack: false,
        TextTrackCue: false,
        TextTrackCueList: false,
        TextTrackList: false,
        TextUpdateEvent: false,
        TimeEvent: false,
        TimeRanges: false,
        ToggleEvent: false,
        toolbar: false,
        top: false,
        Touch: false,
        TouchEvent: false,
        TouchList: false,
        TrackEvent: false,
        TransformStream: false,
        TransformStreamDefaultController: false,
        TransitionEvent: false,
        TreeWalker: false,
        TrustedHTML: false,
        TrustedScript: false,
        TrustedScriptURL: false,
        TrustedTypePolicy: false,
        TrustedTypePolicyFactory: false,
        trustedTypes: false,
        UIEvent: false,
        URL: false,
        URLPattern: false,
        URLSearchParams: false,
        USB: false,
        USBAlternateInterface: false,
        USBConfiguration: false,
        USBConnectionEvent: false,
        USBDevice: false,
        USBEndpoint: false,
        USBInterface: false,
        USBInTransferResult: false,
        USBIsochronousInTransferPacket: false,
        USBIsochronousInTransferResult: false,
        USBIsochronousOutTransferPacket: false,
        USBIsochronousOutTransferResult: false,
        USBOutTransferResult: false,
        UserActivation: false,
        ValidityState: false,
        VideoColorSpace: false,
        VideoDecoder: false,
        VideoEncoder: false,
        VideoFrame: false,
        VideoPlaybackQuality: false,
        ViewTimeline: false,
        ViewTransition: false,
        ViewTransitionTypeSet: false,
        VirtualKeyboard: false,
        VirtualKeyboardGeometryChangeEvent: false,
        VisibilityStateEntry: false,
        visualViewport: false,
        VisualViewport: false,
        VTTCue: false,
        VTTRegion: false,
        WakeLock: false,
        WakeLockSentinel: false,
        WaveShaperNode: false,
        WebAssembly: false,
        WebGL2RenderingContext: false,
        WebGLActiveInfo: false,
        WebGLBuffer: false,
        WebGLContextEvent: false,
        WebGLFramebuffer: false,
        WebGLObject: false,
        WebGLProgram: false,
        WebGLQuery: false,
        WebGLRenderbuffer: false,
        WebGLRenderingContext: false,
        WebGLSampler: false,
        WebGLShader: false,
        WebGLShaderPrecisionFormat: false,
        WebGLSync: false,
        WebGLTexture: false,
        WebGLTransformFeedback: false,
        WebGLUniformLocation: false,
        WebGLVertexArrayObject: false,
        WebSocket: false,
        WebSocketError: false,
        WebSocketStream: false,
        WebTransport: false,
        WebTransportBidirectionalStream: false,
        WebTransportDatagramDuplexStream: false,
        WebTransportError: false,
        WebTransportReceiveStream: false,
        WebTransportSendStream: false,
        WGSLLanguageFeatures: false,
        WheelEvent: false,
        when: false,
        window: false,
        Window: false,
        WindowControlsOverlay: false,
        WindowControlsOverlayGeometryChangeEvent: false,
        Worker: false,
        Worklet: false,
        WorkletGlobalScope: false,
        WritableStream: false,
        WritableStreamDefaultController: false,
        WritableStreamDefaultWriter: false,
        XMLDocument: false,
        XMLHttpRequest: false,
        XMLHttpRequestEventTarget: false,
        XMLHttpRequestUpload: false,
        XMLSerializer: false,
        XPathEvaluator: false,
        XPathExpression: false,
        XPathResult: false,
        XRAnchor: false,
        XRAnchorSet: false,
        XRBoundedReferenceSpace: false,
        XRCamera: false,
        XRCPUDepthInformation: false,
        XRDepthInformation: false,
        XRDOMOverlayState: false,
        XRFrame: false,
        XRHand: false,
        XRHitTestResult: false,
        XRHitTestSource: false,
        XRInputSource: false,
        XRInputSourceArray: false,
        XRInputSourceEvent: false,
        XRInputSourcesChangeEvent: false,
        XRJointPose: false,
        XRJointSpace: false,
        XRLayer: false,
        XRLightEstimate: false,
        XRLightProbe: false,
        XRPose: false,
        XRRay: false,
        XRReferenceSpace: false,
        XRReferenceSpaceEvent: false,
        XRRenderState: false,
        XRRigidTransform: false,
        XRSession: false,
        XRSessionEvent: false,
        XRSpace: false,
        XRSystem: false,
        XRTransientInputHitTestResult: false,
        XRTransientInputHitTestSource: false,
        XRView: false,
        XRViewerPose: false,
        XRViewport: false,
        XRWebGLBinding: false,
        XRWebGLDepthInformation: false,
        XRWebGLLayer: false,
        XSLTProcessor: false
      },
      builtin: {
        AggregateError: false,
        Array: false,
        ArrayBuffer: false,
        Atomics: false,
        BigInt: false,
        BigInt64Array: false,
        BigUint64Array: false,
        Boolean: false,
        DataView: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        FinalizationRegistry: false,
        Float16Array: false,
        Float32Array: false,
        Float64Array: false,
        Function: false,
        globalThis: false,
        Infinity: false,
        Int16Array: false,
        Int32Array: false,
        Int8Array: false,
        Intl: false,
        isFinite: false,
        isNaN: false,
        Iterator: false,
        JSON: false,
        Map: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        Promise: false,
        Proxy: false,
        RangeError: false,
        ReferenceError: false,
        Reflect: false,
        RegExp: false,
        Set: false,
        SharedArrayBuffer: false,
        String: false,
        Symbol: false,
        SyntaxError: false,
        TypeError: false,
        Uint16Array: false,
        Uint32Array: false,
        Uint8Array: false,
        Uint8ClampedArray: false,
        undefined: false,
        unescape: false,
        URIError: false,
        WeakMap: false,
        WeakRef: false,
        WeakSet: false
      },
      chai: {
        assert: true,
        expect: true,
        should: true
      },
      commonjs: {
        exports: true,
        global: false,
        module: false,
        require: false
      },
      couch: {
        emit: false,
        exports: false,
        getRow: false,
        log: false,
        module: false,
        provides: false,
        require: false,
        respond: false,
        send: false,
        start: false,
        sum: false
      },
      devtools: {
        $: false,
        $_: false,
        $$: false,
        $0: false,
        $1: false,
        $2: false,
        $3: false,
        $4: false,
        $x: false,
        chrome: false,
        clear: false,
        copy: false,
        debug: false,
        dir: false,
        dirxml: false,
        getEventListeners: false,
        inspect: false,
        keys: false,
        monitor: false,
        monitorEvents: false,
        profile: false,
        profileEnd: false,
        queryObjects: false,
        table: false,
        undebug: false,
        unmonitor: false,
        unmonitorEvents: false,
        values: false
      },
      embertest: {
        andThen: false,
        click: false,
        currentPath: false,
        currentRouteName: false,
        currentURL: false,
        fillIn: false,
        find: false,
        findAll: false,
        findWithAssert: false,
        keyEvent: false,
        pauseTest: false,
        resumeTest: false,
        triggerEvent: false,
        visit: false,
        wait: false
      },
      es2015: {
        Array: false,
        ArrayBuffer: false,
        Boolean: false,
        DataView: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        Float32Array: false,
        Float64Array: false,
        Function: false,
        Infinity: false,
        Int16Array: false,
        Int32Array: false,
        Int8Array: false,
        Intl: false,
        isFinite: false,
        isNaN: false,
        JSON: false,
        Map: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        Promise: false,
        Proxy: false,
        RangeError: false,
        ReferenceError: false,
        Reflect: false,
        RegExp: false,
        Set: false,
        String: false,
        Symbol: false,
        SyntaxError: false,
        TypeError: false,
        Uint16Array: false,
        Uint32Array: false,
        Uint8Array: false,
        Uint8ClampedArray: false,
        undefined: false,
        unescape: false,
        URIError: false,
        WeakMap: false,
        WeakSet: false
      },
      es2016: {
        Array: false,
        ArrayBuffer: false,
        Boolean: false,
        DataView: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        Float32Array: false,
        Float64Array: false,
        Function: false,
        Infinity: false,
        Int16Array: false,
        Int32Array: false,
        Int8Array: false,
        Intl: false,
        isFinite: false,
        isNaN: false,
        JSON: false,
        Map: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        Promise: false,
        Proxy: false,
        RangeError: false,
        ReferenceError: false,
        Reflect: false,
        RegExp: false,
        Set: false,
        String: false,
        Symbol: false,
        SyntaxError: false,
        TypeError: false,
        Uint16Array: false,
        Uint32Array: false,
        Uint8Array: false,
        Uint8ClampedArray: false,
        undefined: false,
        unescape: false,
        URIError: false,
        WeakMap: false,
        WeakSet: false
      },
      es2017: {
        Array: false,
        ArrayBuffer: false,
        Atomics: false,
        Boolean: false,
        DataView: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        Float32Array: false,
        Float64Array: false,
        Function: false,
        Infinity: false,
        Int16Array: false,
        Int32Array: false,
        Int8Array: false,
        Intl: false,
        isFinite: false,
        isNaN: false,
        JSON: false,
        Map: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        Promise: false,
        Proxy: false,
        RangeError: false,
        ReferenceError: false,
        Reflect: false,
        RegExp: false,
        Set: false,
        SharedArrayBuffer: false,
        String: false,
        Symbol: false,
        SyntaxError: false,
        TypeError: false,
        Uint16Array: false,
        Uint32Array: false,
        Uint8Array: false,
        Uint8ClampedArray: false,
        undefined: false,
        unescape: false,
        URIError: false,
        WeakMap: false,
        WeakSet: false
      },
      es2018: {
        Array: false,
        ArrayBuffer: false,
        Atomics: false,
        Boolean: false,
        DataView: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        Float32Array: false,
        Float64Array: false,
        Function: false,
        Infinity: false,
        Int16Array: false,
        Int32Array: false,
        Int8Array: false,
        Intl: false,
        isFinite: false,
        isNaN: false,
        JSON: false,
        Map: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        Promise: false,
        Proxy: false,
        RangeError: false,
        ReferenceError: false,
        Reflect: false,
        RegExp: false,
        Set: false,
        SharedArrayBuffer: false,
        String: false,
        Symbol: false,
        SyntaxError: false,
        TypeError: false,
        Uint16Array: false,
        Uint32Array: false,
        Uint8Array: false,
        Uint8ClampedArray: false,
        undefined: false,
        unescape: false,
        URIError: false,
        WeakMap: false,
        WeakSet: false
      },
      es2019: {
        Array: false,
        ArrayBuffer: false,
        Atomics: false,
        Boolean: false,
        DataView: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        Float32Array: false,
        Float64Array: false,
        Function: false,
        Infinity: false,
        Int16Array: false,
        Int32Array: false,
        Int8Array: false,
        Intl: false,
        isFinite: false,
        isNaN: false,
        JSON: false,
        Map: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        Promise: false,
        Proxy: false,
        RangeError: false,
        ReferenceError: false,
        Reflect: false,
        RegExp: false,
        Set: false,
        SharedArrayBuffer: false,
        String: false,
        Symbol: false,
        SyntaxError: false,
        TypeError: false,
        Uint16Array: false,
        Uint32Array: false,
        Uint8Array: false,
        Uint8ClampedArray: false,
        undefined: false,
        unescape: false,
        URIError: false,
        WeakMap: false,
        WeakSet: false
      },
      es2020: {
        Array: false,
        ArrayBuffer: false,
        Atomics: false,
        BigInt: false,
        BigInt64Array: false,
        BigUint64Array: false,
        Boolean: false,
        DataView: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        Float32Array: false,
        Float64Array: false,
        Function: false,
        globalThis: false,
        Infinity: false,
        Int16Array: false,
        Int32Array: false,
        Int8Array: false,
        Intl: false,
        isFinite: false,
        isNaN: false,
        JSON: false,
        Map: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        Promise: false,
        Proxy: false,
        RangeError: false,
        ReferenceError: false,
        Reflect: false,
        RegExp: false,
        Set: false,
        SharedArrayBuffer: false,
        String: false,
        Symbol: false,
        SyntaxError: false,
        TypeError: false,
        Uint16Array: false,
        Uint32Array: false,
        Uint8Array: false,
        Uint8ClampedArray: false,
        undefined: false,
        unescape: false,
        URIError: false,
        WeakMap: false,
        WeakSet: false
      },
      es2021: {
        AggregateError: false,
        Array: false,
        ArrayBuffer: false,
        Atomics: false,
        BigInt: false,
        BigInt64Array: false,
        BigUint64Array: false,
        Boolean: false,
        DataView: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        FinalizationRegistry: false,
        Float32Array: false,
        Float64Array: false,
        Function: false,
        globalThis: false,
        Infinity: false,
        Int16Array: false,
        Int32Array: false,
        Int8Array: false,
        Intl: false,
        isFinite: false,
        isNaN: false,
        JSON: false,
        Map: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        Promise: false,
        Proxy: false,
        RangeError: false,
        ReferenceError: false,
        Reflect: false,
        RegExp: false,
        Set: false,
        SharedArrayBuffer: false,
        String: false,
        Symbol: false,
        SyntaxError: false,
        TypeError: false,
        Uint16Array: false,
        Uint32Array: false,
        Uint8Array: false,
        Uint8ClampedArray: false,
        undefined: false,
        unescape: false,
        URIError: false,
        WeakMap: false,
        WeakRef: false,
        WeakSet: false
      },
      es2022: {
        AggregateError: false,
        Array: false,
        ArrayBuffer: false,
        Atomics: false,
        BigInt: false,
        BigInt64Array: false,
        BigUint64Array: false,
        Boolean: false,
        DataView: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        FinalizationRegistry: false,
        Float32Array: false,
        Float64Array: false,
        Function: false,
        globalThis: false,
        Infinity: false,
        Int16Array: false,
        Int32Array: false,
        Int8Array: false,
        Intl: false,
        isFinite: false,
        isNaN: false,
        JSON: false,
        Map: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        Promise: false,
        Proxy: false,
        RangeError: false,
        ReferenceError: false,
        Reflect: false,
        RegExp: false,
        Set: false,
        SharedArrayBuffer: false,
        String: false,
        Symbol: false,
        SyntaxError: false,
        TypeError: false,
        Uint16Array: false,
        Uint32Array: false,
        Uint8Array: false,
        Uint8ClampedArray: false,
        undefined: false,
        unescape: false,
        URIError: false,
        WeakMap: false,
        WeakRef: false,
        WeakSet: false
      },
      es2023: {
        AggregateError: false,
        Array: false,
        ArrayBuffer: false,
        Atomics: false,
        BigInt: false,
        BigInt64Array: false,
        BigUint64Array: false,
        Boolean: false,
        DataView: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        FinalizationRegistry: false,
        Float32Array: false,
        Float64Array: false,
        Function: false,
        globalThis: false,
        Infinity: false,
        Int16Array: false,
        Int32Array: false,
        Int8Array: false,
        Intl: false,
        isFinite: false,
        isNaN: false,
        JSON: false,
        Map: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        Promise: false,
        Proxy: false,
        RangeError: false,
        ReferenceError: false,
        Reflect: false,
        RegExp: false,
        Set: false,
        SharedArrayBuffer: false,
        String: false,
        Symbol: false,
        SyntaxError: false,
        TypeError: false,
        Uint16Array: false,
        Uint32Array: false,
        Uint8Array: false,
        Uint8ClampedArray: false,
        undefined: false,
        unescape: false,
        URIError: false,
        WeakMap: false,
        WeakRef: false,
        WeakSet: false
      },
      es2024: {
        AggregateError: false,
        Array: false,
        ArrayBuffer: false,
        Atomics: false,
        BigInt: false,
        BigInt64Array: false,
        BigUint64Array: false,
        Boolean: false,
        DataView: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        FinalizationRegistry: false,
        Float32Array: false,
        Float64Array: false,
        Function: false,
        globalThis: false,
        Infinity: false,
        Int16Array: false,
        Int32Array: false,
        Int8Array: false,
        Intl: false,
        isFinite: false,
        isNaN: false,
        JSON: false,
        Map: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        Promise: false,
        Proxy: false,
        RangeError: false,
        ReferenceError: false,
        Reflect: false,
        RegExp: false,
        Set: false,
        SharedArrayBuffer: false,
        String: false,
        Symbol: false,
        SyntaxError: false,
        TypeError: false,
        Uint16Array: false,
        Uint32Array: false,
        Uint8Array: false,
        Uint8ClampedArray: false,
        undefined: false,
        unescape: false,
        URIError: false,
        WeakMap: false,
        WeakRef: false,
        WeakSet: false
      },
      es2025: {
        AggregateError: false,
        Array: false,
        ArrayBuffer: false,
        Atomics: false,
        BigInt: false,
        BigInt64Array: false,
        BigUint64Array: false,
        Boolean: false,
        DataView: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        FinalizationRegistry: false,
        Float16Array: false,
        Float32Array: false,
        Float64Array: false,
        Function: false,
        globalThis: false,
        Infinity: false,
        Int16Array: false,
        Int32Array: false,
        Int8Array: false,
        Intl: false,
        isFinite: false,
        isNaN: false,
        Iterator: false,
        JSON: false,
        Map: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        Promise: false,
        Proxy: false,
        RangeError: false,
        ReferenceError: false,
        Reflect: false,
        RegExp: false,
        Set: false,
        SharedArrayBuffer: false,
        String: false,
        Symbol: false,
        SyntaxError: false,
        TypeError: false,
        Uint16Array: false,
        Uint32Array: false,
        Uint8Array: false,
        Uint8ClampedArray: false,
        undefined: false,
        unescape: false,
        URIError: false,
        WeakMap: false,
        WeakRef: false,
        WeakSet: false
      },
      es3: {
        Array: false,
        Boolean: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        Function: false,
        Infinity: false,
        isFinite: false,
        isNaN: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        RangeError: false,
        ReferenceError: false,
        RegExp: false,
        String: false,
        SyntaxError: false,
        TypeError: false,
        undefined: false,
        unescape: false,
        URIError: false
      },
      es5: {
        Array: false,
        Boolean: false,
        Date: false,
        decodeURI: false,
        decodeURIComponent: false,
        encodeURI: false,
        encodeURIComponent: false,
        Error: false,
        escape: false,
        eval: false,
        EvalError: false,
        Function: false,
        Infinity: false,
        isFinite: false,
        isNaN: false,
        JSON: false,
        Math: false,
        NaN: false,
        Number: false,
        Object: false,
        parseFloat: false,
        parseInt: false,
        RangeError: false,
        ReferenceError: false,
        RegExp: false,
        String: false,
        SyntaxError: false,
        TypeError: false,
        undefined: false,
        unescape: false,
        URIError: false
      },
      greasemonkey: {
        cloneInto: false,
        createObjectIn: false,
        exportFunction: false,
        GM: false,
        GM_addElement: false,
        GM_addStyle: false,
        GM_addValueChangeListener: false,
        GM_deleteValue: false,
        GM_deleteValues: false,
        GM_download: false,
        GM_getResourceText: false,
        GM_getResourceURL: false,
        GM_getTab: false,
        GM_getTabs: false,
        GM_getValue: false,
        GM_getValues: false,
        GM_info: false,
        GM_listValues: false,
        GM_log: false,
        GM_notification: false,
        GM_openInTab: false,
        GM_registerMenuCommand: false,
        GM_removeValueChangeListener: false,
        GM_saveTab: false,
        GM_setClipboard: false,
        GM_setValue: false,
        GM_setValues: false,
        GM_unregisterMenuCommand: false,
        GM_xmlhttpRequest: false,
        unsafeWindow: false
      },
      jasmine: {
        afterAll: false,
        afterEach: false,
        beforeAll: false,
        beforeEach: false,
        describe: false,
        expect: false,
        expectAsync: false,
        fail: false,
        fdescribe: false,
        fit: false,
        it: false,
        jasmine: false,
        pending: false,
        runs: false,
        spyOn: false,
        spyOnAllFunctions: false,
        spyOnProperty: false,
        waits: false,
        waitsFor: false,
        xdescribe: false,
        xit: false
      },
      jest: {
        afterAll: false,
        afterEach: false,
        beforeAll: false,
        beforeEach: false,
        describe: false,
        expect: false,
        fit: false,
        it: false,
        jest: false,
        test: false,
        xdescribe: false,
        xit: false,
        xtest: false
      },
      jquery: {
        $: false,
        jQuery: false
      },
      meteor: {
        $: false,
        Accounts: false,
        AccountsClient: false,
        AccountsCommon: false,
        AccountsServer: false,
        App: false,
        Assets: false,
        Blaze: false,
        check: false,
        Cordova: false,
        DDP: false,
        DDPRateLimiter: false,
        DDPServer: false,
        Deps: false,
        EJSON: false,
        Email: false,
        HTTP: false,
        Log: false,
        Match: false,
        Meteor: false,
        Mongo: false,
        MongoInternals: false,
        Npm: false,
        Package: false,
        Plugin: false,
        process: false,
        Random: false,
        ReactiveDict: false,
        ReactiveVar: false,
        Router: false,
        ServiceConfiguration: false,
        Session: false,
        share: false,
        Spacebars: false,
        Template: false,
        Tinytest: false,
        Tracker: false,
        UI: false,
        Utils: false,
        WebApp: false,
        WebAppInternals: false
      },
      mocha: {
        after: false,
        afterEach: false,
        before: false,
        beforeEach: false,
        context: false,
        describe: false,
        it: false,
        mocha: false,
        run: false,
        setup: false,
        specify: false,
        suite: false,
        suiteSetup: false,
        suiteTeardown: false,
        teardown: false,
        test: false,
        xcontext: false,
        xdescribe: false,
        xit: false,
        xspecify: false
      },
      mongo: {
        _isWindows: false,
        _rand: false,
        BulkWriteResult: false,
        cat: false,
        cd: false,
        connect: false,
        db: false,
        getHostName: false,
        getMemInfo: false,
        hostname: false,
        ISODate: false,
        listFiles: false,
        load: false,
        ls: false,
        md5sumFile: false,
        mkdir: false,
        Mongo: false,
        NumberInt: false,
        NumberLong: false,
        ObjectId: false,
        PlanCache: false,
        print: false,
        printjson: false,
        pwd: false,
        quit: false,
        removeFile: false,
        rs: false,
        sh: false,
        UUID: false,
        version: false,
        WriteResult: false
      },
      nashorn: {
        __DIR__: false,
        __FILE__: false,
        __LINE__: false,
        com: false,
        edu: false,
        exit: false,
        java: false,
        Java: false,
        javafx: false,
        JavaImporter: false,
        javax: false,
        JSAdapter: false,
        load: false,
        loadWithNewGlobal: false,
        org: false,
        Packages: false,
        print: false,
        quit: false
      },
      node: {
        __dirname: false,
        __filename: false,
        AbortController: false,
        AbortSignal: false,
        AsyncDisposableStack: false,
        atob: false,
        Blob: false,
        BroadcastChannel: false,
        btoa: false,
        Buffer: false,
        ByteLengthQueuingStrategy: false,
        clearImmediate: false,
        clearInterval: false,
        clearTimeout: false,
        CloseEvent: false,
        CompressionStream: false,
        console: false,
        CountQueuingStrategy: false,
        crypto: false,
        Crypto: false,
        CryptoKey: false,
        CustomEvent: false,
        DecompressionStream: false,
        DisposableStack: false,
        DOMException: false,
        Event: false,
        EventTarget: false,
        exports: true,
        fetch: false,
        File: false,
        FormData: false,
        global: false,
        Headers: false,
        MessageChannel: false,
        MessageEvent: false,
        MessagePort: false,
        module: false,
        navigator: false,
        Navigator: false,
        performance: false,
        Performance: false,
        PerformanceEntry: false,
        PerformanceMark: false,
        PerformanceMeasure: false,
        PerformanceObserver: false,
        PerformanceObserverEntryList: false,
        PerformanceResourceTiming: false,
        process: false,
        queueMicrotask: false,
        ReadableByteStreamController: false,
        ReadableStream: false,
        ReadableStreamBYOBReader: false,
        ReadableStreamBYOBRequest: false,
        ReadableStreamDefaultController: false,
        ReadableStreamDefaultReader: false,
        Request: false,
        require: false,
        Response: false,
        setImmediate: false,
        setInterval: false,
        setTimeout: false,
        structuredClone: false,
        SubtleCrypto: false,
        SuppressedError: false,
        TextDecoder: false,
        TextDecoderStream: false,
        TextEncoder: false,
        TextEncoderStream: false,
        TransformStream: false,
        TransformStreamDefaultController: false,
        URL: false,
        URLPattern: false,
        URLSearchParams: false,
        WebAssembly: false,
        WebSocket: false,
        WritableStream: false,
        WritableStreamDefaultController: false,
        WritableStreamDefaultWriter: false
      },
      nodeBuiltin: {
        AbortController: false,
        AbortSignal: false,
        AsyncDisposableStack: false,
        atob: false,
        Blob: false,
        BroadcastChannel: false,
        btoa: false,
        Buffer: false,
        ByteLengthQueuingStrategy: false,
        clearImmediate: false,
        clearInterval: false,
        clearTimeout: false,
        CloseEvent: false,
        CompressionStream: false,
        console: false,
        CountQueuingStrategy: false,
        crypto: false,
        Crypto: false,
        CryptoKey: false,
        CustomEvent: false,
        DecompressionStream: false,
        DisposableStack: false,
        DOMException: false,
        Event: false,
        EventTarget: false,
        fetch: false,
        File: false,
        FormData: false,
        global: false,
        Headers: false,
        MessageChannel: false,
        MessageEvent: false,
        MessagePort: false,
        navigator: false,
        Navigator: false,
        performance: false,
        Performance: false,
        PerformanceEntry: false,
        PerformanceMark: false,
        PerformanceMeasure: false,
        PerformanceObserver: false,
        PerformanceObserverEntryList: false,
        PerformanceResourceTiming: false,
        process: false,
        queueMicrotask: false,
        ReadableByteStreamController: false,
        ReadableStream: false,
        ReadableStreamBYOBReader: false,
        ReadableStreamBYOBRequest: false,
        ReadableStreamDefaultController: false,
        ReadableStreamDefaultReader: false,
        Request: false,
        Response: false,
        setImmediate: false,
        setInterval: false,
        setTimeout: false,
        structuredClone: false,
        SubtleCrypto: false,
        SuppressedError: false,
        TextDecoder: false,
        TextDecoderStream: false,
        TextEncoder: false,
        TextEncoderStream: false,
        TransformStream: false,
        TransformStreamDefaultController: false,
        URL: false,
        URLPattern: false,
        URLSearchParams: false,
        WebAssembly: false,
        WebSocket: false,
        WritableStream: false,
        WritableStreamDefaultController: false,
        WritableStreamDefaultWriter: false
      },
      phantomjs: {
        console: true,
        exports: true,
        phantom: true,
        require: true,
        WebPage: true
      },
      prototypejs: {
        $: false,
        $$: false,
        $A: false,
        $break: false,
        $continue: false,
        $F: false,
        $H: false,
        $R: false,
        $w: false,
        Abstract: false,
        Ajax: false,
        Autocompleter: false,
        Builder: false,
        Class: false,
        Control: false,
        Draggable: false,
        Draggables: false,
        Droppables: false,
        Effect: false,
        Element: false,
        Enumerable: false,
        Event: false,
        Field: false,
        Form: false,
        Hash: false,
        Insertion: false,
        ObjectRange: false,
        PeriodicalExecuter: false,
        Position: false,
        Prototype: false,
        Scriptaculous: false,
        Selector: false,
        Sortable: false,
        SortableObserver: false,
        Sound: false,
        Template: false,
        Toggle: false,
        Try: false
      },
      protractor: {
        $: false,
        $$: false,
        browser: false,
        by: false,
        By: false,
        DartObject: false,
        element: false,
        protractor: false
      },
      qunit: {
        asyncTest: false,
        deepEqual: false,
        equal: false,
        expect: false,
        module: false,
        notDeepEqual: false,
        notEqual: false,
        notOk: false,
        notPropEqual: false,
        notStrictEqual: false,
        ok: false,
        propEqual: false,
        QUnit: false,
        raises: false,
        start: false,
        stop: false,
        strictEqual: false,
        test: false,
        throws: false
      },
      rhino: {
        defineClass: false,
        deserialize: false,
        gc: false,
        help: false,
        importClass: false,
        importPackage: false,
        java: false,
        load: false,
        loadClass: false,
        Packages: false,
        print: false,
        quit: false,
        readFile: false,
        readUrl: false,
        runCommand: false,
        seal: false,
        serialize: false,
        spawn: false,
        sync: false,
        toint32: false,
        version: false
      },
      serviceworker: {
        AbortController: false,
        AbortPaymentEvent: false,
        AbortSignal: false,
        addEventListener: false,
        ai: false,
        AI: false,
        AICreateMonitor: false,
        AsyncDisposableStack: false,
        atob: false,
        BackgroundFetchEvent: false,
        BackgroundFetchManager: false,
        BackgroundFetchRecord: false,
        BackgroundFetchRegistration: false,
        BackgroundFetchUpdateUIEvent: false,
        BarcodeDetector: false,
        Blob: false,
        BroadcastChannel: false,
        btoa: false,
        ByteLengthQueuingStrategy: false,
        Cache: false,
        caches: false,
        CacheStorage: false,
        CanMakePaymentEvent: false,
        CanvasGradient: false,
        CanvasPattern: false,
        clearInterval: false,
        clearTimeout: false,
        Client: false,
        clients: false,
        Clients: false,
        CloseEvent: false,
        CompressionStream: false,
        console: false,
        cookieStore: false,
        CookieStore: false,
        CookieStoreManager: false,
        CountQueuingStrategy: false,
        createImageBitmap: false,
        CropTarget: false,
        crossOriginIsolated: false,
        crypto: false,
        Crypto: false,
        CryptoKey: false,
        CSSSkewX: false,
        CSSSkewY: false,
        CustomEvent: false,
        DecompressionStream: false,
        dispatchEvent: false,
        DisposableStack: false,
        DOMException: false,
        DOMMatrix: false,
        DOMMatrixReadOnly: false,
        DOMPoint: false,
        DOMPointReadOnly: false,
        DOMQuad: false,
        DOMRect: false,
        DOMRectReadOnly: false,
        DOMStringList: false,
        ErrorEvent: false,
        Event: false,
        EventSource: false,
        EventTarget: false,
        ExtendableCookieChangeEvent: false,
        ExtendableEvent: false,
        ExtendableMessageEvent: false,
        fetch: false,
        FetchEvent: false,
        File: false,
        FileList: false,
        FileReader: false,
        FileSystemDirectoryHandle: false,
        FileSystemFileHandle: false,
        FileSystemHandle: false,
        FileSystemWritableFileStream: false,
        FontFace: false,
        fonts: false,
        FormData: false,
        GPU: false,
        GPUAdapter: false,
        GPUAdapterInfo: false,
        GPUBindGroup: false,
        GPUBindGroupLayout: false,
        GPUBuffer: false,
        GPUBufferUsage: false,
        GPUCanvasContext: false,
        GPUColorWrite: false,
        GPUCommandBuffer: false,
        GPUCommandEncoder: false,
        GPUCompilationInfo: false,
        GPUCompilationMessage: false,
        GPUComputePassEncoder: false,
        GPUComputePipeline: false,
        GPUDevice: false,
        GPUDeviceLostInfo: false,
        GPUError: false,
        GPUExternalTexture: false,
        GPUInternalError: false,
        GPUMapMode: false,
        GPUOutOfMemoryError: false,
        GPUPipelineError: false,
        GPUPipelineLayout: false,
        GPUQuerySet: false,
        GPUQueue: false,
        GPURenderBundle: false,
        GPURenderBundleEncoder: false,
        GPURenderPassEncoder: false,
        GPURenderPipeline: false,
        GPUSampler: false,
        GPUShaderModule: false,
        GPUShaderStage: false,
        GPUSupportedFeatures: false,
        GPUSupportedLimits: false,
        GPUTexture: false,
        GPUTextureUsage: false,
        GPUTextureView: false,
        GPUUncapturedErrorEvent: false,
        GPUValidationError: false,
        Headers: false,
        IDBCursor: false,
        IDBCursorWithValue: false,
        IDBDatabase: false,
        IDBFactory: false,
        IDBIndex: false,
        IDBKeyRange: false,
        IDBObjectStore: false,
        IDBOpenDBRequest: false,
        IDBRequest: false,
        IDBTransaction: false,
        IDBVersionChangeEvent: false,
        ImageBitmap: false,
        ImageBitmapRenderingContext: false,
        ImageData: false,
        importScripts: false,
        indexedDB: false,
        InstallEvent: false,
        isSecureContext: false,
        LanguageDetector: false,
        location: false,
        Lock: false,
        LockManager: false,
        MediaCapabilities: false,
        MessageChannel: false,
        MessageEvent: false,
        MessagePort: false,
        NavigationPreloadManager: false,
        navigator: false,
        NavigatorUAData: false,
        NetworkInformation: false,
        Notification: false,
        NotificationEvent: false,
        Observable: false,
        OffscreenCanvas: false,
        OffscreenCanvasRenderingContext2D: false,
        onabortpayment: true,
        onactivate: true,
        onbackgroundfetchabort: true,
        onbackgroundfetchclick: true,
        onbackgroundfetchfail: true,
        onbackgroundfetchsuccess: true,
        oncanmakepayment: true,
        oncookiechange: true,
        onerror: true,
        onfetch: true,
        oninstall: true,
        onlanguagechange: true,
        onmessage: true,
        onmessageerror: true,
        onnotificationclick: true,
        onnotificationclose: true,
        onpaymentrequest: true,
        onperiodicsync: true,
        onpush: true,
        onpushsubscriptionchange: true,
        onrejectionhandled: true,
        onsync: true,
        onunhandledrejection: true,
        origin: false,
        Path2D: false,
        PaymentRequestEvent: false,
        performance: false,
        Performance: false,
        PerformanceEntry: false,
        PerformanceMark: false,
        PerformanceMeasure: false,
        PerformanceObserver: false,
        PerformanceObserverEntryList: false,
        PerformanceResourceTiming: false,
        PerformanceServerTiming: false,
        PeriodicSyncEvent: false,
        PeriodicSyncManager: false,
        Permissions: false,
        PermissionStatus: false,
        PromiseRejectionEvent: false,
        PushEvent: false,
        PushManager: false,
        PushMessageData: false,
        PushSubscription: false,
        PushSubscriptionOptions: false,
        queueMicrotask: false,
        ReadableByteStreamController: false,
        ReadableStream: false,
        ReadableStreamBYOBReader: false,
        ReadableStreamBYOBRequest: false,
        ReadableStreamDefaultController: false,
        ReadableStreamDefaultReader: false,
        registration: false,
        removeEventListener: false,
        ReportBody: false,
        reportError: false,
        ReportingObserver: false,
        Request: false,
        Response: false,
        RestrictionTarget: false,
        scheduler: false,
        Scheduler: false,
        SecurityPolicyViolationEvent: false,
        self: false,
        serviceWorker: false,
        ServiceWorker: false,
        ServiceWorkerGlobalScope: false,
        ServiceWorkerRegistration: false,
        setInterval: false,
        setTimeout: false,
        skipWaiting: false,
        StorageBucket: false,
        StorageBucketManager: false,
        StorageManager: false,
        structuredClone: false,
        Subscriber: false,
        SubtleCrypto: false,
        SuppressedError: false,
        SyncEvent: false,
        SyncManager: false,
        TaskController: false,
        TaskPriorityChangeEvent: false,
        TaskSignal: false,
        TextDecoder: false,
        TextDecoderStream: false,
        TextEncoder: false,
        TextEncoderStream: false,
        TextMetrics: false,
        TransformStream: false,
        TransformStreamDefaultController: false,
        TrustedHTML: false,
        TrustedScript: false,
        TrustedScriptURL: false,
        TrustedTypePolicy: false,
        TrustedTypePolicyFactory: false,
        trustedTypes: false,
        URL: false,
        URLPattern: false,
        URLSearchParams: false,
        UserActivation: false,
        WebAssembly: false,
        WebGL2RenderingContext: false,
        WebGLActiveInfo: false,
        WebGLBuffer: false,
        WebGLContextEvent: false,
        WebGLFramebuffer: false,
        WebGLObject: false,
        WebGLProgram: false,
        WebGLQuery: false,
        WebGLRenderbuffer: false,
        WebGLRenderingContext: false,
        WebGLSampler: false,
        WebGLShader: false,
        WebGLShaderPrecisionFormat: false,
        WebGLSync: false,
        WebGLTexture: false,
        WebGLTransformFeedback: false,
        WebGLUniformLocation: false,
        WebGLVertexArrayObject: false,
        WebSocket: false,
        WebSocketError: false,
        WebSocketStream: false,
        WebTransport: false,
        WebTransportBidirectionalStream: false,
        WebTransportDatagramDuplexStream: false,
        WebTransportError: false,
        WGSLLanguageFeatures: false,
        when: false,
        WindowClient: false,
        WorkerGlobalScope: false,
        WorkerLocation: false,
        WorkerNavigator: false,
        WritableStream: false,
        WritableStreamDefaultController: false,
        WritableStreamDefaultWriter: false
      },
      "shared-node-browser": {
        AbortController: false,
        AbortSignal: false,
        AsyncDisposableStack: false,
        atob: false,
        Blob: false,
        BroadcastChannel: false,
        btoa: false,
        ByteLengthQueuingStrategy: false,
        clearInterval: false,
        clearTimeout: false,
        CloseEvent: false,
        CompressionStream: false,
        console: false,
        CountQueuingStrategy: false,
        crypto: false,
        Crypto: false,
        CryptoKey: false,
        CustomEvent: false,
        DecompressionStream: false,
        DisposableStack: false,
        DOMException: false,
        Event: false,
        EventTarget: false,
        fetch: false,
        File: false,
        FormData: false,
        Headers: false,
        MessageChannel: false,
        MessageEvent: false,
        MessagePort: false,
        navigator: false,
        Navigator: false,
        performance: false,
        Performance: false,
        PerformanceEntry: false,
        PerformanceMark: false,
        PerformanceMeasure: false,
        PerformanceObserver: false,
        PerformanceObserverEntryList: false,
        PerformanceResourceTiming: false,
        queueMicrotask: false,
        ReadableByteStreamController: false,
        ReadableStream: false,
        ReadableStreamBYOBReader: false,
        ReadableStreamBYOBRequest: false,
        ReadableStreamDefaultController: false,
        ReadableStreamDefaultReader: false,
        Request: false,
        Response: false,
        setInterval: false,
        setTimeout: false,
        structuredClone: false,
        SubtleCrypto: false,
        SuppressedError: false,
        TextDecoder: false,
        TextDecoderStream: false,
        TextEncoder: false,
        TextEncoderStream: false,
        TransformStream: false,
        TransformStreamDefaultController: false,
        URL: false,
        URLPattern: false,
        URLSearchParams: false,
        WebAssembly: false,
        WebSocket: false,
        WritableStream: false,
        WritableStreamDefaultController: false,
        WritableStreamDefaultWriter: false
      },
      shelljs: {
        cat: false,
        cd: false,
        chmod: false,
        cmd: false,
        config: false,
        cp: false,
        dirs: false,
        echo: false,
        env: false,
        error: false,
        errorCode: false,
        exec: false,
        exit: false,
        find: false,
        grep: false,
        head: false,
        ln: false,
        ls: false,
        mkdir: false,
        mv: false,
        popd: false,
        pushd: false,
        pwd: false,
        rm: false,
        sed: false,
        set: false,
        ShellString: false,
        sort: false,
        tail: false,
        tempdir: false,
        test: false,
        touch: false,
        uniq: false,
        which: false
      },
      vitest: {
        afterAll: false,
        afterEach: false,
        assert: false,
        assertType: false,
        beforeAll: false,
        beforeEach: false,
        chai: false,
        describe: false,
        expect: false,
        expectTypeOf: false,
        it: false,
        onTestFailed: false,
        onTestFinished: false,
        suite: false,
        test: false,
        vi: false,
        vitest: false
      },
      webextensions: {
        browser: false,
        chrome: false,
        opr: false
      },
      worker: {
        AbortController: false,
        AbortSignal: false,
        addEventListener: false,
        ai: false,
        AI: false,
        AICreateMonitor: false,
        AsyncDisposableStack: false,
        atob: false,
        AudioData: false,
        AudioDecoder: false,
        AudioEncoder: false,
        BackgroundFetchManager: false,
        BackgroundFetchRecord: false,
        BackgroundFetchRegistration: false,
        BarcodeDetector: false,
        Blob: false,
        BroadcastChannel: false,
        btoa: false,
        ByteLengthQueuingStrategy: false,
        Cache: false,
        caches: false,
        CacheStorage: false,
        cancelAnimationFrame: false,
        CanvasGradient: false,
        CanvasPattern: false,
        clearInterval: false,
        clearTimeout: false,
        close: false,
        CloseEvent: false,
        CompressionStream: false,
        console: false,
        CountQueuingStrategy: false,
        createImageBitmap: false,
        CropTarget: false,
        crossOriginIsolated: false,
        crypto: false,
        Crypto: false,
        CryptoKey: false,
        CSSSkewX: false,
        CSSSkewY: false,
        CustomEvent: false,
        DecompressionStream: false,
        DedicatedWorkerGlobalScope: false,
        dispatchEvent: false,
        DisposableStack: false,
        DOMException: false,
        DOMMatrix: false,
        DOMMatrixReadOnly: false,
        DOMPoint: false,
        DOMPointReadOnly: false,
        DOMQuad: false,
        DOMRect: false,
        DOMRectReadOnly: false,
        DOMStringList: false,
        EncodedAudioChunk: false,
        EncodedVideoChunk: false,
        ErrorEvent: false,
        Event: false,
        EventSource: false,
        EventTarget: false,
        fetch: false,
        File: false,
        FileList: false,
        FileReader: false,
        FileReaderSync: false,
        FileSystemDirectoryHandle: false,
        FileSystemFileHandle: false,
        FileSystemHandle: false,
        FileSystemObserver: false,
        FileSystemSyncAccessHandle: false,
        FileSystemWritableFileStream: false,
        FontFace: false,
        fonts: false,
        FormData: false,
        GPU: false,
        GPUAdapter: false,
        GPUAdapterInfo: false,
        GPUBindGroup: false,
        GPUBindGroupLayout: false,
        GPUBuffer: false,
        GPUBufferUsage: false,
        GPUCanvasContext: false,
        GPUColorWrite: false,
        GPUCommandBuffer: false,
        GPUCommandEncoder: false,
        GPUCompilationInfo: false,
        GPUCompilationMessage: false,
        GPUComputePassEncoder: false,
        GPUComputePipeline: false,
        GPUDevice: false,
        GPUDeviceLostInfo: false,
        GPUError: false,
        GPUExternalTexture: false,
        GPUInternalError: false,
        GPUMapMode: false,
        GPUOutOfMemoryError: false,
        GPUPipelineError: false,
        GPUPipelineLayout: false,
        GPUQuerySet: false,
        GPUQueue: false,
        GPURenderBundle: false,
        GPURenderBundleEncoder: false,
        GPURenderPassEncoder: false,
        GPURenderPipeline: false,
        GPUSampler: false,
        GPUShaderModule: false,
        GPUShaderStage: false,
        GPUSupportedFeatures: false,
        GPUSupportedLimits: false,
        GPUTexture: false,
        GPUTextureUsage: false,
        GPUTextureView: false,
        GPUUncapturedErrorEvent: false,
        GPUValidationError: false,
        Headers: false,
        HID: false,
        HIDConnectionEvent: false,
        HIDDevice: false,
        HIDInputReportEvent: false,
        IDBCursor: false,
        IDBCursorWithValue: false,
        IDBDatabase: false,
        IDBFactory: false,
        IDBIndex: false,
        IDBKeyRange: false,
        IDBObjectStore: false,
        IDBOpenDBRequest: false,
        IDBRequest: false,
        IDBTransaction: false,
        IDBVersionChangeEvent: false,
        IdleDetector: false,
        ImageBitmap: false,
        ImageBitmapRenderingContext: false,
        ImageData: false,
        ImageDecoder: false,
        ImageTrack: false,
        ImageTrackList: false,
        importScripts: false,
        indexedDB: false,
        isSecureContext: false,
        LanguageDetector: false,
        location: false,
        Lock: false,
        LockManager: false,
        MediaCapabilities: false,
        MediaSource: false,
        MediaSourceHandle: false,
        MessageChannel: false,
        MessageEvent: false,
        MessagePort: false,
        name: false,
        NavigationPreloadManager: false,
        navigator: false,
        NavigatorUAData: false,
        NetworkInformation: false,
        Notification: false,
        Observable: false,
        OffscreenCanvas: false,
        OffscreenCanvasRenderingContext2D: false,
        onerror: true,
        onlanguagechange: true,
        onmessage: true,
        onmessageerror: true,
        onrejectionhandled: true,
        onunhandledrejection: true,
        origin: false,
        Path2D: false,
        performance: false,
        Performance: false,
        PerformanceEntry: false,
        PerformanceMark: false,
        PerformanceMeasure: false,
        PerformanceObserver: false,
        PerformanceObserverEntryList: false,
        PerformanceResourceTiming: false,
        PerformanceServerTiming: false,
        PeriodicSyncManager: false,
        Permissions: false,
        PermissionStatus: false,
        PERSISTENT: false,
        postMessage: false,
        PressureObserver: false,
        PressureRecord: false,
        ProgressEvent: false,
        PromiseRejectionEvent: false,
        PushManager: false,
        PushSubscription: false,
        PushSubscriptionOptions: false,
        queueMicrotask: false,
        ReadableByteStreamController: false,
        ReadableStream: false,
        ReadableStreamBYOBReader: false,
        ReadableStreamBYOBRequest: false,
        ReadableStreamDefaultController: false,
        ReadableStreamDefaultReader: false,
        removeEventListener: false,
        ReportBody: false,
        reportError: false,
        ReportingObserver: false,
        Request: false,
        requestAnimationFrame: false,
        Response: false,
        RestrictionTarget: false,
        RTCDataChannel: false,
        RTCEncodedAudioFrame: false,
        RTCEncodedVideoFrame: false,
        scheduler: false,
        Scheduler: false,
        SecurityPolicyViolationEvent: false,
        self: false,
        Serial: false,
        SerialPort: false,
        ServiceWorkerRegistration: false,
        setInterval: false,
        setTimeout: false,
        SourceBuffer: false,
        SourceBufferList: false,
        StorageBucket: false,
        StorageBucketManager: false,
        StorageManager: false,
        structuredClone: false,
        Subscriber: false,
        SubtleCrypto: false,
        SuppressedError: false,
        SyncManager: false,
        TaskController: false,
        TaskPriorityChangeEvent: false,
        TaskSignal: false,
        TEMPORARY: false,
        TextDecoder: false,
        TextDecoderStream: false,
        TextEncoder: false,
        TextEncoderStream: false,
        TextMetrics: false,
        TransformStream: false,
        TransformStreamDefaultController: false,
        TrustedHTML: false,
        TrustedScript: false,
        TrustedScriptURL: false,
        TrustedTypePolicy: false,
        TrustedTypePolicyFactory: false,
        trustedTypes: false,
        URL: false,
        URLPattern: false,
        URLSearchParams: false,
        USB: false,
        USBAlternateInterface: false,
        USBConfiguration: false,
        USBConnectionEvent: false,
        USBDevice: false,
        USBEndpoint: false,
        USBInterface: false,
        USBInTransferResult: false,
        USBIsochronousInTransferPacket: false,
        USBIsochronousInTransferResult: false,
        USBIsochronousOutTransferPacket: false,
        USBIsochronousOutTransferResult: false,
        USBOutTransferResult: false,
        UserActivation: false,
        VideoColorSpace: false,
        VideoDecoder: false,
        VideoEncoder: false,
        VideoFrame: false,
        WebAssembly: false,
        WebGL2RenderingContext: false,
        WebGLActiveInfo: false,
        WebGLBuffer: false,
        WebGLContextEvent: false,
        WebGLFramebuffer: false,
        WebGLObject: false,
        WebGLProgram: false,
        WebGLQuery: false,
        WebGLRenderbuffer: false,
        WebGLRenderingContext: false,
        WebGLSampler: false,
        WebGLShader: false,
        WebGLShaderPrecisionFormat: false,
        WebGLSync: false,
        WebGLTexture: false,
        WebGLTransformFeedback: false,
        WebGLUniformLocation: false,
        WebGLVertexArrayObject: false,
        webkitRequestFileSystem: false,
        webkitRequestFileSystemSync: false,
        webkitResolveLocalFileSystemSyncURL: false,
        webkitResolveLocalFileSystemURL: false,
        WebSocket: false,
        WebSocketError: false,
        WebSocketStream: false,
        WebTransport: false,
        WebTransportBidirectionalStream: false,
        WebTransportDatagramDuplexStream: false,
        WebTransportError: false,
        WGSLLanguageFeatures: false,
        when: false,
        Worker: false,
        WorkerGlobalScope: false,
        WorkerLocation: false,
        WorkerNavigator: false,
        WritableStream: false,
        WritableStreamDefaultController: false,
        WritableStreamDefaultWriter: false,
        XMLHttpRequest: false,
        XMLHttpRequestEventTarget: false,
        XMLHttpRequestUpload: false
      },
      wsh: {
        ActiveXObject: false,
        CollectGarbage: false,
        Debug: false,
        Enumerator: false,
        GetObject: false,
        RuntimeObject: false,
        ScriptEngine: false,
        ScriptEngineBuildVersion: false,
        ScriptEngineMajorVersion: false,
        ScriptEngineMinorVersion: false,
        VBArray: false,
        WScript: false,
        WSH: false
      },
      yui: {
        YAHOO: false,
        YAHOO_config: false,
        YUI: false,
        YUI_config: false
      }
    };
  }
});

// node_modules/globals/index.js
var require_globals2 = __commonJS({
  "node_modules/globals/index.js"(exports2, module2) {
    "use strict";
    module2.exports = require_globals();
  }
});

// src/index.js
var index_exports = {};
__export(index_exports, {
  default: () => index_default
});
var import_globals, pluginName, plugin, index_default;
var init_index = __esm({
  "src/index.js"() {
    init_rule();
    import_globals = __toESM(require_globals2(), 1);
    pluginName = "react-you-might-not-need-an-effect";
    plugin = {
      meta: {
        name: pluginName
      },
      configs: {},
      rules: {
        [name]: rule
      }
    };
    Object.assign(plugin.configs, {
      // flat config format
      recommended: {
        files: ["**/*.{js,jsx,mjs,cjs,ts,tsx,mts,cts}"],
        plugins: {
          // Object.assign above so we can reference `plugin` here
          [pluginName]: plugin
        },
        rules: {
          [pluginName + "/" + name]: "warn"
        },
        languageOptions: {
          globals: {
            // NOTE: Required so we can resolve global references to their upstream global variables
            ...import_globals.default.browser
          },
          parserOptions: {
            ecmaFeatures: {
              jsx: true
            }
          }
        }
      },
      // eslintrc format
      "legacy-recommended": {
        plugins: [pluginName],
        rules: {
          [pluginName + "/" + name]: "warn"
        },
        globals: {
          // NOTE: Required so we can resolve global references to their upstream global variables
          ...import_globals.default.browser
        },
        parserOptions: {
          ecmaFeatures: {
            jsx: true
          }
        }
      }
    });
    index_default = plugin;
  }
});

// src/index.cjs
module.exports = (init_index(), __toCommonJS(index_exports)).default;
/*! Bundled license information:

eslint-utils/index.mjs:
  (*! <AUTHOR> Nagashima <https://github.com/mysticatea> *)
*/
//# sourceMappingURL=index.cjs.map
