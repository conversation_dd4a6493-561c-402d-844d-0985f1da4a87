{"name": "eslint-plugin-react-you-might-not-need-an-effect", "version": "0.1.5", "description": "ESLint rule to warn against unnecessary React useEffect hooks.", "author": "<PERSON>", "license": "MIT", "type": "module", "module": "./src/index.js", "main": "./dist/index.cjs", "types": "./types/index.d.ts", "files": ["src", "types", "dist"], "exports": {"types": "./types/index.d.ts", "import": "./src/index.js", "require": "./dist/index.cjs"}, "repository": {"type": "git", "url": "https://github.com/NickvanDyke/eslint-plugin-react-you-might-not-need-an-effect.git"}, "scripts": {"build": "node build.js", "lint": "eslint", "test": "mocha"}, "keywords": ["eslint", "eslintplugin", "eslint-plugin", "react"], "engines": {"node": ">=14.0.0"}, "dependencies": {"eslint-utils": "^3.0.0", "globals": "^16.2.0"}, "devDependencies": {"@eslint/js": "^9.28.0", "esbuild": "^0.25.3", "eslint": "^9.20.1", "eslint-plugin-eslint-plugin": "^6.4.0", "eslint-plugin-n": "^17.17.0", "lint-staged": "^16.1.0", "mocha": "11.1.0", "prettier": "^3.5.3", "simple-git-hooks": "^2.13.0"}, "peerDependencies": {"eslint": ">=7.0.0"}, "packageManager": "yarn@4.6.0", "simple-git-hooks": {"pre-commit": "yarn lint-staged"}, "lint-staged": {"*.js": ["prettier --write"]}}