import { NextRequest, NextResponse } from 'next/server'

// 从配置文件导入语言设置
const i18nConfig = {
  defaultLocale: 'zh-CN',
  locales: ['en', 'zh-CN', 'ja', 'es'],
}

// 获取用户首选语言
function getPreferredLocale(request: NextRequest): string {
  // 1. 检查URL路径中的语言前缀
  const pathname = request.nextUrl.pathname
  const pathnameHasLocale = i18nConfig.locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  )
  
  if (pathnameHasLocale) {
    return pathname.split('/')[1]
  }

  // 2. 检查cookie中保存的语言偏好
  const cookieLocale = request.cookies.get('NEXT_LOCALE')?.value
  if (cookieLocale && i18nConfig.locales.includes(cookieLocale)) {
    return cookieLocale
  }

  // 3. 检查Accept-Language header
  const acceptLanguage = request.headers.get('Accept-Language')
  if (acceptLanguage) {
    const preferredLanguages = acceptLanguage
      .split(',')
      .map(lang => lang.split(';')[0].trim())
    
    for (const lang of preferredLanguages) {
      // 精确匹配
      if (i18nConfig.locales.includes(lang)) {
        return lang
      }
      
      // 语言代码匹配 (如 'zh' 匹配 'zh-CN')
      const matchedLocale = i18nConfig.locales.find(locale => 
        locale.startsWith(lang.split('-')[0])
      )
      if (matchedLocale) {
        return matchedLocale
      }
    }
  }

  // 4. 返回默认语言
  return i18nConfig.defaultLocale
}

// 检查路径是否需要排除中间件处理
function shouldSkipMiddleware(pathname: string): boolean {
  // 排除静态资源
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/') ||
    pathname.startsWith('/static/') ||
    pathname.startsWith('/locales/') || // 排除翻译文件
    pathname.startsWith('/images/') ||
    pathname.startsWith('/documents/') ||
    pathname.includes('.') // 文件扩展名
  ) {
    return true
  }

  // 排除特殊Next.js路径
  const skipPaths = [
    '/favicon.ico',
    '/robots.txt',
    '/sitemap.xml',
    '/manifest.json',
    '/site.webmanifest',
    '/apple-touch-icon.png',
    '/favicon-32x32.png',
    '/favicon-16x16.png'
  ]

  return skipPaths.includes(pathname)
}

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname

  console.log('Middleware called for:', pathname)

  // 跳过不需要处理的路径
  if (shouldSkipMiddleware(pathname)) {
    console.log('Skipping middleware for:', pathname)
    return NextResponse.next()
  }

  // 检查路径是否已包含语言前缀
  const pathnameHasLocale = i18nConfig.locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  )

  // 如果路径已包含语言前缀，直接通过
  if (pathnameHasLocale) {
    console.log('Path already has locale:', pathname)
    return NextResponse.next()
  }

  // 如果是根路径，重定向到默认语言
  if (pathname === '/') {
    const redirectUrl = new URL(`/${i18nConfig.defaultLocale}`, request.url)
    console.log('Redirecting root to:', redirectUrl.toString())
    return NextResponse.redirect(redirectUrl)
  }

  // 对于其他路径，添加默认语言前缀
  const redirectUrl = new URL(`/${i18nConfig.defaultLocale}${pathname}`, request.url)
  redirectUrl.search = request.nextUrl.search
  console.log('Redirecting to:', redirectUrl.toString())

  return NextResponse.redirect(redirectUrl)
}

// 配置中间件匹配规则
export const config = {
  matcher: [
    // 匹配根路径和所有页面路径
    '/',
    '/((?!api|_next|locales|favicon|apple-touch-icon|robots|sitemap|manifest).*)',
  ],
}
