/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/zh-CN/page";
exports.ids = ["app/zh-CN/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fzh-CN%2Fpage&page=%2Fzh-CN%2Fpage&appPaths=%2Fzh-CN%2Fpage&pagePath=private-next-app-dir%2Fzh-CN%2Fpage.tsx&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fzh-CN%2Fpage&page=%2Fzh-CN%2Fpage&appPaths=%2Fzh-CN%2Fpage&pagePath=private-next-app-dir%2Fzh-CN%2Fpage.tsx&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?2f02\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'zh-CN',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/zh-CN/page.tsx */ \"(rsc)/./src/app/zh-CN/page.tsx\")), \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/zh-CN/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/zh-CN/page\",\n        pathname: \"/zh-CN\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX0BvcGVudGVsZW1ldHJ5K2FwaUAxLjkuMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZ6aC1DTiUyRnBhZ2UmcGFnZT0lMkZ6aC1DTiUyRnBhZ2UmYXBwUGF0aHM9JTJGemgtQ04lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGemgtQ04lMkZwYWdlLnRzeCZhcHBEaXI9JTJGVXNlcnMlMkZEYXRhJTJGVmliZS1Db2RlJTJGdGVzdC13ZWItMS4wJTJGc3JjJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj0lMkZVc2VycyUyRkRhdGElMkZWaWJlLUNvZGUlMkZ0ZXN0LXdlYi0xLjAmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9c3RhbmRhbG9uZSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsYUFBYSxzQkFBc0I7QUFDaUU7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLHVCQUF1Qiw0SkFBOEY7QUFDckg7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSx5QkFBeUIsb0pBQTBGO0FBQ25ILG9CQUFvQixpVEFBZ0Y7QUFDcEc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLDhHQUFrQjtBQUNqRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90dWNzZW5iZXJnLXdlYnNpdGUvPzk2NTYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnemgtQ04nLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvRGF0YS9WaWJlLUNvZGUvdGVzdC13ZWItMS4wL3NyYy9hcHAvemgtQ04vcGFnZS50c3hcIiksIFwiL1VzZXJzL0RhdGEvVmliZS1Db2RlL3Rlc3Qtd2ViLTEuMC9zcmMvYXBwL3poLUNOL3BhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvRGF0YS9WaWJlLUNvZGUvdGVzdC13ZWItMS4wL3NyYy9hcHAvbGF5b3V0LnRzeFwiKSwgXCIvVXNlcnMvRGF0YS9WaWJlLUNvZGUvdGVzdC13ZWItMS4wL3NyYy9hcHAvbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiL1VzZXJzL0RhdGEvVmliZS1Db2RlL3Rlc3Qtd2ViLTEuMC9zcmMvYXBwL3poLUNOL3BhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvemgtQ04vcGFnZVwiO1xuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCI7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi96aC1DTi9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi96aC1DTlwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fzh-CN%2Fpage&page=%2Fzh-CN%2Fpage&appPaths=%2Fzh-CN%2Fpage&pagePath=private-next-app-dir%2Fzh-CN%2Fpage.tsx&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"80e7294b7f81\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHVjc2VuYmVyZy13ZWJzaXRlLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz8wMzNjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiODBlNzI5NGI3ZjgxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SUPPORTED_LOCALES: () => (/* binding */ SUPPORTED_LOCALES),\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n// 支持的语言配置\nconst SUPPORTED_LOCALES = {\n    \"zh-CN\": {\n        name: \"中文\",\n        nativeName: \"简体中文\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        dir: \"ltr\"\n    },\n    \"en\": {\n        name: \"English\",\n        nativeName: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        dir: \"ltr\"\n    },\n    \"ja\": {\n        name: \"Japanese\",\n        nativeName: \"日本語\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        dir: \"ltr\"\n    },\n    \"es\": {\n        name: \"Spanish\",\n        nativeName: \"Espa\\xf1ol\",\n        flag: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\",\n        dir: \"ltr\"\n    }\n};\n// 生成动态metadata的函数\nfunction generateMetadata(locale = \"zh-CN\") {\n    const localeConfig = SUPPORTED_LOCALES[locale];\n    // 根据语言生成不同的metadata\n    const metadataByLocale = {\n        \"zh-CN\": {\n            title: {\n                default: \"Tucsenberg - 专业防洪设备制造商\",\n                template: \"%s | Tucsenberg\"\n            },\n            description: \"图森博格(Tucsenberg)成立于2010年，专注于防洪设备研发、设计与制造，为全球用户提供质量过硬的防洪产品系列和综合解决方案。\",\n            keywords: [\n                \"防洪设备\",\n                \"防洪墙\",\n                \"防洪产品\",\n                \"应急防洪\",\n                \"工程防洪\",\n                \"Tucsenberg\",\n                \"图森博格\"\n            ]\n        },\n        \"en\": {\n            title: {\n                default: \"Tucsenberg - Professional Flood Protection Equipment Manufacturer\",\n                template: \"%s | Tucsenberg\"\n            },\n            description: \"Tucsenberg, established in 2010, specializes in flood protection equipment R&D, design and manufacturing, providing high-quality flood protection products and comprehensive solutions for global users.\",\n            keywords: [\n                \"flood protection\",\n                \"flood barriers\",\n                \"flood control\",\n                \"emergency flood protection\",\n                \"industrial flood protection\",\n                \"Tucsenberg\"\n            ]\n        },\n        \"ja\": {\n            title: {\n                default: \"Tucsenberg - プロフェッショナル洪水防護設備メーカー\",\n                template: \"%s | Tucsenberg\"\n            },\n            description: \"2010年に設立されたTucsenbergは、洪水防護設備の研究開発、設計、製造を専門とし、世界中のユーザーに高品質な洪水防護製品と包括的なソリューションを提供しています。\",\n            keywords: [\n                \"洪水防護\",\n                \"防水壁\",\n                \"洪水制御\",\n                \"緊急洪水防護\",\n                \"産業洪水防護\",\n                \"Tucsenberg\"\n            ]\n        },\n        \"es\": {\n            title: {\n                default: \"Tucsenberg - Fabricante Profesional de Equipos de Protecci\\xf3n contra Inundaciones\",\n                template: \"%s | Tucsenberg\"\n            },\n            description: \"Tucsenberg, establecida en 2010, se especializa en I+D, dise\\xf1o y fabricaci\\xf3n de equipos de protecci\\xf3n contra inundaciones, proporcionando productos de alta calidad y soluciones integrales para usuarios globales.\",\n            keywords: [\n                \"protecci\\xf3n contra inundaciones\",\n                \"barreras contra inundaciones\",\n                \"control de inundaciones\",\n                \"protecci\\xf3n de emergencia\",\n                \"protecci\\xf3n industrial\",\n                \"Tucsenberg\"\n            ]\n        }\n    };\n    const localizedContent = metadataByLocale[locale];\n    return {\n        ...localizedContent,\n        authors: [\n            {\n                name: \"Tucsenberg\",\n                url: \"https://tucsenberg.com\"\n            }\n        ],\n        creator: \"Tucsenberg\",\n        publisher: \"Tucsenberg\",\n        formatDetection: {\n            email: false,\n            address: false,\n            telephone: false\n        },\n        metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\"),\n        alternates: {\n            canonical: \"/\",\n            languages: {\n                \"en\": \"/en\",\n                \"zh-CN\": \"/zh-CN\",\n                \"ja\": \"/ja\",\n                \"es\": \"/es\"\n            }\n        },\n        openGraph: {\n            type: \"website\",\n            locale: locale === \"zh-CN\" ? \"zh_CN\" : locale === \"ja\" ? \"ja_JP\" : locale === \"es\" ? \"es_ES\" : \"en_US\",\n            url: \"/\",\n            title: localizedContent.title.default,\n            description: localizedContent.description,\n            siteName: \"Tucsenberg\",\n            images: [\n                {\n                    url: \"/images/og-image.jpg\",\n                    width: 1200,\n                    height: 630,\n                    alt: locale === \"zh-CN\" ? \"Tucsenberg 防洪设备\" : locale === \"ja\" ? \"Tucsenberg 洪水防護設備\" : locale === \"es\" ? \"Tucsenberg Equipos de Protecci\\xf3n contra Inundaciones\" : \"Tucsenberg Flood Protection Equipment\"\n                }\n            ]\n        },\n        twitter: {\n            card: \"summary_large_image\",\n            title: localizedContent.title.default,\n            description: localizedContent.description,\n            images: [\n                \"/images/og-image.jpg\"\n            ]\n        },\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                \"max-video-preview\": -1,\n                \"max-image-preview\": \"large\",\n                \"max-snippet\": -1\n            }\n        },\n        verification: {\n            google: \"your-google-verification-code\",\n            yandex: \"your-yandex-verification-code\",\n            yahoo: \"your-yahoo-verification-code\"\n        }\n    };\n}\n// 默认导出metadata（用于根布局）\nconst metadata = generateMetadata(\"zh-CN\");\n// 获取当前语言的函数\nfunction getCurrentLocale() {\n    // 在服务端，我们需要从URL或其他方式获取语言\n    // 这里先返回默认语言，实际的语言检测由中间件处理\n    return \"zh-CN\";\n}\nfunction RootLayout({ children, params }) {\n    // 从URL参数或其他方式获取当前语言\n    const currentLocale = params?.locale || getCurrentLocale();\n    const localeConfig = SUPPORTED_LOCALES[currentLocale] || SUPPORTED_LOCALES[\"zh-CN\"];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: currentLocale,\n        dir: localeConfig.dir,\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#2563eb\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#2563eb\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    Object.entries(SUPPORTED_LOCALES).map(([locale, config])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"alternate\",\n                            hrefLang: locale === \"zh-CN\" ? \"zh-Hans\" : locale,\n                            href: `${process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\"}/${locale}`\n                        }, locale, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"x-default\",\n                        href: `${process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\"}/zh-CN`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"root\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n// 导出语言配置供其他组件使用\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/zh-CN/page.tsx":
/*!********************************!*\
  !*** ./src/app/zh-CN/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ZhCNPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction ZhCNPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold mb-4\",\n                    children: \"欢迎来到中文页面！\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl text-gray-600\",\n                    children: \"当前语言: 中文 (zh-CN)\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/zh-CN\",\n                            className: \"text-blue-600 hover:underline\",\n                            children: \"中文\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/en\",\n                            className: \"text-blue-600 hover:underline\",\n                            children: \"English\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/ja\",\n                            className: \"text-blue-600 hover:underline\",\n                            children: \"日本語\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/es\",\n                            className: \"text-blue-600 hover:underline\",\n                            children: \"Espa\\xf1ol\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3poLUNOL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFHRCxXQUFVOzhCQUEwQjs7Ozs7OzhCQUd4Qyw4REFBQ0U7b0JBQUVGLFdBQVU7OEJBQXdCOzs7Ozs7OEJBR3JDLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNHOzRCQUFFQyxNQUFLOzRCQUFTSixXQUFVO3NDQUFnQzs7Ozs7O3NDQUMzRCw4REFBQ0c7NEJBQUVDLE1BQUs7NEJBQU1KLFdBQVU7c0NBQWdDOzs7Ozs7c0NBQ3hELDhEQUFDRzs0QkFBRUMsTUFBSzs0QkFBTUosV0FBVTtzQ0FBZ0M7Ozs7OztzQ0FDeEQsOERBQUNHOzRCQUFFQyxNQUFLOzRCQUFNSixXQUFVO3NDQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLbEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90dWNzZW5iZXJnLXdlYnNpdGUvLi9zcmMvYXBwL3poLUNOL3BhZ2UudHN4PzRjY2UiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gWmhDTlBhZ2UoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIGZvbnQtYm9sZCBtYi00XCI+XG4gICAgICAgICAg5qyi6L+O5p2l5Yiw5Lit5paH6aG16Z2i77yBXG4gICAgICAgIDwvaDE+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgIOW9k+WJjeivreiogDog5Lit5paHICh6aC1DTilcbiAgICAgICAgPC9wPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTggc3BhY2UteC00XCI+XG4gICAgICAgICAgPGEgaHJlZj1cIi96aC1DTlwiIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dW5kZXJsaW5lXCI+5Lit5paHPC9hPlxuICAgICAgICAgIDxhIGhyZWY9XCIvZW5cIiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnVuZGVybGluZVwiPkVuZ2xpc2g8L2E+XG4gICAgICAgICAgPGEgaHJlZj1cIi9qYVwiIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dW5kZXJsaW5lXCI+5pel5pys6KqePC9hPlxuICAgICAgICAgIDxhIGhyZWY9XCIvZXNcIiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnVuZGVybGluZVwiPkVzcGHDsW9sPC9hPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiWmhDTlBhZ2UiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiLCJhIiwiaHJlZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/zh-CN/page.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1","vendor-chunks/@opentelemetry+api@1.9.0","vendor-chunks/@swc+helpers@0.5.5"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fzh-CN%2Fpage&page=%2Fzh-CN%2Fpage&appPaths=%2Fzh-CN%2Fpage&pagePath=private-next-app-dir%2Fzh-CN%2Fpage.tsx&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();