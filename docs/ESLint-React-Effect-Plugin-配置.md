# ESLint React You Might Not Need An Effect 插件配置

## 概述

本文档记录了 `eslint-plugin-react-you-might-not-need-an-effect` 插件的安装和配置过程。该插件用于检测不必要的 React `useEffect` 使用，帮助开发者编写更高效、更易维护的 React 代码。

## 安装步骤

### 1. 安装插件

```bash
pnpm add -D eslint-plugin-react-you-might-not-need-an-effect
```

### 2. 配置 ESLint

创建/更新 `.eslintrc.cjs` 文件：

```javascript
module.exports = {
  extends: [
    'next/core-web-vitals',
    'plugin:react-you-might-not-need-an-effect/legacy-recommended'
  ],
  rules: {
    // 仅警告，不阻塞构建
    'react-you-might-not-need-an-effect/you-might-not-need-an-effect': 'warn'
  },
  overrides: [
    {
      files: ['src/components/ui/LanguageSwitcher.tsx'],
      rules: {
        // 这个文件中的 useEffect 用于事件监听器，不是状态初始化
        'react-you-might-not-need-an-effect/you-might-not-need-an-effect': 'off'
      }
    }
  ]
};
```

### 3. 更新 package.json 脚本

```json
{
  "scripts": {
    "lint": "next lint",
    "lint:custom": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix"
  }
}
```

### 4. 运行 Lint

```bash
# 使用自定义配置运行 lint
pnpm lint:custom

# 或者直接使用 eslint
pnpm eslint "**/*.{js,jsx,ts,tsx}" --fix
```

## GitHub Actions 集成

创建 `.github/workflows/ci.yml` 文件，包含 lint 检查：

```yaml
- name: Lint
  run: pnpm eslint "**/*.{js,jsx,ts,tsx}" --max-warnings=0
```

## 修复的问题

### 1. React Hooks 使用错误

**问题**: 在 `src/lib/i18n.ts` 中，`useRouter` Hook 被在类的静态方法中调用。

**修复**: 
- 移除了类静态方法中的 Hook 调用
- 添加了警告注释，指导开发者使用 `useI18n` Hook

### 2. 插件配置问题

**问题**: 初始配置使用了错误的规则名称和配置方式。

**修复**:
- 使用正确的规则名称: `you-might-not-need-an-effect`
- 使用 `legacy-recommended` 配置
- 添加了特定文件的规则覆盖

## 插件功能

该插件检测以下不必要的 useEffect 使用模式：

1. **存储派生状态**: 当状态可以直接从 props 或其他状态计算得出时
2. **链式状态更新**: 当一个状态更新触发另一个状态更新时
3. **初始化状态**: 当 useEffect 仅用于设置初始状态时
4. **props 变化时重置所有状态**: 当可以使用 key 属性重置组件时
5. **耦合父子组件状态或行为**: 当可以通过提升状态或使用回调来解决时

## 使用建议

1. **仅警告模式**: 配置为 `warn` 而不是 `error`，避免阻塞构建
2. **特定文件覆盖**: 对于合理使用 useEffect 的文件（如事件监听器），可以使用 `overrides` 配置
3. **结合其他规则**: 建议与 `react-hooks/exhaustive-deps` 规则一起使用，获得更好的效果

## 注意事项

1. **TypeScript 版本警告**: 当前 TypeScript 版本 (5.8.3) 超出了官方支持范围，但不影响功能
2. **误报处理**: 对于合理的 useEffect 使用（如事件监听器），可以通过配置覆盖来避免误报
3. **持续监控**: 定期检查插件更新，获取最新的检测规则和修复

## 相关资源

- [React 官方文档 - You Might Not Need an Effect](https://react.dev/learn/you-might-not-need-an-effect)
- [插件 GitHub 仓库](https://github.com/NickvanDyke/eslint-plugin-react-you-might-not-need-an-effect)
- [ESLint 配置指南](https://eslint.org/docs/user-guide/configuring/)
