{"version": 2, "name": "tucsenberg-website", "alias": ["tucsenberg.com", "www.tucsenberg.com"], "regions": ["hkg1", "nrt1", "sfo1"], "build": {"env": {"NODE_ENV": "production"}}, "buildCommand": "pnpm build", "devCommand": "pnpm dev", "installCommand": "pnpm install --frozen-lockfile", "framework": "nextjs", "outputDirectory": ".next", "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}], "env": {"NEXT_PUBLIC_SITE_URL": "@site-url", "NEXT_PUBLIC_SITE_NAME": "@site-name", "RESEND_API_KEY": "@resend-api-key", "LINGO_API_KEY": "@lingo-api-key"}, "github": {"enabled": true, "autoAlias": true, "autoJobCancelation": true}}